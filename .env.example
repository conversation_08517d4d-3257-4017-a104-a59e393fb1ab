# Zeka Go Discord Bot 环境变量配置

# Discord 配置
DISCORD_TOKEN=your_discord_bot_token_here
DISCORD_CLIENT_ID=your_discord_client_id_here
DISCORD_GUILD_ID=your_development_guild_id_here
DISCORD_OWNER_ID=your_discord_user_id_here

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# RabbitMQ 配置
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_VHOST=/
RABBITMQ_HEARTBEAT=60
RABBITMQ_RECONNECT_DELAY=5000
RABBITMQ_MAX_RECONNECT_ATTEMPTS=10
RABBITMQ_CONNECTION_TIMEOUT=10000

# 日志配置
LOG_LEVEL=info
LOG_TO_DISCORD=false
LOG_CHANNEL_ID=

# 应用配置
ENVIRONMENT=development
