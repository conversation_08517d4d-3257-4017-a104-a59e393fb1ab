# Zeka Go Discord Bot Makefile

# 变量定义
BINARY_NAME=zeka
MAIN_PATH=./cmd/zeka
BUILD_DIR=./build
DOCKER_IMAGE=zeka-go
DOCKER_TAG=latest

# Go 相关变量
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
GOFMT=gofmt
GOLINT=golangci-lint

# 构建信息
VERSION ?= $(shell git describe --tags --always --dirty)
COMMIT ?= $(shell git rev-parse --short HEAD)
BUILD_TIME ?= $(shell date -u '+%Y-%m-%d_%H:%M:%S')

# 构建标志
LDFLAGS=-ldflags "-X main.Version=$(VERSION) -X main.Commit=$(COMMIT) -X main.BuildTime=$(BUILD_TIME)"

.PHONY: all build clean test coverage lint fmt vet deps tidy run dev docker help

# 默认目标
all: clean deps lint test build

# 构建二进制文件
build:
	@echo "构建 $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)

# 清理构建文件
clean:
	@echo "清理构建文件..."
	$(GOCLEAN)
	@rm -rf $(BUILD_DIR)

# 运行测试
test:
	@echo "运行测试..."
	$(GOTEST) -v ./...

# 运行测试并生成覆盖率报告
coverage:
	@echo "生成测试覆盖率报告..."
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html

# 代码检查
lint:
	@echo "运行代码检查..."
	$(GOLINT) run

# 格式化代码
fmt:
	@echo "格式化代码..."
	$(GOFMT) -s -w .

# 代码静态分析
vet:
	@echo "运行代码静态分析..."
	$(GOCMD) vet ./...

# 下载依赖
deps:
	@echo "下载依赖..."
	$(GOGET) -d ./...

# 整理模块
tidy:
	@echo "整理 Go 模块..."
	$(GOMOD) tidy

# 运行应用
run: build
	@echo "运行 $(BINARY_NAME)..."
	$(BUILD_DIR)/$(BINARY_NAME)

# 开发模式运行（使用 air 热重载）
dev:
	@echo "启动开发模式..."
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "请先安装 air: go install github.com/cosmtrek/air@latest"; \
		exit 1; \
	fi

# 构建 Docker 镜像
docker:
	@echo "构建 Docker 镜像..."
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .

# 运行 Docker 容器
docker-run:
	@echo "运行 Docker 容器..."
	docker run --rm -it \
		--env-file .env \
		-v $(PWD)/configs:/app/configs \
		-v $(PWD)/templates:/app/templates \
		-v $(PWD)/logs:/app/logs \
		$(DOCKER_IMAGE):$(DOCKER_TAG)

# 安装开发工具
install-tools:
	@echo "安装开发工具..."
	$(GOGET) -u github.com/cosmtrek/air@latest
	$(GOGET) -u github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# 检查代码质量
quality: fmt vet lint test

# 完整的 CI 流程
ci: deps tidy quality build

# 显示帮助信息
help:
	@echo "可用的 make 目标："
	@echo "  all          - 执行完整的构建流程"
	@echo "  build        - 构建二进制文件"
	@echo "  clean        - 清理构建文件"
	@echo "  test         - 运行测试"
	@echo "  coverage     - 生成测试覆盖率报告"
	@echo "  lint         - 运行代码检查"
	@echo "  fmt          - 格式化代码"
	@echo "  vet          - 运行代码静态分析"
	@echo "  deps         - 下载依赖"
	@echo "  tidy         - 整理 Go 模块"
	@echo "  run          - 运行应用"
	@echo "  dev          - 开发模式运行（热重载）"
	@echo "  docker       - 构建 Docker 镜像"
	@echo "  docker-run   - 运行 Docker 容器"
	@echo "  install-tools- 安装开发工具"
	@echo "  quality      - 检查代码质量"
	@echo "  ci           - 完整的 CI 流程"
	@echo "  help         - 显示此帮助信息"
