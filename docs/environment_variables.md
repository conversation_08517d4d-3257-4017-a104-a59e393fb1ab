# 环境变量配置指南

## 概述

Zeka Discord Bot 支持通过环境变量进行配置，这些变量可以覆盖配置文件中的默认值。环境变量特别适用于容器化部署和敏感信息管理。

## Discord 配置

### 必需变量

| 变量名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `DISCORD_TOKEN` | string | Discord Bot Token | `MTIzNDU2Nzg5MDEyMzQ1Njc4OQ.GhIjKl.MnOpQrStUvWxYzAbCdEfGhIjKlMnOpQrStUv` |
| `DISCORD_CLIENT_ID` | string | Discord 应用客户端ID | `1234567890123456789` |

### 可选变量

| 变量名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `DISCORD_GUILD_ID` | string | - | 开发时的测试服务器ID |
| `DISCORD_OWNER_ID` | string | - | Bot 所有者的用户ID |



## Redis 配置

| 变量名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `REDIS_ENABLED` | bool | `true` | 是否启用Redis |
| `REDIS_HOST` | string | `localhost` | Redis 主机地址 |
| `REDIS_PORT` | int | `6379` | Redis 端口 |
| `REDIS_PASSWORD` | string | - | Redis 密码 |
| `REDIS_DB` | int | `0` | Redis 数据库编号 |
| `REDIS_MAX_RETRIES` | int | `3` | 最大重试次数 |
| `REDIS_POOL_SIZE` | int | `10` | 连接池大小 |

## RabbitMQ 配置

| 变量名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `RABBITMQ_URL` | string | `amqp://127.0.0.1:5672` | RabbitMQ 连接URL（强制IPv4） |
| `QUEUE_PREFETCH_COUNT` | int | `10` | 预取消息数量 |
| `QUEUE_MAX_RETRIES` | int | `3` | 最大重试次数 |
| `QUEUE_HEARTBEAT_INTERVAL` | duration | `60s` | 心跳间隔 |
| `QUEUE_RECONNECT_DELAY` | duration | `5s` | 重连延迟 |
| `QUEUE_CONNECTION_TIMEOUT` | duration | `10s` | 连接超时 |
| `QUEUE_ENABLE_GRACEFUL_DEGRADATION` | bool | `true` | 启用优雅降级 |
| `QUEUE_REQUIRED_FOR_STARTUP` | bool | `false` | 队列服务是否为启动必需 |

### RabbitMQ URL 格式

```
amqp://[username:password@]host[:port][/vhost]
```

示例：
- `amqp://127.0.0.1:5672` - 本地连接（IPv4）
- `amqp://user:pass@127.0.0.1:5672` - 带认证的本地连接
- `amqp://user:pass@rabbitmq:5672/` - Docker环境连接
- `amqp://user:<EMAIL>:5672/vhost` - 生产环境连接

### 优雅降级模式

当 `QUEUE_ENABLE_GRACEFUL_DEGRADATION=true` 时，Bot 在 RabbitMQ 不可用的情况下仍能启动：

- ✅ **基本功能正常**：命令处理、事件响应等核心功能不受影响
- ⚠️ **队列功能降级**：延迟消息转发、库存通知等依赖队列的功能将不可用
- 🔄 **自动重连**：后台持续尝试重连，服务恢复后自动启用队列功能
- 📊 **状态监控**：通过健康检查API可以监控队列服务状态

**推荐配置**：
```bash
QUEUE_ENABLE_GRACEFUL_DEGRADATION=true
QUEUE_REQUIRED_FOR_STARTUP=false
```

## 日志配置

| 变量名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `LOG_LEVEL` | string | `info` | 全局日志级别 |
| `LOG_TO_DISCORD` | bool | `false` | 是否发送日志到Discord |
| `LOG_CHANNEL_ID` | string | - | 日志频道ID |
| `LOG_FORMAT` | string | `json` | 日志格式 (json/text) |

### 日志级别

- `debug` - 调试信息（最详细）
- `info` - 一般信息
- `warn` - 警告信息
- `error` - 错误信息
- `fatal` - 致命错误（最严重）

## API 配置

| 变量名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `API_ENABLED` | bool | `true` | 是否启用API服务 |
| `API_HOST` | string | `0.0.0.0` | API 监听地址 |
| `API_PORT` | int | `8080` | API 端口 |
| `API_TIMEOUT` | duration | `30s` | API 超时时间 |

## 调度器配置

| 变量名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `SCHEDULER_ENABLED` | bool | `true` | 是否启用调度器 |
| `SCHEDULER_TIMEZONE` | string | `UTC` | 时区设置 |

## 环境配置

| 变量名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `ENVIRONMENT` | string | `development` | 运行环境 |
| `CONFIG_PATH` | string | `configs` | 配置文件路径 |

### 环境类型

- `development` - 开发环境
- `staging` - 测试环境
- `production` - 生产环境

## 配置示例

### 开发环境 (.env.development)

```bash
# Discord 配置
DISCORD_TOKEN=your_development_token_here
DISCORD_CLIENT_ID=your_client_id_here
DISCORD_GUILD_ID=your_test_guild_id_here

# 频道监听
CHANNEL_MONITOR_ENABLED=true
CHANNEL_MONITOR_DEFAULT_DELAY=1m
CHANNEL_MONITOR_LOG_LEVEL=debug

# 服务配置
REDIS_HOST=localhost
REDIS_PORT=6379
RABBITMQ_URL=amqp://localhost:5672

# 日志配置
LOG_LEVEL=debug
LOG_FORMAT=text
```

### 生产环境 (.env.production)

```bash
# Discord 配置
DISCORD_TOKEN=${DISCORD_TOKEN}
DISCORD_CLIENT_ID=${DISCORD_CLIENT_ID}

# 频道监听
CHANNEL_MONITOR_ENABLED=true
CHANNEL_MONITOR_DEFAULT_DELAY=5m
CHANNEL_MONITOR_LOG_LEVEL=info

# 服务配置
REDIS_HOST=redis.example.com
REDIS_PORT=6379
REDIS_PASSWORD=${REDIS_PASSWORD}
RABBITMQ_URL=amqp://${RABBITMQ_USER}:${RABBITMQ_PASS}@rabbitmq.example.com:5672

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
LOG_TO_DISCORD=true
LOG_CHANNEL_ID=${LOG_CHANNEL_ID}

# 环境设置
ENVIRONMENT=production
```

### Docker Compose 环境

```yaml
version: '3.8'
services:
  zeka:
    image: zeka-go:latest
    environment:
      - DISCORD_TOKEN=${DISCORD_TOKEN}
      - DISCORD_CLIENT_ID=${DISCORD_CLIENT_ID}
      - CHANNEL_MONITOR_ENABLED=true
      - REDIS_HOST=redis
      - RABBITMQ_URL=amqp://rabbitmq:5672
      - LOG_LEVEL=info
    depends_on:
      - redis
      - rabbitmq
```

## 配置优先级

配置的优先级从高到低：

1. **环境变量** - 最高优先级
2. **配置文件** - 中等优先级
3. **默认值** - 最低优先级

## 安全最佳实践

### 敏感信息管理

1. **使用环境变量存储敏感信息**
   ```bash
   DISCORD_TOKEN=your_secret_token
   REDIS_PASSWORD=your_redis_password
   ```

2. **避免在代码中硬编码**
   ```go
   // ❌ 错误做法
   token := "MTIzNDU2Nzg5..."
   
   // ✅ 正确做法
   token := os.Getenv("DISCORD_TOKEN")
   ```

3. **使用 .env 文件管理本地配置**
   ```bash
   # 添加到 .gitignore
   .env
   .env.local
   .env.production
   ```

### 生产环境建议

1. **使用密钥管理服务**
   - AWS Secrets Manager
   - Azure Key Vault
   - HashiCorp Vault

2. **限制环境变量访问**
   - 使用最小权限原则
   - 定期轮换密钥

3. **监控配置变更**
   - 记录配置变更日志
   - 设置变更告警

## 故障排除

### 常见问题

1. **环境变量未生效**
   - 检查变量名拼写
   - 确认变量已正确设置
   - 重启应用程序

2. **配置验证失败**
   - 检查数据类型是否正确
   - 验证必需变量是否设置
   - 查看启动日志

3. **连接失败**
   - 验证主机地址和端口
   - 检查网络连接
   - 确认服务状态

### 调试命令

```bash
# 查看所有环境变量
env | grep -E "(DISCORD|REDIS|RABBITMQ|LOG|CHANNEL_MONITOR)"

# 测试配置加载
go run test_config.go

# 验证连接
go run test_connections.go
```
