# Zeka Discord Bot 部署指南

## 概述

本文档提供了Zeka Discord Bot的完整部署指南，包括环境准备、配置设置、部署方式和运维管理。

## 系统要求

### 最低要求
- **操作系统**: Linux (Ubuntu 20.04+), macOS, Windows
- **Go版本**: 1.19+
- **内存**: 512MB RAM
- **存储**: 1GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐配置
- **操作系统**: Linux (Ubuntu 22.04 LTS)
- **Go版本**: 1.21+
- **内存**: 2GB RAM
- **存储**: 5GB 可用空间
- **CPU**: 2核心

## 依赖服务

### 必需服务
1. **Discord Application**
   - 创建Discord应用程序
   - 获取Bot Token和Client ID
   - 配置必要的权限

### 可选服务
1. **Redis** (推荐)
   - 版本: 6.0+
   - 用于缓存和会话存储

2. **RabbitMQ** (推荐)
   - 版本: 3.8+
   - 用于异步任务处理

## 环境准备

### 1. 安装Go

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install golang-go

# macOS (使用Homebrew)
brew install go

# 验证安装
go version
```

### 2. 安装Redis (可选)

```bash
# Ubuntu/Debian
sudo apt install redis-server

# macOS
brew install redis

# 启动Redis
sudo systemctl start redis-server
# 或
redis-server
```

### 3. 安装RabbitMQ (可选)

```bash
# Ubuntu/Debian
sudo apt install rabbitmq-server

# macOS
brew install rabbitmq

# 启动RabbitMQ
sudo systemctl start rabbitmq-server
# 或
brew services start rabbitmq
```

## 项目部署

### 1. 获取源码

```bash
git clone https://github.com/your-org/zeka-go.git
cd zeka-go
```

### 2. 安装依赖

```bash
go mod download
go mod tidy
```

### 3. 构建项目

```bash
# 开发构建
go build -o zeka-bot ./cmd/bot

# 生产构建 (优化)
CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags '-w -s' -o zeka-bot ./cmd/bot
```

## 配置设置

### 1. 创建配置文件

```bash
cp config/config.example.yaml config/config.yaml
```

### 2. 基础配置

```yaml
# config/config.yaml
environment: "production"

discord:
  token: "YOUR_BOT_TOKEN"
  client_id: "YOUR_CLIENT_ID"
  guild_id: "YOUR_GUILD_ID"  # 可选，用于开发测试

logger:
  level: "info"
  file: "logs/bot.log"
  max_size: 100
  max_backups: 3
  max_age: 28

# Redis配置 (可选)
redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  prefix: "zeka:"

# RabbitMQ配置 (可选)
queue:
  url: "amqp://guest:guest@localhost:5672/"
  prefetch_count: 10
  max_retries: 3
  heartbeat_interval: "30s"
  reconnect_delay: "5s"
  connection_timeout: "30s"
```

### 3. 环境变量配置

```bash
# 创建环境变量文件
cat > .env << EOF
DISCORD_TOKEN=your_bot_token_here
DISCORD_CLIENT_ID=your_client_id_here
DISCORD_GUILD_ID=your_guild_id_here
REDIS_HOST=localhost
REDIS_PORT=6379
RABBITMQ_URL=amqp://guest:guest@localhost:5672/
LOG_LEVEL=info
EOF
```

## 部署方式

### 方式1: 直接运行

```bash
# 使用配置文件
./zeka-bot -config config/config.yaml

# 使用环境变量
source .env
./zeka-bot
```

### 方式2: 系统服务

创建systemd服务文件:

```bash
sudo tee /etc/systemd/system/zeka-bot.service > /dev/null << EOF
[Unit]
Description=Zeka Discord Bot
After=network.target

[Service]
Type=simple
User=zeka
WorkingDirectory=/opt/zeka-bot
ExecStart=/opt/zeka-bot/zeka-bot -config /opt/zeka-bot/config/config.yaml
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable zeka-bot
sudo systemctl start zeka-bot
```

### 方式3: Docker部署

#### 创建Dockerfile

```dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags '-w -s' -o zeka-bot ./cmd/bot

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/zeka-bot .
COPY --from=builder /app/config ./config

EXPOSE 8080
CMD ["./zeka-bot"]
```

#### 构建和运行

```bash
# 构建镜像
docker build -t zeka-bot:latest .

# 运行容器
docker run -d \
  --name zeka-bot \
  --restart unless-stopped \
  -e DISCORD_TOKEN=your_token \
  -e DISCORD_CLIENT_ID=your_client_id \
  -v $(pwd)/config:/root/config \
  -v $(pwd)/logs:/root/logs \
  zeka-bot:latest
```

### 方式4: Docker Compose

创建docker-compose.yml:

```yaml
version: '3.8'

services:
  zeka-bot:
    build: .
    container_name: zeka-bot
    restart: unless-stopped
    environment:
      - DISCORD_TOKEN=${DISCORD_TOKEN}
      - DISCORD_CLIENT_ID=${DISCORD_CLIENT_ID}
      - REDIS_HOST=redis
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672/
    volumes:
      - ./config:/root/config
      - ./logs:/root/logs
    depends_on:
      - redis
      - rabbitmq

  redis:
    image: redis:7-alpine
    container_name: zeka-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data

  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: zeka-rabbitmq
    restart: unless-stopped
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    ports:
      - "15672:15672"  # 管理界面

volumes:
  redis_data:
  rabbitmq_data:
```

运行:

```bash
docker-compose up -d
```

## 配置验证

### 1. 检查配置

```bash
# 验证配置文件
./zeka-bot -config config/config.yaml -validate

# 检查Discord连接
./zeka-bot -config config/config.yaml -test-discord

# 检查服务连接
./zeka-bot -config config/config.yaml -test-services
```

### 2. 健康检查

```bash
# 检查Bot状态
curl http://localhost:8080/health

# 检查服务状态
curl http://localhost:8080/health/services

# 检查详细信息
curl http://localhost:8080/health/detailed
```

## 监控和日志

### 1. 日志管理

```bash
# 查看实时日志
tail -f logs/bot.log

# 查看错误日志
grep "ERROR" logs/bot.log

# 日志轮转配置
# 日志文件会自动轮转，保留最近3个备份
```

### 2. 系统监控

```bash
# 查看服务状态
sudo systemctl status zeka-bot

# 查看资源使用
top -p $(pgrep zeka-bot)

# 查看网络连接
netstat -tulpn | grep zeka-bot
```

### 3. Docker监控

```bash
# 查看容器状态
docker ps

# 查看容器日志
docker logs zeka-bot

# 查看资源使用
docker stats zeka-bot
```

## 故障排除

### 常见问题

1. **Bot无法启动**
   ```bash
   # 检查配置文件
   ./zeka-bot -config config/config.yaml -validate
   
   # 检查权限
   ls -la zeka-bot
   chmod +x zeka-bot
   ```

2. **Discord连接失败**
   ```bash
   # 验证Token
   curl -H "Authorization: Bot YOUR_TOKEN" https://discord.com/api/v10/users/@me
   
   # 检查网络连接
   ping discord.com
   ```

3. **Redis连接失败**
   ```bash
   # 检查Redis状态
   redis-cli ping
   
   # 检查连接配置
   redis-cli -h localhost -p 6379
   ```

4. **RabbitMQ连接失败**
   ```bash
   # 检查RabbitMQ状态
   sudo systemctl status rabbitmq-server
   
   # 检查连接
   curl http://localhost:15672
   ```

### 日志分析

```bash
# 查看启动日志
grep "Bot 启动" logs/bot.log

# 查看错误日志
grep "ERROR\|FATAL" logs/bot.log

# 查看性能日志
grep "性能\|内存\|CPU" logs/bot.log
```

## 性能优化

### 1. 系统优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
sysctl -p
```

### 2. 应用优化

```yaml
# config/config.yaml
# 调整连接池大小
redis:
  pool_size: 20
  min_idle_conns: 5

queue:
  prefetch_count: 50
  max_retries: 5
```

## 安全配置

### 1. 文件权限

```bash
# 设置适当的文件权限
chmod 600 config/config.yaml
chmod 600 .env
chmod 755 zeka-bot
```

### 2. 网络安全

```bash
# 配置防火墙 (如果需要)
sudo ufw allow 8080/tcp  # 健康检查端口
sudo ufw enable
```

### 3. 密钥管理

```bash
# 使用环境变量而不是配置文件存储敏感信息
export DISCORD_TOKEN="your_secret_token"
export REDIS_PASSWORD="your_redis_password"
```

## 备份和恢复

### 1. 配置备份

```bash
# 备份配置
tar -czf backup-$(date +%Y%m%d).tar.gz config/ logs/

# 恢复配置
tar -xzf backup-20231201.tar.gz
```

### 2. 数据备份

```bash
# Redis数据备份
redis-cli BGSAVE

# RabbitMQ配置备份
sudo rabbitmqctl export_definitions backup.json
```

## 更新和维护

### 1. 更新流程

```bash
# 1. 备份当前版本
cp zeka-bot zeka-bot.backup

# 2. 停止服务
sudo systemctl stop zeka-bot

# 3. 更新代码
git pull origin main
go build -o zeka-bot ./cmd/bot

# 4. 启动服务
sudo systemctl start zeka-bot

# 5. 验证更新
sudo systemctl status zeka-bot
```

### 2. 回滚流程

```bash
# 如果更新失败，回滚到备份版本
sudo systemctl stop zeka-bot
cp zeka-bot.backup zeka-bot
sudo systemctl start zeka-bot
```

---

*本部署指南涵盖了Zeka Discord Bot的主要部署场景。如遇到特殊问题，请参考项目文档或联系技术支持。*
