# 频道监听系统统一增强设计

## 概述

基于现有系统深入分析后的**统一完整设计方案**，整合转发规则、映射组集成、产品信息模型扩展和过滤系统的所有功能。

## 现有系统分析

### 当前架构优势
1. **完整的Embed处理**：`forward_rules.go`已实现Embed字段提取
2. **消息转发任务**：`MessageForwardTask`支持Embeds数据序列化
3. **事件驱动架构**：基于Discord事件，无需额外轮询
4. **配置系统**：完整的`ChannelMapping`配置结构

### 现有Embed处理机制
```go
// 当前系统已支持的Embed字段提取
- embed.Title -> "[嵌入标题: {title}]"
- embed.Description -> "[嵌入描述: {description}]"
- embed.URL -> "[嵌入链接: {url}]"
- embed.Fields -> "[{name}: {value}]"
```

## 统一设计方案

### 1. 转发功能设计

#### 核心设计原则
- **严格1对1转发**：每个转发规则只能有一个输入频道和一个输出频道
- **自动命名规则**：格式为 `{inputChannelName}_to_{outputChannelName}`
- **映射组必需**：每个转发规则必须关联一个字段映射组
- **多规则实现1对多**：通过创建多个规则实现复杂转发效果
- **默认延迟0秒**：简化配置，默认立即转发
- **删除重建模式**：不提供编辑功能，采用删除重建方式
- **独立规则管理**：转发规则和过滤规则独立管理，删除转发规则不影响过滤规则

#### 转发规则结构
```go
type ForwardRule struct {
    // 基础信息
    Name          string        `json:"name"`           // 自动生成
    InputChannel  string        `json:"input_channel"`  // 单个输入频道
    OutputChannel string        `json:"output_channel"` // 单个输出频道
    Delay         time.Duration `json:"delay"`          // 默认0秒
    Enabled       bool          `json:"enabled"`        // 默认true

    // 映射组集成（必需）
    FieldMappingGroup string `json:"field_mapping_group"` // 必需字段

    // 频道名称缓存（用于命名和查询）
    InputChannelName  string `json:"input_channel_name,omitempty"`
    OutputChannelName string `json:"output_channel_name,omitempty"`

    // 时间戳
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
    CreatedBy string    `json:"created_by"`
}
```

#### 配置示例
```yaml
# 统一配置格式
forward_rules:
  enabled: true

  # 字段映射组文件引用
  field_mapping_groups_file: "configs/field_mapping_groups.yaml"

  # 转发规则（严格1对1模式）
  forward_rules:
    - input_channel: "1380203642526634064"
      output_channel: "1356648190023307304"
      field_mapping_group: "amazon_standard"
      # name 自动生成为: amazon-it_to_deals
      # delay 默认为: 0s
      # enabled 默认为: true

    - input_channel: "1380203642526634064"  # 同一输入
      output_channel: "1356648190023307305"  # 不同输出
      field_mapping_group: "amazon_standard"
      # name 自动生成为: amazon-it_to_notifications
```

#### 1对多转发实现示例
```yaml
# 实现 source → target1, target2, target3
forward_rules:
  - input_channel: "source_channel_id"
    output_channel: "target1_channel_id"
    field_mapping_group: "amazon_standard"

  - input_channel: "source_channel_id"
    output_channel: "target2_channel_id"
    field_mapping_group: "amazon_standard"

  - input_channel: "source_channel_id"
    output_channel: "target3_channel_id"
    field_mapping_group: "amazon_standard"
```

### 2. 产品信息模型设计

#### 核心设计原则
- **合并Price字段**：Price字段包含货币信息（如 $19.99, €15.50）
- **Discord Embed字段支持**：所有Embed字段都设为可选，支持从现有`message.Embeds`提取
- **向后兼容**：保持与现有ProductItem的兼容性
- **可扩展性**：支持动态Embed字段映射

#### 统一产品信息模型
```go
type ProductItem struct {
    // 核心产品信息
    Title     string `json:"title"`
    URL       string `json:"url"`
    Description *string `json:"description,omitempty"` // embed.Description
    Color       *int    `json:"color,omitempty"`       // embed.Color

    ProductID string `json:"productId"`
    Price     string `json:"price"` // 合并价格和货币：$19.99, €15.50, ¥1200

    // Embed图片信息
    ImageURL     *string `json:"image_url,omitempty"`     // embed.Image.URL
    ThumbnailURL *string `json:"thumbnail_url,omitempty"` // embed.Thumbnail.URL

    // Embed页脚和作者信息
    FooterText    *string `json:"footer_text,omitempty"`     // embed.Footer.Text
    AuthorName    *string `json:"author_name,omitempty"`     // embed.Author.Name
    AuthorURL     *string `json:"author_url,omitempty"`      // embed.Author.URL

    // 库存和可用性
    Stock        int    `json:"stock"`
    Availability string `json:"availability"`

    // 可选产品信息
    SkuID       *string `json:"skuId,omitempty"`
    OfferID     *string `json:"offerId,omitempty"`
    Addition    *string `json:"addition,omitempty"`
    ReleaseDate *string `json:"releaseDate,omitempty"`
    AtcLink     *string `json:"atcLink,omitempty"`

    // 元数据
    ChannelId   string               `json:"ChannelId"`
    Metadata      map[string]interface{} `json:"metadata"`

    // 原始Discord数据（用于调试和回溯）
    OriginalMessageID string                   `json:"original_message_id,omitempty"`
    OriginalChannelID string                   `json:"original_channel_id,omitempty"`
    OriginalEmbeds    []map[string]interface{} `json:"original_embeds,omitempty"`

    // 时间戳
    Timestamp   *string `json:"timestamp,omitempty"`   // embed.Timestamp

}
```

#### 与现有系统集成
基于现有的`forward_rules.go`中的Embed处理机制，产品信息模型将直接利用：

```go
// 现有系统已支持的提取逻辑
- embed.Title → Title
- embed.Description → Description
- embed.URL → URL
- embed.Fields[i].Name/Value → Fields[name] = value
```

### 3. 过滤系统设计

#### 核心设计原则
- **基于频道ID过滤**：FilterRule基于Channel ID进行匹配和应用
- **队列前过滤**：消息进入队列前，根据源频道ID获取该频道的过滤规则并执行过滤
- **队列后过滤**：消息从队列取出后，根据目标频道ID获取该频道的过滤规则并执行过滤
- **表情反应过滤**：专门针对ProductID进行过滤
- **前提条件限制**：表情反应和filter命令只能在存在转发规则的频道中使用
- **完整CRUD支持**：FilterRule系统支持创建、删除、查询、列表等完整操作
- **高性能过滤**：提供多种过滤性能优化方案

#### 极简的过滤规则结构
```go
type FilterRule struct {
    // 使用 Channel + Keyword + Mode 作为唯一标识，不再需要 Name 字段
    Channel   string    `json:"channel"`   // 应用的频道

    // 统一关键字过滤（命令和表情反应都使用）- 在ProductID, Title, URL中搜索
    Keyword   string    `json:"keyword,omitempty"`   // 关键字（在ProductID, Title, URL中搜索）

    Mode      string    `json:"mode"`      // whitelist, blacklist
    Source    string    `json:"source"`    // command, reaction（用于区分创建方式和日志记录）
    Enabled   bool      `json:"enabled"`   // 默认true
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
    CreatedBy string    `json:"created_by"`

    // 表情反应相关（仅当source=reaction时）
    MessageID     string                 `json:"message_id,omitempty"`
    ReactionEmoji string                 `json:"reaction_emoji,omitempty"`
    ProductData   map[string]interface{} `json:"product_data,omitempty"`
}

// 注意：不再需要 FilterCondition 结构，统一使用 Keyword 字段
```

#### 表情反应过滤机制重新设计
```go
// 表情反应配置（简化版）
type ReactionFilterConfig struct {
    WhitelistEmoji string   `json:"whitelist_emoji"` // 默认: ✅
    BlacklistEmoji string   `json:"blacklist_emoji"` // 默认: ❌
    AdminRoles     []string `json:"admin_roles"`     // 管理员角色ID
    AdminUsers     []string `json:"admin_users"`     // 管理员用户ID
    // 移除 enabled_channels 配置项
}

// 表情反应过滤规则
- 专门针对ProductID进行过滤
- 表情反应直接在当前消息所在频道创建过滤规则
- 前提条件：当前频道必须存在转发规则（无论是作为源频道还是目标频道）
- 取消表情反应（移除表情）意味着删除对应的过滤规则

// 工作流程
添加表情：消息发布 → 用户添加表情(✅/❌) → 权限验证（静默跳过无权限用户）→ 频道转发规则检查 → ProductID提取作为关键字 → 创建统一关键字过滤规则（在ProductID, Title, URL中搜索）→ 立即生效 → 记录操作日志

移除表情：用户移除表情 → 权限验证（静默跳过无权限用户）→ 查询消息提取ProductID → 根据ProductID关键字和表情类型匹配过滤规则 → 删除匹配的规则 → 记录操作日志

手动删除：/filter remove message_id:xxx emoji:✅ → 权限检查 → 查询指定消息 → 提取ProductID作为关键字 → 根据emoji确定模式 → 匹配并删除对应规则 → 记录操作日志

// 权限处理策略：
// - 命令操作：权限不足时返回错误提示给用户
// - 表情反应：权限不足时静默跳过，不给用户任何反馈
// - 所有操作：无论成功失败都记录详细日志
```

#### 简化的规则删除机制

提供多种删除方式，用户可以选择最方便的方式：

#### 简化的规则管理机制

```go
// 极简的过滤规则结构，支持+/-前缀
type FilterRule struct {
    // 移除 Name 字段，使用 Channel + Keyword + Mode 作为唯一标识
    Channel   string    `json:"channel"`   // 应用的频道
    Keyword   string    `json:"keyword,omitempty"`   // 统一关键字（命令和表情反应都使用）
    Mode      string    `json:"mode"`      // whitelist, blacklist（从+/-前缀解析得出）

    // 其他字段保持不变
    Source    string    `json:"source"`    // command, reaction（用于区分创建方式和日志记录）
    Enabled   bool      `json:"enabled"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
    CreatedBy string    `json:"created_by"`

    // 表情反应相关
    MessageID     string                 `json:"message_id,omitempty"`
    ReactionEmoji string                 `json:"reaction_emoji,omitempty"`
    ProductData   map[string]interface{} `json:"product_data,omitempty"`
}

// +/-前缀解析函数
func ParseKeywordWithMode(keywordWithPrefix string) (keyword string, mode string, err error) {
    if len(keywordWithPrefix) < 2 {
        return "", "", fmt.Errorf("关键字格式错误，应为 +关键字 或 -关键字")
    }

    prefix := keywordWithPrefix[0]
    keyword = keywordWithPrefix[1:]

    switch prefix {
    case '+':
        mode = "whitelist"
    case '-':
        mode = "blacklist"
    default:
        return "", "", fmt.Errorf("关键字必须以 + 或 - 开头，+ 表示白名单，- 表示黑名单")
    }

    if keyword == "" {
        return "", "", fmt.Errorf("关键字不能为空")
    }

    return keyword, mode, nil
}

// 重复性检测：同一频道+关键字+模式不能重复
func (v *SimpleFilterValidator) CheckDuplicate(newRule *FilterRule, existingRules []*FilterRule) error {
    if newRule.Source == "command" && newRule.Keyword != "" {
        for _, existing := range existingRules {
            if existing.Channel == newRule.Channel &&
               existing.Keyword == newRule.Keyword &&
               existing.Mode == newRule.Mode &&
               existing.Source == "command" {
                return fmt.Errorf("频道 %s 中已存在关键字 '%s' 的 %s 规则",
                    newRule.Channel, newRule.Keyword, newRule.Mode)
            }
        }
    }
    return nil
}
```

#### 极简的规则删除方式

```go
// 删除方式1：按关键字删除（最常用）
/filter remove channel:#deals keyword:+ABC123   # 删除白名单规则
/filter remove channel:#deals keyword:-Funko    # 删除黑名单规则

// 删除方式2：按表情反应删除（基于产品信息匹配）
/filter remove channel:#deals message_id:123456789 emoji:✅

// 表情删除工作机制：
// 1. 系统查询指定的消息ID，提取消息中的产品信息
// 2. 从产品信息中获取ProductID作为关键字
// 3. 根据emoji类型确定要删除的规则模式（✅=白名单，❌=黑名单）
// 4. 查找匹配ProductID关键字和对应模式的过滤规则
// 5. 删除找到的过滤规则
// 注意：删除操作基于ProductID关键字匹配，统一使用关键字过滤机制

// 删除方式3：批量删除（按模式清空）
/filter clear channel:#deals mode:whitelist confirm:CONFIRM
/filter clear channel:#deals mode:blacklist confirm:CONFIRM
/filter clear channel:#deals mode:all confirm:CONFIRM
```

#### 简化的验证机制

```go
// 大幅简化验证逻辑，只检查基本冲突
type SimpleFilterValidator struct {
    cache map[string][]*FilterRule
}

func (v *SimpleFilterValidator) ValidateRule(newRule *FilterRule, existingRules []*FilterRule) error {
    // 1. 基础字段验证
    if newRule.Channel == "" || newRule.Keyword == "" {
        return fmt.Errorf("频道和关键字不能为空")
    }

    // 2. 统一的重复性检测（命令和表情反应都使用相同逻辑）
    for _, existing := range existingRules {
        if existing.Channel == newRule.Channel &&
           existing.Keyword == newRule.Keyword &&
           existing.Mode == newRule.Mode {
            if newRule.Source == "command" {
                return fmt.Errorf("频道 %s 中已存在关键字 '%s' 的 %s 规则，请使用不同的关键字或删除现有规则",
                    newRule.Channel, newRule.Keyword, newRule.Mode)
            } else {
                return fmt.Errorf("频道 %s 中已存在关键字 '%s' 的 %s 表情反应规则",
                    newRule.Channel, newRule.Keyword, newRule.Mode)
            }
        }
    }

    return nil
}

// 注意：不再需要 hasSameConditions 函数，因为统一使用 Keyword 字段进行比较
```

#### 极简的关键字过滤
```go
// 不再指定具体字段，使用关键字同时在多个字段中匹配
type KeywordFilter struct {
    Keyword string   `json:"keyword"`           // 关键字
    Fields  []string `json:"fields,omitempty"` // 默认在 ProductID, Title, URL 中搜索
}

// 默认搜索字段
var DefaultSearchFields = []string{"ProductID", "Title", "URL"}

// 使用示例：
// 关键字: "ABC123" -> 在 ProductID, Title, URL 中查找包含 "ABC123" 的内容
// 关键字: "Funko" -> 在 ProductID, Title, URL 中查找包含 "Funko" 的内容
// 关键字: "amazon.com" -> 在 ProductID, Title, URL 中查找包含 "amazon.com" 的内容

// 匹配逻辑：只要任意一个字段包含关键字就匹配成功
// ProductID="ABC123" OR Title="Funko Pop ABC123" OR URL="https://amazon.com/ABC123"
// 任意一个包含关键字都会匹配
```

#### 过滤性能优化方案

##### 方案A：Redis过滤（推荐）
```go
// 利用Redis的数据结构进行高性能过滤
type RedisFilterEngine struct {
    client redis.Client
}

// Redis存储结构
// zeka:filter:channel:{channel_id}:whitelist:ProductID -> Set{product_ids}
// zeka:filter:channel:{channel_id}:blacklist:ProductID -> Set{product_ids}
// zeka:filter:channel:{channel_id}:rules -> Hash{rule_name: rule_json}

// 优势：
// - 利用Redis Set的O(1)查找性能
// - 支持分布式部署
// - 内存使用优化
// - 支持复杂查询操作
```

##### 方案B：内存过滤
```go
// 使用Go内存数据结构进行过滤
type MemoryFilterEngine struct {
    channelFilters map[string]*ChannelFilterCache
    mu             sync.RWMutex
}

type ChannelFilterCache struct {
    WhitelistProductIDs map[string]bool
    BlacklistProductIDs map[string]bool
    Rules              map[string]*FilterRule
    LastUpdate         time.Time
}

// 优势：
// - 极高的查找性能（纳秒级）
// - 无网络延迟
// - 简单的实现逻辑
// 劣势：
// - 内存占用较大
// - 不支持分布式
```

##### 方案C：混合过滤（高性能方案）
```go
// 结合Redis和内存的混合方案
type HybridFilterEngine struct {
    memoryCache  *MemoryFilterEngine
    redisBackend *RedisFilterEngine
    cacheTimeout time.Duration
}

// 工作原理：
// 1. 优先从内存缓存查找
// 2. 缓存未命中时从Redis加载
// 3. 定期同步Redis数据到内存
// 4. 写操作同时更新Redis和内存

// 优势：
// - 读取性能接近纯内存方案
// - 支持分布式部署
// - 数据持久化保证
// - 内存使用可控
```

### 4. 字段映射组设计

#### 映射组配置
字段映射组预设在配置文件中，支持默认映射组：

```yaml
# configs/field_mapping_groups.yaml
field_mapping_groups:
  amazon_standard:
    name: "Amazon标准映射"
    platform: "amazon"

    # 字段映射规则
    mappings:
      - source_field: "ASIN"           # 从Embed字段提取
        target_field: "ProductID"
        required: true

      - source_field: "Title"    # 直接映射Embed标题
        target_field: "Title"
        fallback_fields: ["Name"]

      - source_field: "PRICE"
        target_field: "Price"
        transform: "parse_price"

      - source_field: "embed_url"
        target_field: "URL"

    # 默认值
    defaults:
      Platform: "amazon"
      Country: "US"
      InStock: false

    # 数据转换规则
    transforms:
      parse_price:
        type: "regex"
        pattern: "\\$?([0-9,]+\\.?[0-9]*)"

  # 默认映射组
  default:
    name: "默认映射"
    mappings:
      - source_field: "Title"
        target_field: "Title"
      - source_field: "Url"
        target_field: "URL"
    defaults:
      Platform: "unknown"
```

### 5. 处理模式设计

#### 三种处理模式
1. **Forward模式**：直接转发原始消息（保持现有行为）
2. **Convert模式**：转换为产品信息后重新格式化
3. **Hybrid模式**：根据条件选择转发或转换

#### Convert模式输出格式
```go
type ConvertedMessage struct {
    Product *EnhancedProductItem `json:"product"`
    Format  string               `json:"format"` // "embed", "plain", "rich"
    Template string              `json:"template,omitempty"`
}
```

### 6. 过滤系统集成

#### 统一的产品信息过滤
无论是队列前还是队列后，都基于转换后的产品信息进行过滤：

```go
type ProductFilter struct {
    Field    string      `json:"field"`    // 产品字段名
    Operator string      `json:"operator"` // 操作符
    Value    interface{} `json:"value"`    // 过滤值
    Mode     string      `json:"mode"`     // whitelist/blacklist
}
```

#### 表情反应过滤增强
```go
type ReactionFilter struct {
    MessageID     string                 `json:"message_id"`
    ChannelID     string                 `json:"channel_id"`
    UserID        string                 `json:"user_id"`
    Emoji         string                 `json:"emoji"`
    FilterType    string                 `json:"filter_type"` // whitelist/blacklist
    ExtractedData *EnhancedProductItem   `json:"extracted_data"`
    CreatedAt     time.Time              `json:"created_at"`
}
```

### 5. 命令系统设计

#### Forward命令（需要管理员权限）
```bash
# 添加转发规则（自动生成name，默认delay=0s）
/forward add input:#source output:#target mapping:amazon_standard

# 删除转发规则
/forward remove name:source_to_target

# 列出转发规则（支持按频道过滤）
/forward list channel:#source

# 智能查询转发规则（支持双向查询）
/forward query channel:#source

# 权限要求：
# - 用户必须具有管理员权限或指定的转发管理角色
# - 权限检查在命令执行前进行
# - 权限不足时返回明确的错误提示
```

#### 极简的Filter命令（需要管理员权限）
```bash
# 添加过滤规则（使用+/-前缀指定白名单/黑名单）
/filter add channel:#target keyword:+ABC123      # 白名单：+ 前缀
/filter add channel:#target keyword:-Funko       # 黑名单：- 前缀
/filter add channel:#target keyword:+amazon.com  # 白名单：+ 前缀

# 简化的删除方式
/filter remove channel:#target keyword:+ABC123   # 删除白名单规则
/filter remove channel:#target keyword:-Funko    # 删除黑名单规则
/filter remove channel:#target message_id:123456789 emoji:✅  # 按表情删除（基于产品信息匹配）

# 列出过滤规则（移除source参数，因为来源对用户无实际价值）
/filter list channel:#target                    # 列出频道所有规则
/filter list channel:#target mode:whitelist     # 只显示白名单规则
/filter list channel:#target mode:blacklist     # 只显示黑名单规则

# 批量清空（按模式清空，移除基于来源的清空）
/filter clear channel:#target mode:whitelist confirm:CONFIRM  # 清空所有白名单规则
/filter clear channel:#target mode:blacklist confirm:CONFIRM  # 清空所有黑名单规则
/filter clear channel:#target mode:all confirm:CONFIRM        # 清空所有规则

# 权限要求：
# - 用户必须具有管理员权限或指定的过滤管理角色
# - 表情反应过滤同样需要管理员权限
# - 权限检查在命令执行前进行
# - 权限不足时返回明确的错误提示
```

#### 极简的命令参数
```yaml
Filter命令参数（极简版）:
  channel: 应用频道（必需）
  keyword: 关键字（必需）- 格式：+关键字（白名单）或 -关键字（黑名单）

# 关键字格式说明：
# +ABC123  -> 白名单，包含ABC123的产品会通过过滤
# -Funko   -> 黑名单，包含Funko的产品会被过滤掉
# +amazon.com -> 白名单，来自amazon.com的产品会通过过滤

  # 删除相关参数
  message_id: 消息ID（用于删除表情反应规则，系统会查询消息提取产品信息进行匹配删除）
  emoji: 表情符号（用于删除表情反应规则，✅=白名单，❌=黑名单）
  mode: 过滤模式（用于批量删除和列表过滤）- whitelist, blacklist, all
  confirm: 确认字符串（用于危险操作）- 必须输入"CONFIRM"
```

#### Filter命令前提条件
- **权限检查**：用户必须具有管理员权限或指定的过滤管理角色
- **频道转发规则检查**：filter命令只能在存在转发规则的频道中使用
- **双向检查**：频道可以是转发规则的源频道或目标频道
- **表情反应同样限制**：表情反应过滤也遵循相同的权限和前提条件

#### 命令参数说明
```yaml
Forward命令参数:
  input: 输入频道（必需）
  output: 输出频道（必需）
  mapping: 字段映射组（必需）
  delay: 延迟时间（可选，默认0s）


```

### 6. 配置管理设计

#### 配置文件分离策略

**核心设计原则**：
- **按频道分离存储**：每个频道的过滤规则独立存储，避免单一大文件的性能问题
- **避免并发冲突**：多个频道的规则修改不会相互影响
- **便于管理和备份**：频道级别的配置管理，支持单独备份和恢复

**文件命名规范**：
```
configs/
├── main_config.yaml                    # 主配置文件（权限、日志等）
├── forward_rules.yaml                  # 转发规则配置
├── field_mapping_groups.yaml           # 字段映射组配置
└── filter_rules/                       # 过滤规则目录
    ├── filter_rules_channel_123456789.yaml    # 频道123456789的过滤规则
    ├── filter_rules_channel_987654321.yaml    # 频道987654321的过滤规则
    └── filter_rules_channel_{channel_id}.yaml # 其他频道的过滤规则
```

**配置文件自动创建机制**：
```go
// 当为新频道创建第一个过滤规则时的处理流程
func CreateFilterRuleForChannel(channelID string, rule *FilterRule) error {
    configFile := fmt.Sprintf("configs/filter_rules/filter_rules_channel_%s.yaml", channelID)

    // 1. 检查配置文件是否存在
    if !fileExists(configFile) {
        // 2. 创建目录（如果不存在）
        if err := os.MkdirAll("configs/filter_rules", 0755); err != nil {
            return fmt.Errorf("创建配置目录失败: %w", err)
        }

        // 3. 创建新的配置文件
        initialConfig := &ChannelFilterConfig{
            ChannelID:    channelID,
            ChannelName:  getChannelName(channelID), // 从Discord API获取
            FilterRules:  []FilterRule{},
            CreatedAt:    time.Now(),
            UpdatedAt:    time.Now(),
        }

        if err := saveConfigFile(configFile, initialConfig); err != nil {
            return fmt.Errorf("创建配置文件失败: %w", err)
        }
    }

    // 4. 添加规则到配置文件和Redis
    return addRuleToChannelConfig(configFile, rule)
}
```

#### 启动时配置加载机制

**完整同步流程**：
```go
// 系统启动时的配置加载流程
func LoadAllConfigurationsOnStartup() error {
    // 1. 加载主配置文件
    if err := loadMainConfig("configs/main_config.yaml"); err != nil {
        return fmt.Errorf("加载主配置失败: %w", err)
    }

    // 2. 加载转发规则
    if err := loadForwardRules("configs/forward_rules.yaml"); err != nil {
        return fmt.Errorf("加载转发规则失败: %w", err)
    }

    // 3. 加载字段映射组
    if err := loadFieldMappingGroups("configs/field_mapping_groups.yaml"); err != nil {
        return fmt.Errorf("加载字段映射组失败: %w", err)
    }

    // 4. 扫描并加载所有频道的过滤规则
    if err := loadAllChannelFilterRules("configs/filter_rules/"); err != nil {
        return fmt.Errorf("加载过滤规则失败: %w", err)
    }

    return nil
}

// 加载所有频道过滤规则
func loadAllChannelFilterRules(configDir string) error {
    files, err := filepath.Glob(filepath.Join(configDir, "filter_rules_channel_*.yaml"))
    if err != nil {
        return fmt.Errorf("扫描配置文件失败: %w", err)
    }

    for _, file := range files {
        // 从文件名提取频道ID
        channelID := extractChannelIDFromFilename(file)

        // 加载配置文件
        config, err := loadChannelFilterConfig(file)
        if err != nil {
            log.Errorf("加载频道 %s 的配置失败: %v", channelID, err)
            continue
        }

        // 将配置加载到Redis（永久存储，无TTL）
        if err := syncChannelConfigToRedis(channelID, config); err != nil {
            log.Errorf("同步频道 %s 配置到Redis失败: %v", channelID, err)
            continue
        }

        log.Infof("成功加载频道 %s 的 %d 个过滤规则", channelID, len(config.FilterRules))
    }

    return nil
}
```

#### 命令操作时的双向同步

**同步策略**：
```go
// 用户通过命令修改过滤规则时的双向同步流程
func UpdateFilterRuleViaCommand(channelID string, operation string, rule *FilterRule) error {
    // 1. 更新Redis中的运行时数据
    if err := updateRedisFilterRule(channelID, operation, rule); err != nil {
        return fmt.Errorf("更新Redis失败: %w", err)
    }

    // 2. 更新对应频道的配置文件
    configFile := fmt.Sprintf("configs/filter_rules/filter_rules_channel_%s.yaml", channelID)
    if err := updateChannelConfigFile(configFile, operation, rule); err != nil {
        // Redis更新成功但文件更新失败，需要回滚Redis
        rollbackRedisOperation(channelID, operation, rule)
        return fmt.Errorf("更新配置文件失败: %w", err)
    }

    // 3. 记录操作日志
    logConfigurationChange(channelID, operation, rule)

    return nil
}

// 配置文件更新操作
func updateChannelConfigFile(configFile string, operation string, rule *FilterRule) error {
    // 加载现有配置
    config, err := loadChannelFilterConfig(configFile)
    if err != nil {
        return fmt.Errorf("加载配置文件失败: %w", err)
    }

    // 根据操作类型更新配置
    switch operation {
    case "add":
        config.FilterRules = append(config.FilterRules, *rule)
    case "remove":
        config.FilterRules = removeRuleFromConfig(config.FilterRules, rule)
    case "update":
        config.FilterRules = updateRuleInConfig(config.FilterRules, rule)
    }

    // 更新时间戳
    config.UpdatedAt = time.Now()

    // 保存配置文件
    return saveConfigFile(configFile, config)
}
```

#### Redis存储结构优化

**频道分离的Redis键值结构**：
```redis
# 转发规则
zeka:forward:rules:{rule_name} -> ForwardRule JSON
zeka:forward:by_input:{channel_id} -> Set[rule_names]
zeka:forward:by_output:{channel_id} -> Set[rule_names]

# 过滤规则（按频道分离）
zeka:filter:channel:{channel_id}:rules -> Hash{keyword_mode: FilterRule JSON}
zeka:filter:channel:{channel_id}:metadata -> Hash{
    "channel_name": "频道名称",
    "rule_count": "规则数量",
    "last_updated": "最后更新时间",
    "config_file": "配置文件路径"
}

# 过滤规则索引（用于快速查询）
zeka:filter:by_keyword:{keyword} -> Set[channel_ids]
zeka:filter:by_mode:{mode} -> Set[channel_ids]

# 映射组和权限配置
zeka:mapping:groups:{name} -> FieldMappingGroup JSON
zeka:permissions:commands -> Hash{command: [roles, users]}
zeka:permissions:reactions -> Hash{emoji: [roles, users]}

# 配置同步状态
zeka:config:sync_status -> Hash{
    "last_full_sync": "最后完整同步时间",
    "failed_channels": "同步失败的频道列表",
    "sync_in_progress": "是否正在同步"
}
```

#### 配置文件标准格式

**主配置文件（configs/main_config.yaml）**：
```yaml
# 系统主配置
system:
  version: "3.0.0"
  environment: "production"  # development, staging, production

# 命令权限配置
command_permissions:
  forward_admin_roles: ["admin_role_id", "forward_manager_role_id"]
  forward_admin_users: ["admin_user_id"]
  filter_admin_roles: ["admin_role_id", "filter_manager_role_id"]
  filter_admin_users: ["admin_user_id"]
  strict_permission_check: true
  permission_error_message: "您没有权限执行此命令，请联系管理员"

# 表情反应过滤配置
reaction_filter:
  whitelist_emoji: "✅"
  blacklist_emoji: "❌"
  admin_roles: ["admin_role_id"]
  admin_users: ["admin_user_id"]
  silent_permission_check: true

# 操作日志配置
operation_logging:
  enabled: true
  log_level: "INFO"
  log_file: "logs/operations.log"
  log_commands: true
  log_reactions: true
  log_permission_checks: true
  log_errors: true
  log_format: "json"

# 配置文件管理
config_management:
  filter_rules_directory: "configs/filter_rules/"
  backup_directory: "backups/"
  auto_backup_enabled: true
  backup_retention_days: 30
```

**频道过滤规则配置文件（configs/filter_rules/filter_rules_channel_{channel_id}.yaml）**：
```yaml
# 频道过滤规则配置
channel_info:
  channel_id: "123456789"
  channel_name: "deals"
  guild_id: "987654321"
  created_at: "2024-01-31T14:30:22Z"
  updated_at: "2024-01-31T15:45:33Z"

# 过滤规则列表
filter_rules:
  - channel: "123456789"
    keyword: "ABC123"
    mode: "whitelist"
    source: "command"
    enabled: true
    created_at: "2024-01-31T14:30:22Z"
    updated_at: "2024-01-31T14:30:22Z"
    created_by: "user_123"

  - channel: "123456789"
    keyword: "Funko"
    mode: "blacklist"
    source: "reaction"
    enabled: true
    created_at: "2024-01-31T15:45:33Z"
    updated_at: "2024-01-31T15:45:33Z"
    created_by: "user_456"
    message_id: "111222333"
    reaction_emoji: "❌"
    product_data:
      product_id: "Funko001"
      title: "Funko Pop Figure"
      url: "https://amazon.com/funko-pop"

# 配置元数据
metadata:
  rule_count: 2
  last_backup: "2024-01-31T12:00:00Z"
  config_version: "1.0"
```

**转发规则配置文件（configs/forward_rules.yaml）**：
```yaml
# 转发规则配置
forward_rules:
  - name: "amazon-it_to_deals"
    input_channel: "123456789"
    input_channel_name: "amazon-it"
    output_channel: "987654321"
    output_channel_name: "deals"
    mapping_group: "amazon_standard"
    delay: "0s"
    enabled: true
    created_at: "2024-01-31T10:00:00Z"
    updated_at: "2024-01-31T10:00:00Z"
    created_by: "admin_user"

  - name: "ebay-us_to_deals"
    input_channel: "555666777"
    input_channel_name: "ebay-us"
    output_channel: "987654321"
    output_channel_name: "deals"
    mapping_group: "ebay_standard"
    delay: "5s"
    enabled: true
    created_at: "2024-01-31T11:00:00Z"
    updated_at: "2024-01-31T11:00:00Z"
    created_by: "admin_user"
```

#### 错误处理和恢复机制

**配置文件操作错误处理**：
```go
// 配置文件操作的完整错误处理机制
type ConfigManager struct {
    backupDir     string
    maxRetries    int
    retryInterval time.Duration
}

// 安全的配置文件更新操作
func (cm *ConfigManager) SafeUpdateChannelConfig(channelID string, operation string, rule *FilterRule) error {
    configFile := fmt.Sprintf("configs/filter_rules/filter_rules_channel_%s.yaml", channelID)
    backupFile := fmt.Sprintf("%s/filter_rules_channel_%s_%d.yaml.backup",
        cm.backupDir, channelID, time.Now().Unix())

    // 1. 创建备份
    if err := cm.createBackup(configFile, backupFile); err != nil {
        return fmt.Errorf("创建备份失败: %w", err)
    }

    // 2. 尝试更新配置文件（带重试机制）
    var lastErr error
    for i := 0; i < cm.maxRetries; i++ {
        if err := cm.updateConfigFile(configFile, operation, rule); err != nil {
            lastErr = err
            log.Warnf("配置文件更新失败，第 %d 次重试: %v", i+1, err)
            time.Sleep(cm.retryInterval)
            continue
        }

        // 更新成功，验证文件完整性
        if err := cm.validateConfigFile(configFile); err != nil {
            lastErr = fmt.Errorf("配置文件验证失败: %w", err)
            continue
        }

        // 成功完成
        log.Infof("频道 %s 配置文件更新成功", channelID)
        return nil
    }

    // 3. 所有重试都失败，恢复备份
    if err := cm.restoreFromBackup(backupFile, configFile); err != nil {
        log.Errorf("恢复备份失败: %v", err)
        return fmt.Errorf("配置更新失败且备份恢复失败: %w", lastErr)
    }

    return fmt.Errorf("配置更新失败，已恢复备份: %w", lastErr)
}

// 配置文件完整性验证
func (cm *ConfigManager) validateConfigFile(configFile string) error {
    config, err := loadChannelFilterConfig(configFile)
    if err != nil {
        return fmt.Errorf("配置文件格式错误: %w", err)
    }

    // 验证必要字段
    if config.ChannelInfo.ChannelID == "" {
        return fmt.Errorf("缺少频道ID")
    }

    // 验证规则格式
    for i, rule := range config.FilterRules {
        if rule.Channel == "" || rule.Keyword == "" || rule.Mode == "" {
            return fmt.Errorf("规则 %d 格式不完整", i)
        }
        if rule.Mode != "whitelist" && rule.Mode != "blacklist" {
            return fmt.Errorf("规则 %d 模式无效: %s", i, rule.Mode)
        }
    }

    return nil
}
```

**Redis同步失败恢复机制**：
```go
// Redis同步失败时的恢复策略
func (cm *ConfigManager) RecoverFromRedisSyncFailure(channelID string) error {
    configFile := fmt.Sprintf("configs/filter_rules/filter_rules_channel_%s.yaml", channelID)

    // 1. 从配置文件重新加载
    config, err := loadChannelFilterConfig(configFile)
    if err != nil {
        return fmt.Errorf("加载配置文件失败: %w", err)
    }

    // 2. 清除Redis中的旧数据
    if err := cm.clearChannelRedisData(channelID); err != nil {
        log.Warnf("清除Redis旧数据失败: %v", err)
    }

    // 3. 重新同步到Redis
    if err := syncChannelConfigToRedis(channelID, config); err != nil {
        return fmt.Errorf("重新同步到Redis失败: %w", err)
    }

    // 4. 更新同步状态
    cm.updateSyncStatus(channelID, "recovered", time.Now())

    log.Infof("频道 %s 的Redis同步已恢复", channelID)
    return nil
}

// 系统启动时的一致性检查
func (cm *ConfigManager) PerformConsistencyCheck() error {
    log.Info("开始执行配置一致性检查...")

    // 1. 检查所有配置文件
    configFiles, err := filepath.Glob("configs/filter_rules/filter_rules_channel_*.yaml")
    if err != nil {
        return fmt.Errorf("扫描配置文件失败: %w", err)
    }

    var inconsistentChannels []string

    for _, configFile := range configFiles {
        channelID := extractChannelIDFromFilename(configFile)

        // 2. 比较配置文件和Redis数据
        if consistent, err := cm.checkChannelConsistency(channelID, configFile); err != nil {
            log.Errorf("检查频道 %s 一致性失败: %v", channelID, err)
            inconsistentChannels = append(inconsistentChannels, channelID)
        } else if !consistent {
            log.Warnf("频道 %s 配置不一致，需要修复", channelID)
            inconsistentChannels = append(inconsistentChannels, channelID)
        }
    }

    // 3. 修复不一致的频道
    for _, channelID := range inconsistentChannels {
        if err := cm.RecoverFromRedisSyncFailure(channelID); err != nil {
            log.Errorf("修复频道 %s 失败: %v", channelID, err)
        }
    }

    log.Infof("一致性检查完成，修复了 %d 个频道的配置", len(inconsistentChannels))
    return nil
}
```

#### 最佳实践和性能优化

**配置管理最佳实践**：
1. **原子操作**：所有配置更新操作都是原子的，要么全部成功，要么全部回滚
2. **备份策略**：每次修改前自动创建备份，保留最近30天的备份文件
3. **验证机制**：配置文件更新后立即验证格式和内容的正确性
4. **重试机制**：网络或IO错误时自动重试，避免临时故障导致的配置丢失
5. **监控告警**：配置同步失败时发送告警，及时发现和处理问题

**性能优化策略**：
1. **延迟写入**：批量操作时延迟写入配置文件，减少IO操作
2. **缓存机制**：频繁访问的配置数据在内存中缓存
3. **异步同步**：配置文件更新采用异步方式，不阻塞命令响应
4. **增量备份**：只备份发生变化的配置文件，减少存储空间占用
5. **压缩存储**：历史备份文件采用压缩存储，节省磁盘空间

#### 配置管理实施计划

**阶段1：基础架构搭建（2天）**
1. **目录结构创建**：
   - 创建 `configs/filter_rules/` 目录
   - 创建 `backups/` 目录
   - 设置适当的文件权限

2. **配置文件模板**：
   - 定义标准的YAML配置文件模板
   - 实现配置文件验证函数
   - 创建配置文件操作的基础函数

**阶段2：核心功能实现（3天）**
1. **配置加载机制**：
   - 实现启动时的完整配置加载
   - 实现配置文件到Redis的同步
   - 实现配置文件扫描和解析

2. **双向同步机制**：
   - 实现命令操作时的双向同步
   - 实现事务性配置更新
   - 实现回滚机制

**阶段3：错误处理和恢复（2天）**
1. **错误处理机制**：
   - 实现配置文件操作的错误处理
   - 实现重试机制
   - 实现备份和恢复功能

2. **一致性检查**：
   - 实现启动时的一致性检查
   - 实现配置修复机制
   - 实现监控和告警

**阶段4：性能优化和测试（2天）**
1. **性能优化**：
   - 实现异步配置同步
   - 实现配置缓存机制
   - 实现批量操作优化

2. **测试和验证**：
   - 单元测试覆盖
   - 集成测试验证
   - 性能测试和调优

### 7. 操作日志结构设计
```go
// 统一的操作日志结构
type OperationLog struct {
    Timestamp   time.Time `json:"timestamp"`
    Operation   string    `json:"operation"`    // "command", "reaction_add", "reaction_remove"
    Action      string    `json:"action"`       // "forward_add", "filter_add", "filter_remove", etc.
    UserID      string    `json:"user_id"`
    Username    string    `json:"username"`
    ChannelID   string    `json:"channel_id"`
    ChannelName string    `json:"channel_name"`

    // 权限检查结果
    PermissionCheck struct {
        Required bool   `json:"required"`
        Granted  bool   `json:"granted"`
        Roles    []string `json:"roles,omitempty"`
        Reason   string   `json:"reason,omitempty"`
    } `json:"permission_check"`

    // 操作详情
    Details map[string]interface{} `json:"details"`

    // 结果
    Success bool   `json:"success"`
    Error   string `json:"error,omitempty"`

    // 执行时间
    Duration time.Duration `json:"duration"`
}

// 日志示例
// 命令操作日志
{
    "timestamp": "2024-01-31T14:30:22Z",
    "operation": "command",
    "action": "filter_add",
    "user_id": "123456789",
    "username": "admin_user",
    "channel_id": "987654321",
    "channel_name": "deals",
    "permission_check": {
        "required": true,
        "granted": true,
        "roles": ["admin_role_id"]
    },
    "details": {
        "keyword": "+ABC123",
        "mode": "whitelist",
        "command": "/filter add channel:#deals keyword:+ABC123"
    },
    "success": true,
    "duration": "15ms"
}

// 表情反应日志
{
    "timestamp": "2024-01-31T14:31:15Z",
    "operation": "reaction_add",
    "action": "filter_create",
    "user_id": "123456789",
    "username": "admin_user",
    "channel_id": "987654321",
    "channel_name": "deals",
    "permission_check": {
        "required": true,
        "granted": false,
        "reason": "user not in admin roles"
    },
    "details": {
        "message_id": "111222333",
        "emoji": "✅",
        "keyword": "ABC123",
        "mode": "whitelist"
    },
    "success": false,
    "error": "permission denied - silently skipped",
    "duration": "5ms"
}
```

### 7. 实施计划

#### 阶段1：核心功能实现（1-2周）
1. **产品信息模型扩展**
   - 更新ProductItem结构，添加Embed字段支持
   - 实现Price字段合并逻辑
   - 集成现有Embed处理机制

2. **转发规则系统**
   - 实现1对1转发规则结构
   - 实现自动命名逻辑
   - 集成字段映射组支持

#### 阶段2：过滤系统实现（1周）
1. **统一过滤机制**
   - 实现基于产品信息的过滤
   - 支持多种操作符和字段类型
   - 实现过滤规则自动命名

2. **表情反应过滤**
   - 增强现有反应事件处理器
   - 实现权限验证机制
   - 实现产品信息提取和过滤规则创建

#### 阶段3：权限、日志和命令系统实现（1周）
1. **权限系统**
   - 实现角色和用户权限检查机制
   - 支持动态权限配置更新
   - 实现命令权限错误提示
   - 实现表情反应权限静默跳过机制

2. **操作日志系统**
   - 实现结构化日志记录（JSON格式）
   - 记录所有命令操作（成功/失败）
   - 记录所有表情反应操作（成功/失败/权限跳过）
   - 记录权限检查结果和错误信息
   - 支持日志级别配置和文件轮转

3. **Forward命令**
   - 实现add、remove、list、query子命令
   - 集成权限检查和日志记录机制
   - 支持智能查询和独立删除
   - 集成频道名称解析

4. **Filter命令**
   - 实现add、remove、list、clear子命令（移除query子命令）
   - 集成权限检查和日志记录机制
   - 支持+/-前缀关键字过滤和多种删除方式
   - 实现频道转发规则前提条件检查
   - 集成表情反应过滤显示

#### 阶段4：集成和优化（1周）
1. **配置管理集成**
   - 实现Redis和文件双向同步
   - 实现配置热重载
   - 添加配置验证和错误处理

2. **性能优化**
   - 实现缓存策略
   - 优化查询性能
   - 添加监控和调试功能

### 8. 向后兼容性

#### 现有配置支持
- 保持现有`forward_rules.mappings`配置的兼容性
- 提供自动迁移工具将旧配置转换为新格式
- 支持混合模式运行（新旧系统并存）

#### 迁移策略
1. **渐进式迁移**：新功能使用新系统，现有功能保持不变
2. **配置转换**：提供工具将现有ChannelMapping转换为ForwardRule
3. **平滑过渡**：支持在不停机的情况下切换到新系统

## 总结

这个统一设计方案提供了：

1. **简化的转发规则**：1对1模式，自动命名，映射组集成，独立规则管理
2. **扩展的产品信息模型**：支持Discord Embed字段，合并Price字段
3. **极简的过滤系统**：统一使用contains操作符，大幅简化复杂度
4. **智能表情反应过滤**：专门针对ProductID，支持取消表情删除规则
5. **多样化删除方式**：按名称、按条件、按表情、批量删除等多种方式
6. **简化的验证机制**：只检查基本重复和冲突，减少复杂验证逻辑
7. **高性能架构**：Redis/内存/混合三种过滤方案，支持快速contains匹配
8. **灵活的配置管理**：Redis运行时存储，文件持久化，双向同步
9. **向后兼容性**：保持现有系统功能，提供平滑迁移路径

## 简化后的使用示例

### 转发规则管理
```bash
# 添加转发规则（自动生成名称）
/forward add input:#amazon-it output:#deals mapping:amazon_standard

# 删除转发规则（独立删除，不影响过滤规则）
/forward remove name:amazon-it_to_deals

# 查询转发规则
/forward query channel:#amazon-it
```

### 过滤规则管理
```bash
# 添加过滤规则（使用+/-前缀，关键字在ProductID, Title, URL中搜索）
/filter add channel:#deals keyword:+ABC123      # 白名单：包含ABC123的产品通过
/filter add channel:#deals keyword:-Funko       # 黑名单：包含Funko的产品被过滤
/filter add channel:#deals keyword:+amazon.com  # 白名单：来自amazon.com的产品通过

# 极简的删除方式
/filter remove channel:#deals keyword:+ABC123   # 删除白名单规则
/filter remove channel:#deals keyword:-Funko    # 删除黑名单规则
/filter remove channel:#deals message_id:123456789 emoji:✅  # 删除表情反应规则（基于产品信息匹配）
/filter clear channel:#deals mode:whitelist confirm:CONFIRM  # 批量清空白名单规则

# 查看过滤规则（按模式过滤，不再区分来源）
/filter list channel:#deals                     # 显示所有规则
/filter list channel:#deals mode:whitelist      # 只显示白名单规则
/filter list channel:#deals mode:blacklist      # 只显示黑名单规则

# 示例输出：
# ✅ 关键字: ABC123 (白名单)
# ❌ 关键字: Funko (黑名单)
# ✅ 表情反应: ProductID=ABC123 (白名单)
```

### 表情反应过滤（静默权限处理）
```bash
# 用户在消息上添加✅表情 → 权限检查（静默跳过无权限用户）→ 自动创建白名单规则
# 规则内容：关键字 "ABC123" 在 ProductID, Title, URL 中搜索（统一关键字过滤）
# 权限处理：有权限用户 → 创建规则，无权限用户 → 静默跳过，不给任何反馈

# 用户移除✅表情 → 权限检查（静默跳过无权限用户）→ 系统查询消息提取ProductID作为关键字，匹配并删除对应的过滤规则
# 权限处理：有权限用户 → 删除规则，无权限用户 → 静默跳过，不给任何反馈

# 所有表情反应操作都会记录详细日志（包括权限跳过的情况）

# 查看所有过滤规则（包括表情反应创建的规则）
/filter list channel:#deals                     # 显示所有规则
/filter list channel:#deals mode:whitelist      # 只显示白名单规则（包括表情反应创建的）

# 手动删除表情反应规则（需要命令权限，会给用户反馈）
/filter remove channel:#deals message_id:123456789 emoji:✅  # 基于消息和表情删除
```

### 实际过滤效果
```go
// 使用+/-前缀的关键字过滤效果示例

# 白名单规则：/filter add channel:#deals keyword:+ABC123
关键字: "+ABC123" → 白名单匹配，以下情况的产品会通过过滤：
  - ProductID包含"ABC123": "ABC123", "XYZABC123", "ABC123DEF"
  - Title包含"ABC123": "New ABC123 Figure", "ABC123 Pop"
  - URL包含"ABC123": "https://amazon.com/ABC123", "https://store.com/product/ABC123"

# 黑名单规则：/filter add channel:#deals keyword:-Funko
关键字: "-Funko" → 黑名单匹配，以下情况的产品会被过滤掉：
  - ProductID包含"Funko": "Funko001", "NewFunko"
  - Title包含"Funko": "Funko Pop", "New Funko Figure"
  - URL包含"Funko": "https://amazon.com/Funko-Pop"

# 白名单规则：/filter add channel:#deals keyword:+amazon.com
关键字: "+amazon.com" → 白名单匹配，以下情况的产品会通过过滤：
  - URL包含"amazon.com": "https://amazon.com/product", "https://www.amazon.com/dp/123"
  - ProductID包含"amazon.com": 不太可能，但支持
  - Title包含"amazon.com": 不太可能，但支持

# 过滤逻辑：
# - 白名单：只有匹配的产品才能通过
# - 黑名单：匹配的产品会被过滤掉
# - 多规则：按优先级和逻辑组合执行
```

该方案充分利用现有系统的优势，同时通过大幅简化操作符和验证逻辑，提供了更加用户友好和易于维护的过滤系统。
