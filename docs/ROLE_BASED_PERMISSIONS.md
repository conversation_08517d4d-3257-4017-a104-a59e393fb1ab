# 基于角色ID的权限系统

## 概述

Zeka Discord Bot 现在支持基于角色ID的精确权限控制系统，提供比Discord内置权限位更灵活和准确的权限管理。

## 权限系统架构

### 权限检查优先级

1. **Bot所有者** - 拥有所有权限，跳过所有检查
2. **角色ID权限** - 检查用户是否拥有指定的角色ID
3. **用户ID权限** - 检查特定用户ID
4. **Discord权限位** - 回退到传统的Discord权限检查
5. **回退权限配置** - 使用配置文件中的回退权限

### 支持的功能

- ✅ **命令权限控制** - filter、forward等命令
- ✅ **表情反应权限控制** - 表情反应过滤功能
- ✅ **混合权限模式** - 角色ID + 权限位双重保障
- ✅ **动态配置** - 支持热重载权限配置

## 配置文件

### 权限配置文件位置
```
configs/permissions.yaml
```

### 配置文件结构

```yaml
# Bot 所有者（拥有所有权限）
bot_owners:
  - "1245089093071802470"  # 替换为实际的用户ID

# 是否启用权限检查
enable_permission_check: true

# 命令权限要求
command_permissions:
  filter:
    allowed_roles:
      - "1234567890123456789"  # 管理员角色ID
      - "9876543210987654321"  # 频道管理员角色ID
    allowed_users:
      - "1111111111111111111"  # 特定用户ID
    fallback_permissions:
      - "MANAGE_CHANNELS"      # 回退到Discord权限

  forward:
    allowed_roles:
      - "1234567890123456789"  # 仅管理员角色ID
    allowed_users:
      - "1111111111111111111"  # Bot管理员用户ID
    fallback_permissions:
      - "ADMINISTRATOR"        # 回退到Discord权限

# 表情反应权限
reaction_permissions:
  allowed_roles:
    - "1234567890123456789"    # 管理员角色ID
    - "5555555555555555555"    # 版主角色ID
  allowed_users:
    - "1111111111111111111"    # 特定用户ID
  fallback_permissions:
    - "MANAGE_MESSAGES"        # 回退到Discord权限
```

## 如何获取角色ID和用户ID

### 获取角色ID
1. 在Discord中右键点击角色
2. 选择"复制ID"（需要开启开发者模式）
3. 或使用命令 `/role info @角色名`

### 获取用户ID
1. 在Discord中右键点击用户
2. 选择"复制ID"（需要开启开发者模式）
3. 或使用命令 `/user info @用户名`

### 开启开发者模式
1. Discord设置 → 高级 → 开发者模式 → 开启

## 权限配置示例

### 示例1：严格的管理员控制
```yaml
command_permissions:
  forward:
    allowed_roles:
      - "123456789012345678"   # 仅服务器管理员角色
    allowed_users: []          # 不允许特定用户
    fallback_permissions: []   # 不使用回退权限
```

### 示例2：分层权限管理
```yaml
command_permissions:
  filter:
    allowed_roles:
      - "123456789012345678"   # 管理员角色
      - "987654321098765432"   # 版主角色
      - "456789012345678901"   # 频道管理员角色
    allowed_users:
      - "111111111111111111"   # 特殊权限用户
    fallback_permissions:
      - "MANAGE_CHANNELS"      # 拥有管理频道权限的用户也可以使用
```

### 示例3：宽松的权限控制
```yaml
command_permissions:
  filter:
    allowed_roles: []          # 不限制角色
    allowed_users: []          # 不限制用户
    fallback_permissions:
      - "SEND_MESSAGES"        # 任何能发送消息的用户都可以使用
```

## 权限检查日志

系统会记录详细的权限检查日志：

```json
{
  "level": "debug",
  "message": "角色权限检查通过",
  "user": "123456789012345678",
  "command": "filter"
}
```

```json
{
  "level": "warn", 
  "message": "用户权限检查失败",
  "user": "123456789012345678",
  "command": "forward"
}
```

## 性能优势

| 检查方式 | 性能 | 准确性 | 灵活性 |
|----------|------|--------|--------|
| **角色ID检查** | 高 (O(n*m)) | 高 | 高 |
| **权限位检查** | 中 (需要计算) | 中 | 低 |

其中 n = 用户角色数量，m = 允许角色数量（通常都很小）

## 故障排除

### 权限配置不生效
1. 检查 `enable_permission_check: true`
2. 确认角色ID和用户ID正确
3. 查看日志中的权限检查信息

### 获取角色ID失败
1. 确认已开启Discord开发者模式
2. 确认有足够权限查看角色信息
3. 使用Bot命令获取角色信息

### 权限过于严格
1. 添加回退权限配置
2. 检查用户是否在正确的角色中
3. 临时禁用权限检查进行测试

## 最佳实践

1. **最小权限原则** - 只给予必要的权限
2. **分层管理** - 使用不同角色实现权限分层
3. **回退机制** - 配置合理的回退权限
4. **定期审查** - 定期检查和更新权限配置
5. **测试验证** - 在生产环境前充分测试权限配置

## 迁移指南

### 从权限位迁移到角色ID

1. **识别当前权限需求**
   ```yaml
   # 旧配置（仅权限位）
   required_permissions:
     - "MANAGE_CHANNELS"
   ```

2. **创建对应角色**
   - 在Discord中创建专门的Bot管理角色
   - 分配给需要权限的用户

3. **更新配置**
   ```yaml
   # 新配置（角色ID + 回退）
   allowed_roles:
     - "123456789012345678"  # Bot管理员角色
   fallback_permissions:
     - "MANAGE_CHANNELS"     # 保持兼容性
   ```

4. **逐步迁移**
   - 先添加角色ID配置
   - 保留回退权限确保兼容性
   - 测试验证后移除回退权限

## 技术实现

权限系统的核心实现位于：
- `internal/types/config.go` - 权限配置数据结构
- `internal/handlers/permission.go` - 权限检查逻辑
- `internal/config/config.go` - 配置文件加载
- `internal/events/reaction.go` - 表情反应权限检查
