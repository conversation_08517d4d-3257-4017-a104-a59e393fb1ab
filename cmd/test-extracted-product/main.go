package main

import (
	"context"
	"fmt"
	"time"

	"zeka-go/internal/bot"
	"zeka-go/internal/config"
	"zeka-go/internal/services/notification"
	"zeka-go/internal/types"
)

func main() {
	fmt.Println("=== 提取产品信息并重新发送测试 ===")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("❌ 加载配置失败: %v\n", err)
		return
	}

	fmt.Println("✅ 配置加载成功")

	// 创建 Bot 实例
	botInstance, err := bot.New(cfg)
	if err != nil {
		fmt.Printf("❌ 创建 Bot 实例失败: %v\n", err)
		return
	}

	fmt.Println("✅ Bot 实例创建成功")

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Minute)
	defer cancel()

	// 启动 Bot
	fmt.Println("🚀 启动 Bot...")
	if err := botInstance.Start(ctx); err != nil {
		fmt.Printf("❌ 启动 Bot 失败: %v\n", err)
		return
	}

	defer func() {
		fmt.Println("🔄 关闭 Bot...")
		botInstance.Shutdown(ctx)
	}()

	// 等待连接建立
	fmt.Println("⏳ 等待连接建立...")
	time.Sleep(3 * time.Second)

	// 获取产品通知服务
	fmt.Println("🔍 获取产品通知服务...")
	botImpl := botInstance.(*bot.Bot)
	serviceManager := botImpl.GetServiceManager()

	productNotificationServiceRaw, err := serviceManager.GetService("product_notification")
	if err != nil {
		fmt.Printf("❌ 获取产品通知服务失败: %v\n", err)
		return
	}

	productNotificationAdapter := productNotificationServiceRaw.(*notification.ProductNotificationServiceAdapter)
	service := productNotificationAdapter.GetService()
	fmt.Println("✅ 产品通知服务获取成功")

	// 从Discord消息提取产品信息
	fmt.Println("\n📦 从Discord消息提取产品信息...")

	// 提取的产品信息
	extractedProduct := &types.ProductItem{
		Title:        "POP MART THE MONSTERS FALL IN WILD SERIES - Vinyl Plush Doll Limited to 1pc per Order",
		URL:          "https://zephr.app/v3/pu999?u=www.aliexpress.com%2F0f1g0b0j04h04j04i04i05d04i04i05f05h05h05a04i05d05h05d04j05a04g0e1g0j0i",
		ProductID:    "1005007991996609",
		Price:        "N/A",        // 从fields中提取
		Platform:     "aliexpress", // 从author信息推断
		Stock:        2,            // 从Inventory字段提取
		Availability: "现货 - 限购1件",
		ImageURL:     stringPtr("https://ae01.alicdn.com/kf/A079b69a9cc1f471fb3e92ae2d91474f0i/POP-MART-THE-MONSTERS-FLIP-WITH-ME-Vinyl-Plush-Doll-Limited-to-2pcs-per-order.jpg"),
		SkuID:        stringPtr("12000043186175049"),
		AtcLink:      stringPtr("https://www.aliexpress.com/p/trade/confirm.html?objectId=1005007991996609&skuId=12000043186175049&quantity=1"),
		AuthorName:   stringPtr("AliExpress PopMart 官方"),
		Timestamp:    stringPtr(time.Now().Format(time.RFC3339)),
		Metadata: map[string]interface{}{
			"original_source":    "Zephyr Monitors",
			"monitor_version":    "v2.0.0",
			"restock_type":       "Restock",
			"quicktask":          "fdsafds",
			"inventory_count":    2,
			"purchase_limit":     "1pc per order",
			"original_timestamp": "2025-07-31T16:00:00.000Z",
			"extracted_fields": map[string]string{
				"Price":     "N/A",
				"Type":      "Restock",
				"Inventory": "2",
				"Quicktask": "fdsafds",
			},
		},
	}

	fmt.Println("📋 提取的产品信息:")
	fmt.Printf("  标题: %s\n", extractedProduct.Title)
	fmt.Printf("  链接: %s\n", extractedProduct.URL)
	fmt.Printf("  产品ID: %s\n", extractedProduct.ProductID)
	fmt.Printf("  价格: %s\n", extractedProduct.Price)
	fmt.Printf("  平台: %s\n", extractedProduct.Platform)
	fmt.Printf("  库存: %d\n", extractedProduct.Stock)
	fmt.Printf("  可用性: %s\n", extractedProduct.Availability)
	if extractedProduct.Description != nil {
		fmt.Printf("  描述长度: %d 字符\n", len(*extractedProduct.Description))
	}
	if extractedProduct.ImageURL != nil {
		fmt.Printf("  图片: %s\n", *extractedProduct.ImageURL)
	}

	// 目标频道ID
	channelID := "1395859887434498068"
	fmt.Printf("\n🎯 目标频道ID: %s\n", channelID)

	// 发送使用AliExpress模板的通知
	fmt.Println("\n📤 发送AliExpress模板通知...")
	result, err := service.SendProductNotification(ctx, extractedProduct, channelID)
	if err != nil {
		fmt.Printf("❌ 发送失败: %v\n", err)
		return
	}

	if result.Success {
		fmt.Println("✅ AliExpress模板通知发送成功!")
		fmt.Printf("   📧 通知ID: %s\n", result.ID)
		fmt.Printf("   💬 消息ID: %s\n", result.MessageID)
		fmt.Printf("   🎨 使用模板: %s\n", result.TemplateUsed)
		fmt.Printf("   🏷️  平台: %s\n", result.Platform)
		fmt.Printf("   ⏱️  模板渲染: %v\n", result.TemplateRenderTime)
		fmt.Printf("   📡 消息发送: %v\n", result.MessageSendTime)
		fmt.Printf("   🕐 总耗时: %v\n", result.TotalTime)
	} else {
		fmt.Printf("❌ AliExpress模板通知发送失败: %v\n", result.Error)
		return
	}

	// 等待间隔
	fmt.Println("\n⏳ 等待 3 秒...")
	time.Sleep(3 * time.Second)

	// 尝试使用PopMart模板（因为这是PopMart产品）
	fmt.Println("📤 发送PopMart模板通知...")
	popMartProduct := &types.ProductItem{
		Title:        extractedProduct.Title,
		URL:          extractedProduct.URL,
		ProductID:    extractedProduct.ProductID,
		Price:        extractedProduct.Price,
		Platform:     "popmart", // 改为popmart平台
		Stock:        extractedProduct.Stock,
		Availability: extractedProduct.Availability,
		Description:  extractedProduct.Description,
		ImageURL:     extractedProduct.ImageURL,
		SkuID:        extractedProduct.SkuID,
		AtcLink:      extractedProduct.AtcLink,
		ReleaseDate:  stringPtr("2025-07-31"), // 从原始时间戳提取
		AuthorName:   stringPtr("POP MART 官方"),
		Timestamp:    stringPtr(time.Now().Format(time.RFC3339)),
		Metadata:     extractedProduct.Metadata,
	}

	popResult, err := service.SendProductNotification(ctx, popMartProduct, channelID)
	if err != nil {
		fmt.Printf("❌ 发送失败: %v\n", err)
		return
	}

	if popResult.Success {
		fmt.Println("✅ PopMart模板通知发送成功!")
		fmt.Printf("   📧 通知ID: %s\n", popResult.ID)
		fmt.Printf("   💬 消息ID: %s\n", popResult.MessageID)
		fmt.Printf("   🎨 使用模板: %s\n", popResult.TemplateUsed)
		fmt.Printf("   🏷️  平台: %s\n", popResult.Platform)
	} else {
		fmt.Printf("❌ PopMart模板通知发送失败: %v\n", popResult.Error)
		return
	}

	// 等待间隔
	fmt.Println("\n⏳ 等待 3 秒...")
	time.Sleep(3 * time.Second)

	// 测试高级选项通知
	fmt.Println("📤 发送高级选项通知（强制使用Amazon模板）...")
	options := types.ProductNotificationOptions{
		Product:   extractedProduct,
		ChannelID: channelID,
		Priority:  types.PriorityHigh,
		Timeout:   30 * time.Second,
		RetryMax:  2,
		Metadata: map[string]interface{}{
			"test_type":         "extracted_product",
			"original_source":   "discord_message",
			"template_override": true,
		},
		TemplateID:       "amazon", // 强制使用Amazon模板
		TemplateOverride: true,
	}

	advancedResult, err := service.SendProductNotificationWithOptions(ctx, options)
	if err != nil {
		fmt.Printf("❌ 发送高级选项通知失败: %v\n", err)
		return
	}

	if advancedResult.Success {
		fmt.Println("✅ 高级选项通知发送成功!")
		fmt.Printf("   📧 通知ID: %s\n", advancedResult.ID)
		fmt.Printf("   💬 消息ID: %s\n", advancedResult.MessageID)
		fmt.Printf("   🎨 使用模板: %s\n", advancedResult.TemplateUsed)
	} else {
		fmt.Printf("❌ 高级选项通知发送失败: %v\n", advancedResult.Error)
		return
	}

	fmt.Println("\n🎉 提取产品信息测试完成!")
	fmt.Println("📱 请检查Discord频道中的3条消息:")
	fmt.Println("   1. AliExpress模板版本")
	fmt.Println("   2. PopMart模板版本")
	fmt.Println("   3. Amazon模板版本（强制使用）")
	fmt.Println("\n🔍 对比分析:")
	fmt.Println("   • 检查哪个模板最适合显示这类产品信息")
	fmt.Println("   • 验证描述、字段、图片等是否正确显示")
	fmt.Println("   • 确认模板选择逻辑是否正确工作")
}

func stringPtr(s string) *string {
	return &s
}
