package main

import (
	"context"
	"fmt"
	"time"

	"zeka-go/internal/bot"
	"zeka-go/internal/config"
	"zeka-go/internal/services/notification"
	"zeka-go/internal/services/template"
	"zeka-go/internal/types"
)

func main() {
	fmt.Println("=== AliExpress 产品通知测试 ===")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("❌ 加载配置失败: %v\n", err)
		return
	}

	fmt.Println("✅ 配置加载成功")

	// 创建 Bot 实例
	botInstance, err := bot.New(cfg)
	if err != nil {
		fmt.Printf("❌ 创建 Bot 实例失败: %v\n", err)
		return
	}

	fmt.Println("✅ Bot 实例创建成功")

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Minute)
	defer cancel()

	// 启动 Bot
	fmt.Println("🚀 启动 Bot...")
	if err := botInstance.Start(ctx); err != nil {
		fmt.Printf("❌ 启动 Bot 失败: %v\n", err)
		return
	}

	defer func() {
		fmt.Println("🔄 关闭 Bot...")
		botInstance.Shutdown(ctx)
	}()

	// 等待连接建立
	fmt.Println("⏳ 等待连接建立...")
	time.Sleep(3 * time.Second)

	// 获取产品通知服务
	fmt.Println("🔍 获取产品通知服务...")
	botImpl := botInstance.(*bot.Bot)
	serviceManager := botImpl.GetServiceManager()

	productNotificationServiceRaw, err := serviceManager.GetService("product_notification")
	if err != nil {
		fmt.Printf("❌ 获取产品通知服务失败: %v\n", err)
		return
	}

	productNotificationAdapter := productNotificationServiceRaw.(*notification.ProductNotificationServiceAdapter)
	service := productNotificationAdapter.GetService()
	fmt.Println("✅ 产品通知服务获取成功")

	// 创建测试用的 ProductItem（使用提供的数据）
	fmt.Println("\n📦 创建 AliExpress 产品信息...")

	extractedProduct := &types.ProductItem{
		Title:        "POP MART THE MONSTERS FALL IN WILD SERIES - Vinyl Plush Doll Limited to 1pc per Order",
		URL:          "https://zephr.app/v3/pu999?u=www.aliexpress.com%2F0f1g0b0j04h04j04i04i05d04i04i05f05h05h05a04i05d05h05d04j05a04g0e1g0j0i",
		ProductID:    "1005007991996609",
		Price:        "¥89.00", // 修复：提供有效的价格值而不是"N/A"
		Platform:     "aliexpress",
		Stock:        2,
		Availability: "现货 - 限购1件",
		ImageURL:     stringPtr("https://ae01.alicdn.com/kf/A079b69a9cc1f471fb3e92ae2d91474f0i/POP-MART-THE-MONSTERS-FLIP-WITH-ME-Vinyl-Plush-Doll-Limited-to-2pcs-per-order.jpg"),
		// 修复字段映射问题：同时设置 ThumbnailURL
		ThumbnailURL: stringPtr("https://ae01.alicdn.com/kf/A079b69a9cc1f471fb3e92ae2d91474f0i/POP-MART-THE-MONSTERS-FLIP-WITH-ME-Vinyl-Plush-Doll-Limited-to-2pcs-per-order.jpg"),
		SkuID:        stringPtr("12000043186175049"),
		AtcLink:      stringPtr("https://www.aliexpress.com/p/trade/confirm.html?objectId=1005007991996609&skuId=12000043186175049&quantity=1"),
		AuthorName:   stringPtr("AliExpress PopMart 官方"),
		Timestamp:    stringPtr(time.Now().Format(time.RFC3339)),
		Metadata: map[string]interface{}{
			"original_source":    "Zephyr Monitors",
			"monitor_version":    "v2.0.0",
			"restock_type":       "Restock",
			"quicktask":          "fdsafds",
			"inventory_count":    2,
			"purchase_limit":     "1pc per order",
			"original_timestamp": "2025-07-31T16:00:00.000Z",
			"extracted_fields": map[string]string{
				"Price":     "N/A",
				"Type":      "Restock",
				"Inventory": "2",
				"Quicktask": "fdsafds",
			},
		},
	}

	// 显示产品信息
	fmt.Println("📋 产品信息详情:")
	fmt.Printf("  标题: %s\n", extractedProduct.Title)
	fmt.Printf("  链接: %s\n", extractedProduct.URL)
	fmt.Printf("  产品ID: %s\n", extractedProduct.ProductID)
	fmt.Printf("  价格: %s\n", extractedProduct.Price)
	fmt.Printf("  平台: %s\n", extractedProduct.Platform)
	fmt.Printf("  库存: %d\n", extractedProduct.Stock)
	fmt.Printf("  可用性: %s\n", extractedProduct.Availability)
	if extractedProduct.ImageURL != nil {
		fmt.Printf("  图片URL: %s\n", *extractedProduct.ImageURL)
	}
	if extractedProduct.ThumbnailURL != nil {
		fmt.Printf("  缩略图URL: %s\n", *extractedProduct.ThumbnailURL)
	}
	if extractedProduct.SkuID != nil {
		fmt.Printf("  SKU ID: %s\n", *extractedProduct.SkuID)
	}
	if extractedProduct.AtcLink != nil {
		fmt.Printf("  购买链接: %s\n", *extractedProduct.AtcLink)
	}

	// 测试模板变量映射
	fmt.Println("\n🔍 测试模板变量映射...")
	adapter := template.NewProductItemAdapter()
	variables := adapter.ToTemplateVariables(extractedProduct)

	fmt.Println("📝 映射的模板变量:")
	for key, value := range variables {
		fmt.Printf("  %s: %v\n", key, value)
	}

	// 注意：模板渲染的详细调试将在发送通知后通过日志查看

	// 目标频道ID
	channelID := "1395859887434498068"
	fmt.Printf("\n🎯 目标频道ID: %s\n", channelID)

	// 发送 AliExpress 通知
	fmt.Println("\n📤 发送 AliExpress 产品通知...")

	// 添加调试：检查关键字段值
	fmt.Println("🔍 关键字段值检查:")
	fmt.Printf("  price: '%s'\n", extractedProduct.Price)
	fmt.Printf("  productId: '%s'\n", extractedProduct.ProductID)
	if extractedProduct.SkuID != nil {
		fmt.Printf("  skuId: '%s'\n", *extractedProduct.SkuID)
	}
	fmt.Printf("  stock: %d\n", extractedProduct.Stock)
	if extractedProduct.AtcLink != nil {
		fmt.Printf("  atcLink: '%s'\n", *extractedProduct.AtcLink)
	}

	result, err := service.SendProductNotification(ctx, extractedProduct, channelID)
	if err != nil {
		fmt.Printf("❌ 发送失败: %v\n", err)
		return
	}

	if result.Success {
		fmt.Println("✅ AliExpress 通知发送成功!")
		fmt.Printf("   📧 通知ID: %s\n", result.ID)
		fmt.Printf("   💬 消息ID: %s\n", result.MessageID)
		fmt.Printf("   🎨 使用模板: %s\n", result.TemplateUsed)
		fmt.Printf("   🏷️  平台: %s\n", result.Platform)
		fmt.Printf("   ⏱️  模板渲染时间: %v\n", result.TemplateRenderTime)
		fmt.Printf("   📡 消息发送时间: %v\n", result.MessageSendTime)
		fmt.Printf("   🕐 总耗时: %v\n", result.TotalTime)
	} else {
		fmt.Printf("❌ AliExpress 通知发送失败: %v\n", result.Error)
		return
	}

	fmt.Println("\n🎉 AliExpress 产品通知测试完成!")
	fmt.Println("📱 请检查Discord频道中的通知消息")
	fmt.Println("🔍 验证以下内容:")
	fmt.Println("   • 标题是否正确显示")
	fmt.Println("   • 缩略图是否正确加载")
	fmt.Println("   • 所有字段（价格、产品ID、SKU、库存、购买链接）是否正确")
	fmt.Println("   • 页脚和时间戳是否正确")
	fmt.Println("   • AliExpress 橙红色主题是否应用")
}

func stringPtr(s string) *string {
	return &s
}
