package main

import (
	"fmt"
	"regexp"
	"strings"
)

// 简化的变量替换函数，模拟模板渲染器的逻辑
func replaceVariables(text string, variables map[string]interface{}) string {
	if text == "" {
		return text
	}

	// 处理简单的 {variable} 格式（单花括号）
	singlePattern := regexp.MustCompile(`\{([^}]+)\}`)
	text = singlePattern.ReplaceAllStringFunc(text, func(match string) string {
		// 提取变量名
		varName := strings.TrimSpace(match[1 : len(match)-1])

		// 获取变量值
		value := variables[varName]
		if value == nil {
			fmt.Printf("❌ 变量未找到: %s\n", varName)
			return match // 保留原始占位符
		}

		result := fmt.Sprintf("%v", value)
		fmt.Printf("✅ 变量替换: %s -> %s\n", varName, result)
		return result
	})

	return text
}

func main() {
	fmt.Println("=== 变量替换测试 ===")

	// 模拟AliExpress模板的字段
	fields := []map[string]interface{}{
		{
			"name":   "💰 PRICE",
			"value":  "{price}",
			"inline": true,
		},
		{
			"name":   "🆔 PRODUCT",
			"value":  "`{productId}`",
			"inline": true,
		},
		{
			"name":   "🆔 SKU",
			"value":  "`{skuId}`",
			"inline": true,
		},
		{
			"name":   "📊 STOCK",
			"value":  "{stock}",
			"inline": true,
		},
		{
			"name":   "🛒 ATC",
			"value":  "{atcLink}",
			"inline": true,
		},
	}

	// 模拟变量映射
	variables := map[string]interface{}{
		"price":     "¥89.00",
		"productId": "1005007991996609",
		"skuId":     "12000043186175049",
		"stock":     "2",
		"atcLink":   "https://www.aliexpress.com/p/trade/confirm.html?objectId=1005007991996609&skuId=12000043186175049&quantity=1",
	}

	fmt.Println("\n📝 可用变量:")
	for key, value := range variables {
		fmt.Printf("  %s: %v\n", key, value)
	}

	fmt.Println("\n🔄 字段替换测试:")
	for i, field := range fields {
		fmt.Printf("\n字段 %d:\n", i+1)
		
		name := field["name"].(string)
		value := field["value"].(string)
		
		fmt.Printf("  原始名称: %s\n", name)
		fmt.Printf("  原始值: %s\n", value)
		
		replacedName := replaceVariables(name, variables)
		replacedValue := replaceVariables(value, variables)
		
		fmt.Printf("  替换后名称: %s\n", replacedName)
		fmt.Printf("  替换后值: %s\n", replacedValue)
		
		if replacedValue == value {
			fmt.Printf("  ⚠️  值没有被替换！\n")
		} else {
			fmt.Printf("  ✅ 值替换成功\n")
		}
	}

	fmt.Println("\n🎯 测试结论:")
	fmt.Println("如果所有变量都能正确替换，那么问题可能在模板加载或Discord API层面")
	fmt.Println("如果变量替换失败，那么问题在变量映射或替换逻辑")
}
