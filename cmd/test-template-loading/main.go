package main

import (
	"context"
	"encoding/json"
	"fmt"

	"zeka-go/internal/services/template"
)

func main() {
	fmt.Println("=== 模板加载测试 ===")

	// 创建模板管理器
	templateManager := template.NewManager("templates")

	// 初始化模板管理器
	ctx := context.Background()
	if err := templateManager.Initialize(ctx); err != nil {
		fmt.Printf("❌ 初始化模板管理器失败: %v\n", err)
		return
	}

	// 获取AliExpress模板
	aliexpressTemplate, err := templateManager.GetTemplate("aliexpress")
	if err != nil {
		fmt.Printf("❌ 获取AliExpress模板失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 成功获取AliExpress模板\n")
	fmt.Printf("  ID: %s\n", aliexpressTemplate.ID)
	fmt.Printf("  名称: %s\n", aliexpressTemplate.Name)
	fmt.Printf("  类型: %s\n", aliexpressTemplate.Type)

	// 检查模板内容
	fmt.Println("\n🔍 检查模板内容结构:")
	contentJSON, err := json.MarshalIndent(aliexpressTemplate.Content, "", "  ")
	if err != nil {
		fmt.Printf("❌ 序列化模板内容失败: %v\n", err)
		return
	}

	fmt.Printf("模板内容:\n%s\n", string(contentJSON))

	// 特别检查embed字段
	if content, ok := aliexpressTemplate.Content.(map[string]interface{}); ok {
		if embed, ok := content["embed"].(map[string]interface{}); ok {
			fmt.Println("\n📋 Embed结构分析:")

			if title, ok := embed["title"].(string); ok {
				fmt.Printf("  标题模板: %s\n", title)
			}

			if fieldsRaw, exists := embed["fields"]; exists {
				fmt.Printf("  fields字段存在，类型: %T\n", fieldsRaw)
				if fields, ok := fieldsRaw.([]interface{}); ok {
					fmt.Printf("  字段数量: %d\n", len(fields))
					for i, fieldRaw := range fields {
						if field, ok := fieldRaw.(map[string]interface{}); ok {
							name := field["name"].(string)
							value := field["value"].(string)
							inline := field["inline"].(bool)
							fmt.Printf("    字段%d: %s = %s (inline: %t)\n", i+1, name, value, inline)
						}
					}
				} else {
					fmt.Printf("  ❌ fields类型断言失败，实际类型: %T\n", fieldsRaw)
				}
			} else {
				fmt.Println("  ❌ 没有找到fields字段")
			}

			if footer, ok := embed["footer"].(map[string]interface{}); ok {
				if text, ok := footer["text"].(string); ok {
					fmt.Printf("  页脚模板: %s\n", text)
				}
			}
		} else {
			fmt.Println("❌ 没有找到embed结构")
		}
	} else {
		fmt.Println("❌ 模板内容格式错误")
	}

	fmt.Println("\n🎯 结论:")
	fmt.Println("如果这里显示了5个字段，那么模板加载是正确的")
	fmt.Println("如果字段缺失，那么问题在模板加载过程")
}
