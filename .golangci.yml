# golangci-lint 配置文件
# 用于 Zeka Discord Bot Go 项目的代码质量检查
# 基础配置，符合最新版本要求

version: "2"

run:
  timeout: 5m
  issues-exit-code: 1
  tests: true

linters:
  enable:
    - bodyclose
    - dogsled
    - errcheck
    - goconst
    - gocritic
    - gocyclo
    - goprintffuncname
    - gosec
    - govet
    - ineffassign
    - lll
    - misspell
    - nakedret
    - revive
    - rowserrcheck
    - staticcheck
    - unconvert
    - unparam
    - unused
    - whitespace
  disable:
    - prealloc
    - depguard  # 过于严格，禁用
    - dupl      # 重复代码检查可能过于严格
    - funlen    # 函数长度检查可能过于严格
    - gochecknoinits  # init 函数检查可能不适用
    - mnd       # 魔法数字检查可能过于严格
  settings:
    gocyclo:
      min-complexity: 15
    dupl:
      threshold: 100
    goconst:
      min-len: 2
      min-occurrences: 2
    lll:
      line-length: 120
    funlen:
      lines: 100
      statements: 50

issues:
  max-issues-per-linter: 0
  max-same-issues: 0
