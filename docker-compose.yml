# Zeka项目 - 生产环境Docker Compose配置
services:
  # Zeka主应用
  zeka:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        VERSION: 1.0.0
        BUILD_TIME: ${BUILD_TIME:-$(date '+%Y-%m-%d %H:%M:%S')}
        GIT_COMMIT: ${GIT_COMMIT:-unknown}
    image: zeka:latest
    container_name: zeka-app
    restart: unless-stopped
    volumes:
      # 配置文件映射（只读）
      - ./configs:/app/configs:ro
      - ./.env:/app/.env:ro
      - ./logs:/app/logs:rw
      - ./data:/app/data:rw
    networks:
      - zeka-network

# 网络配置
networks:
  zeka-network:
    external: true


