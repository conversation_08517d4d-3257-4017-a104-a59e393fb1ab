#!/bin/bash

# Zeka项目基础设施 - Docker管理脚本

set -euo pipefail

# 配置
readonly PROJECT_NAME="zeka"
readonly COMPOSE_FILE="docker-compose.yml"
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m'

# 工具函数
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# 环境检查
check_environment() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker命令未找到，请先安装Docker"
        exit 1
    fi

    if ! command -v docker compose &> /dev/null; then
        log_error "Docker Compose命令未找到，请先安装"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行，请启动Docker"
        exit 1
    fi

    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log_error "Docker Compose文件不存在: $COMPOSE_FILE"
        exit 1
    fi
}

# 准备宿主机目录
prepare_host_directories() {
    log_info "准备宿主机目录..."

    # 创建必要的目录
    local dirs=("logs" "data")
    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            log_info "创建目录: $dir"
            mkdir -p "$dir"
        fi
        chmod 755 "$dir"
    done

    log_success "宿主机目录准备完成"
}

# 启动服务
start_services() {
    log_info "启动基础设施服务..."

    # 准备宿主机目录
    prepare_host_directories

    # 启动服务
    log_info "启动容器服务..."
    if ! docker compose -f "$COMPOSE_FILE" up -d; then
        log_error "服务启动失败"
        return 1
    fi

    log_success "基础设施服务已启动"
    show_status
}

# 停止服务
stop_services() {
    log_info "停止基础设施服务..."

    if ! docker compose -f "$COMPOSE_FILE" down; then
        log_error "服务停止失败"
        return 1
    fi

    log_success "基础设施服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启基础设施服务..."
    stop_services
    sleep 2
    start_services
}

# 查看状态
show_status() {
    echo
    log_info "服务状态:"
    docker compose -f "$COMPOSE_FILE" ps

    echo
    log_info "镜像信息:"
    docker images | grep -E "(rabbitmq|redis)" || log_warning "未找到基础设施镜像"
}

# 查看日志
show_logs() {
    echo
    log_info "选择要查看的服务日志:"
    echo "1) RabbitMQ"
    echo "2) Redis"
    echo "3) 所有服务"
    echo
    read -p "请选择 [1-3]: " choice

    case $choice in
        1)
            log_info "查看RabbitMQ服务日志 (最后100行，Ctrl+C 退出):"
            docker compose -f "$COMPOSE_FILE" logs --tail=100 -f rabbitmq
            ;;
        2)
            log_info "查看Redis服务日志 (最后100行，Ctrl+C 退出):"
            docker compose -f "$COMPOSE_FILE" logs --tail=100 -f redis
            ;;
        3)
            log_info "查看所有服务日志 (最后100行，Ctrl+C 退出):"
            docker compose -f "$COMPOSE_FILE" logs --tail=100 -f
            ;;
        *)
            log_warning "无效选择，显示所有服务日志"
            docker compose -f "$COMPOSE_FILE" logs --tail=100 -f
            ;;
    esac
}

# 清理资源
clean_docker() {
    log_info "清理基础设施Docker资源..."

    # 停止并删除容器、网络、卷
    log_info "停止并删除容器..."
    docker compose -f "$COMPOSE_FILE" down --volumes --remove-orphans 2>/dev/null || true

    # 强制停止和删除相关容器
    log_info "清理相关容器..."
    local containers=$(docker ps -aq --filter "name=rabbitmq" --filter "name=zeka-redis" 2>/dev/null || true)
    if [[ -n "$containers" ]]; then
        docker stop $containers 2>/dev/null || true
        docker rm $containers 2>/dev/null || true
    fi

    # 清理相关镜像
    log_info "清理相关镜像..."
    local images_to_remove=(
        "rabbitmq_delayed:latest"
        "redis:alpine"
    )

    for image in "${images_to_remove[@]}"; do
        if docker images "$image" -q 2>/dev/null | grep -q .; then
            log_info "删除镜像: $image"
            docker rmi "$image" 2>/dev/null || log_warning "删除镜像 $image 失败"
        fi
    done

    # 清理项目相关的卷
    log_info "清理项目卷..."
    local volumes_to_remove=(
        "server_rabbitmq-data"
        "server_redis-data"
    )

    for volume in "${volumes_to_remove[@]}"; do
        if docker volume ls -q | grep -q "$volume" 2>/dev/null; then
            log_info "删除卷: $volume"
            docker volume rm "$volume" 2>/dev/null || log_warning "删除卷 $volume 失败"
        fi
    done

    # 清理项目网络
    log_info "清理项目网络..."
    local network="server_zeka-network"
    if docker network ls -q --filter "name=$network" | grep -q .; then
        log_info "删除网络: $network"
        docker network rm "$network" 2>/dev/null || log_warning "删除网络 $network 失败"
    fi

    log_success "基础设施Docker资源清理完成"
}

# 显示菜单
show_menu() {
    while true; do
        echo
        echo -e "${CYAN}🐳 Zeka基础设施 Docker管理${NC}"
        echo -e "${CYAN}=============================${NC}"
        echo
        echo "1) 启动服务"
        echo "2) 停止服务"
        echo "3) 重启服务"
        echo "4) 查看状态"
        echo "5) 查看日志"
        echo "6) 清理资源"
        echo "0) 退出"
        echo
        read -p "请选择 [0-6]: " choice

        case $choice in
            1) start_services ;;
            2) stop_services ;;
            3) restart_services ;;
            4) show_status ;;
            5) show_logs ;;
            6)
                read -p "确认清理? [y/N]: " confirm
                [[ "$confirm" =~ ^[Yy]$ ]] && clean_docker
                ;;
            0) log_info "退出"; exit 0 ;;
            *) log_warning "无效选择" ;;
        esac

        [[ "$choice" != "5" ]] && read -p "按回车继续..."
    done
}

# 主函数
main() {
    # 解析参数
    case "${1:-}" in
        start) check_environment; start_services ;;
        stop) check_environment; stop_services ;;
        restart) check_environment; restart_services ;;
        status) check_environment; show_status ;;
        logs) check_environment; show_logs ;;
        clean) check_environment; clean_docker ;;
        *)
            echo -e "${CYAN}🐳 Zeka基础设施 Docker管理${NC}"
            echo -e "${CYAN}=============================${NC}"
            echo
            check_environment
            show_menu
            ;;
    esac
}

# 脚本入口
main "$@"
