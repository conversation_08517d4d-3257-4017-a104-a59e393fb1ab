FROM rabbitmq:latest

# 设置延迟消息插件版本
ENV RABBITMQ_DELAYED_MESSAGE_VERSION=4.1.0

# 安装curl（如果镜像中没有）
USER root
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# 下载延迟消息插件
RUN curl -L https://github.com/rabbitmq/rabbitmq-delayed-message-exchange/releases/download/v${RABBITMQ_DELAYED_MESSAGE_VERSION}/rabbitmq_delayed_message_exchange-${RABBITMQ_DELAYED_MESSAGE_VERSION}.ez \
    -o /opt/rabbitmq/plugins/rabbitmq_delayed_message_exchange-${RABBITMQ_DELAYED_MESSAGE_VERSION}.ez

# 设置插件文件权限
RUN chmod 644 /opt/rabbitmq/plugins/rabbitmq_delayed_message_exchange-${RABBITMQ_DELAYED_MESSAGE_VERSION}.ez

# 启用管理插件
RUN rabbitmq-plugins enable rabbitmq_management --offline

# 启用延迟消息插件  
RUN rabbitmq-plugins enable rabbitmq_delayed_message_exchange --offline

# 切换回rabbitmq用户
USER rabbitmq

# 暴露端口
EXPOSE 5672 15672

# 使用默认启动命令
CMD ["rabbitmq-server"] 