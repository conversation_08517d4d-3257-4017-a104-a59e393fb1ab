# 使用Docker Compose V2格式
# 直接使用 docker compose 命令启动
# 兼容Docker v20.10.0+
services:
  # RabbitMQ消息队列，启用延迟队列插件
  rabbitmq:
    build:
      context: ./rabbitmq
      dockerfile: Dockerfile
    image: rabbitmq_delayed:latest
    container_name: rabbitmq
    hostname: rabbitmq
    restart: always
    environment:
      - RABBITMQ_DEFAULT_USER=zeka
      - RABBITMQ_DEFAULT_PASS=cYrje8miFbhruQp
    ports:
      - "5672:5672"   # AMQP 协议端口
      - "15672:15672" # 管理界面端口
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - zeka-network

  # Redis缓存服务
  redis:
    image: redis:alpine
    container_name: zeka-redis
    hostname: redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - zeka-network

volumes:
  rabbitmq-data:
  redis-data:

networks:
  zeka-network:
    # external: true