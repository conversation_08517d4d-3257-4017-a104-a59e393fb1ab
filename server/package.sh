#!/bin/bash

# Zeka基础设施打包脚本
# 用于打包 Docker 相关文件以便部署

set -euo pipefail

# 配置
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_NAME="zeka-infrastructure"
readonly PACKAGE_NAME="${PROJECT_NAME}.tar.gz"
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m'

# 工具函数
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# 需要打包的文件和目录列表
readonly FILES_TO_PACKAGE=(
    "rabbitmq"
    "docker-compose.yml"
    "start-docker.sh"
)

# 检查必要文件是否存在
check_files() {
    log_info "检查必要文件..."
    
    local missing_files=()
    
    for file in "${FILES_TO_PACKAGE[@]}"; do
        local file_path="${SCRIPT_DIR}/${file}"
        if [[ ! -e "$file_path" ]]; then
            missing_files+=("$file")
        else
            if [[ -d "$file_path" ]]; then
                log_info "✓ 目录存在: $file"
            else
                log_info "✓ 文件存在: $file"
            fi
        fi
    done
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        log_error "以下文件或目录不存在:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        return 1
    fi
    
    log_success "所有必要文件检查完成"
}

# 创建打包
create_package() {
    log_info "开始创建打包文件..."
    
    # 切换到 server 目录
    cd "$SCRIPT_DIR"
    
    # 创建临时目录列表文件
    local temp_file_list=$(mktemp)
    
    # 将要打包的文件写入临时文件
    for file in "${FILES_TO_PACKAGE[@]}"; do
        echo "$file" >> "$temp_file_list"
    done
    
    # 创建 tar.gz 包
    log_info "正在压缩文件..."
    if tar -czf "$PACKAGE_NAME" -T "$temp_file_list"; then
        log_success "打包完成: $PACKAGE_NAME"
    else
        log_error "打包失败"
        rm -f "$temp_file_list"
        return 1
    fi
    
    # 清理临时文件
    rm -f "$temp_file_list"
    
    # 显示包信息
    local package_size=$(du -h "$PACKAGE_NAME" | cut -f1)
    log_info "包大小: $package_size"
    log_info "包路径: ${SCRIPT_DIR}/${PACKAGE_NAME}"
}

# 生成解压说明
generate_instructions() {
    local instructions_file="${SCRIPT_DIR}/解压说明.txt"
    
    cat > "$instructions_file" << EOF
Zeka基础设施部署包解压说明
================================

包文件: $PACKAGE_NAME
创建时间: $(date)

解压方法:
--------
1. 将 $PACKAGE_NAME 上传到目标服务器

2. 在目标目录执行解压命令:
   tar -xzf $PACKAGE_NAME

3. 解压后的目录结构:
   ./rabbitmq/          # RabbitMQ配置目录
   ./docker-compose.yml # Docker Compose配置文件
   ./start-docker.sh    # Docker管理脚本

4. 设置脚本执行权限:
   chmod +x start-docker.sh

5. 启动服务:
   ./start-docker.sh start

注意事项:
--------
- 确保目标服务器已安装 Docker 和 Docker Compose
- 解压时会在当前目录创建文件，请确保在合适的位置解压
- 如需修改配置，请编辑 docker-compose.yml 文件
- 使用 ./start-docker.sh 查看所有可用命令

联系信息:
--------
如有问题，请联系系统管理员。
EOF

    log_success "解压说明已生成: $instructions_file"
}

# 显示完成信息
show_completion_info() {
    echo
    log_success "打包完成！"
    echo
    echo -e "${CYAN}📦 打包信息${NC}"
    echo -e "${CYAN}============${NC}"
    echo "包文件名: $PACKAGE_NAME"
    echo "包大小: $(du -h "$PACKAGE_NAME" | cut -f1)"
    echo "包路径: ${SCRIPT_DIR}/${PACKAGE_NAME}"
    echo
    echo -e "${CYAN}📋 包含文件${NC}"
    echo -e "${CYAN}============${NC}"
    for file in "${FILES_TO_PACKAGE[@]}"; do
        echo "  ✓ $file"
    done
    echo
    echo -e "${CYAN}🚀 部署步骤${NC}"
    echo -e "${CYAN}============${NC}"
    echo "1. 上传 $PACKAGE_NAME 到目标服务器"
    echo "2. 在目标目录执行: tar -xzf $PACKAGE_NAME"
    echo "3. 设置权限: chmod +x start-docker.sh"
    echo "4. 启动服务: ./start-docker.sh start"
    echo
}

# 主函数
main() {
    echo -e "${CYAN}🐳 Zeka基础设施打包工具${NC}"
    echo -e "${CYAN}=========================${NC}"
    echo
    
    # 检查文件
    if ! check_files; then
        exit 1
    fi
    
    # 创建打包
    if ! create_package; then
        exit 1
    fi
    
    # 生成说明
    generate_instructions
    
    # 显示完成信息
    show_completion_info
}

# 脚本入口
main "$@"
