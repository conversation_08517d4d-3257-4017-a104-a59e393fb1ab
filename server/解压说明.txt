Zeka基础设施部署包解压说明
================================

包文件: zeka-infrastructure.tar.gz
创建时间: 2025年 7月19日 星期六 06时31分35秒 CST

解压方法:
--------
1. 将 zeka-infrastructure.tar.gz 上传到目标服务器

2. 在目标目录执行解压命令:
   tar -xzf zeka-infrastructure.tar.gz

3. 解压后的目录结构:
   ./rabbitmq/          # RabbitMQ配置目录
   ./docker-compose.yml # Docker Compose配置文件
   ./start-docker.sh    # Docker管理脚本

4. 设置脚本执行权限:
   chmod +x start-docker.sh

5. 启动服务:
   ./start-docker.sh start

注意事项:
--------
- 确保目标服务器已安装 Docker 和 Docker Compose
- 解压时会在当前目录创建文件，请确保在合适的位置解压
- 如需修改配置，请编辑 docker-compose.yml 文件
- 使用 ./start-docker.sh 查看所有可用命令

联系信息:
--------
如有问题，请联系系统管理员。
