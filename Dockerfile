# Zeka项目 - 简化Docker镜像
# 使用预编译的二进制文件

FROM alpine:3.19

# 安装运行时依赖
RUN apk add --no-cache \
    ca-certificates \
    tzdata \
    && rm -rf /var/cache/apk/*

# 创建非root用户
RUN addgroup -g 1001 -S zeka && \
    adduser -u 1001 -S zeka -G zeka

# 设置工作目录
WORKDIR /app

# 创建必要的目录并设置权限
RUN mkdir -p /app/configs /app/logs /app/data && \
    chown -R zeka:zeka /app && \
    chmod -R 775 /app/logs /app/data

# 复制预编译的二进制文件
COPY --chown=zeka:zeka zeka /app/zeka

# 复制配置文件
COPY --chown=zeka:zeka configs/ /app/configs/

# 复制启动脚本
COPY --chown=zeka:zeka entrypoint.sh /app/entrypoint.sh

# 最终权限确认
RUN chown -R zeka:zeka /app && \
    chmod -R 775 /app/logs /app/data && \
    chmod +x /app/entrypoint.sh

# 设置时区
ENV TZ=Asia/Shanghai

# 切换到非root用户
USER zeka

# 设置启动脚本和默认命令
ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["/app/zeka"]
