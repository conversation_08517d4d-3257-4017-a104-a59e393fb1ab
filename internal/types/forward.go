package types

import (
	"fmt"
	"strings"
	"time"
)

// ForwardRule 转发规则结构
// 基于 docs/enhanced_system_design_v3.md 中的设计规范
type ForwardRule struct {
	// 基础信息
	Name        string `yaml:"name" json:"name"`
	Description string `yaml:"description" json:"description"`
	Enabled     bool   `yaml:"enabled" json:"enabled"` // 强制设置，默认为true

	// 源和目标频道（遵循设计文档字段名）
	InputChannel  string `yaml:"input_channel" json:"input_channel"`
	OutputChannel string `yaml:"output_channel" json:"output_channel"`

	// 向后兼容字段（内部使用）
	SourceChannelID string `yaml:"source_channel_id,omitempty" json:"source_channel_id,omitempty"`
	TargetChannelID string `yaml:"target_channel_id,omitempty" json:"target_channel_id,omitempty"`

	// 频道名称缓存（遵循设计文档）
	InputChannelName  string `yaml:"input_channel_name,omitempty" json:"input_channel_name,omitempty"`   // 输入频道名称
	OutputChannelName string `yaml:"output_channel_name,omitempty" json:"output_channel_name,omitempty"` // 输出频道名称

	// 字段映射组
	FieldMappingGroup string `yaml:"field_mapping_group" json:"field_mapping_group"`

	// 转发配置
	ForwardConfig ForwardConfig `yaml:"forward_config" json:"forward_config"`

	// 过滤配置
	FilterConfig FilterConfig `yaml:"filter_config" json:"filter_config"`

	// 元数据
	Metadata ForwardRuleMetadata `yaml:"metadata" json:"metadata"`

	// 统计信息
	Stats ForwardRuleStats `yaml:"stats" json:"stats"`
}

// ForwardConfig 转发配置
type ForwardConfig struct {
	// 转发模式：direct（直接转发）、transform（转换后转发）
	Mode string `yaml:"mode" json:"mode"`

	// 延迟设置（秒）
	DelaySeconds int `yaml:"delay_seconds" json:"delay_seconds"`

	// 是否保留原始Embed
	PreserveOriginalEmbeds bool `yaml:"preserve_original_embeds" json:"preserve_original_embeds"`

	// 是否添加来源信息
	AddSourceInfo bool `yaml:"add_source_info" json:"add_source_info"`

	// 自定义模板
	CustomTemplate *string `yaml:"custom_template,omitempty" json:"custom_template,omitempty"`

	// 转发限制
	RateLimit *RateLimit `yaml:"rate_limit,omitempty" json:"rate_limit,omitempty"`
}

// FilterConfig 过滤配置
type FilterConfig struct {
	// 是否启用过滤
	Enabled bool `yaml:"enabled" json:"enabled"`

	// 关键词过滤
	Keywords []string `yaml:"keywords" json:"keywords"`

	// 关键词模式：whitelist（白名单）、blacklist（黑名单）
	KeywordMode string `yaml:"keyword_mode" json:"keyword_mode"`

	// 最小内容长度
	MinContentLength int `yaml:"min_content_length" json:"min_content_length"`

	// 最大内容长度
	MaxContentLength int `yaml:"max_content_length" json:"max_content_length"`

	// 是否过滤机器人消息
	FilterBotMessages bool `yaml:"filter_bot_messages" json:"filter_bot_messages"`

	// 自定义过滤规则
	CustomFilters []CustomFilter `yaml:"custom_filters" json:"custom_filters"`
}

// CustomFilter 自定义过滤规则
type CustomFilter struct {
	Name        string `yaml:"name" json:"name"`
	Type        string `yaml:"type" json:"type"` // regex, contains, equals, etc.
	Pattern     string `yaml:"pattern" json:"pattern"`
	Field       string `yaml:"field" json:"field"`   // title, description, content, etc.
	Action      string `yaml:"action" json:"action"` // allow, deny
	Description string `yaml:"description" json:"description"`
}

// RateLimit 速率限制
type RateLimit struct {
	MaxMessages int           `yaml:"max_messages" json:"max_messages"`
	TimeWindow  time.Duration `yaml:"time_window" json:"time_window"`
}

// ForwardRuleMetadata 转发规则元数据
type ForwardRuleMetadata struct {
	CreatedAt  time.Time              `yaml:"created_at" json:"created_at"`
	UpdatedAt  time.Time              `yaml:"updated_at" json:"updated_at"`
	CreatedBy  string                 `yaml:"created_by" json:"created_by"`
	UpdatedBy  string                 `yaml:"updated_by" json:"updated_by"`
	Version    int                    `yaml:"version" json:"version"`
	Tags       []string               `yaml:"tags" json:"tags"`
	CustomData map[string]interface{} `yaml:"custom_data" json:"custom_data"`
}

// ForwardRuleStats 转发规则统计
type ForwardRuleStats struct {
	TotalMessages     int64     `json:"total_messages"`
	ForwardedMessages int64     `json:"forwarded_messages"`
	FilteredMessages  int64     `json:"filtered_messages"`
	ErrorMessages     int64     `json:"error_messages"`
	LastForwardTime   time.Time `json:"last_forward_time"`
	LastErrorTime     time.Time `json:"last_error_time"`
	LastError         string    `json:"last_error"`
}

// ForwardRulesMainConfig 转发规则主配置结构
type ForwardRulesMainConfig struct {
	ForwardRules   ForwardRulesSettings  `yaml:"forward_rules" json:"forward_rules"`
	GlobalSettings GlobalForwardSettings `yaml:"global_settings,omitempty" json:"global_settings,omitempty"`
}

// ForwardRulesSettings 转发规则设置
type ForwardRulesSettings struct {
	Enabled                bool          `yaml:"enabled" json:"enabled"`
	FieldMappingGroupsFile string        `yaml:"field_mapping_groups_file" json:"field_mapping_groups_file"`
	Rules                  []ForwardRule `yaml:"rules" json:"rules"`
}

// ForwardRulesConfig 转发规则配置文件结构（向后兼容）
type ForwardRulesConfig struct {
	ForwardRules   []ForwardRule         `yaml:"forward_rules,omitempty" json:"forward_rules,omitempty"`
	GlobalSettings GlobalForwardSettings `yaml:"global_settings,omitempty" json:"global_settings,omitempty"`
}

// GlobalForwardSettings 全局转发设置
type GlobalForwardSettings struct {
	// 默认字段映射组
	DefaultFieldMappingGroup string `yaml:"default_field_mapping_group" json:"default_field_mapping_group"`

	// 默认延迟（秒）
	DefaultDelaySeconds int `yaml:"default_delay_seconds" json:"default_delay_seconds"`

	// 全局速率限制
	GlobalRateLimit *RateLimit `yaml:"global_rate_limit,omitempty" json:"global_rate_limit,omitempty"`

	// 错误处理
	ErrorHandling ErrorHandlingConfig `yaml:"error_handling" json:"error_handling"`

	// 监控设置
	Monitoring MonitoringConfig `yaml:"monitoring" json:"monitoring"`
}

// ErrorHandlingConfig 错误处理配置
type ErrorHandlingConfig struct {
	MaxRetries     int           `yaml:"max_retries" json:"max_retries"`
	RetryDelay     time.Duration `yaml:"retry_delay" json:"retry_delay"`
	LogErrors      bool          `yaml:"log_errors" json:"log_errors"`
	NotifyOnError  bool          `yaml:"notify_on_error" json:"notify_on_error"`
	ErrorChannelID string        `yaml:"error_channel_id" json:"error_channel_id"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Enabled             bool          `yaml:"enabled" json:"enabled"`
	StatsInterval       time.Duration `yaml:"stats_interval" json:"stats_interval"`
	HealthCheckInterval time.Duration `yaml:"health_check_interval" json:"health_check_interval"`
	MetricsEnabled      bool          `yaml:"metrics_enabled" json:"metrics_enabled"`
}

// ForwardRuleManager 转发规则管理器接口
type ForwardRuleManager interface {
	// 规则管理
	AddRule(rule *ForwardRule) error
	RemoveRule(ruleName string) error
	UpdateRule(rule *ForwardRule) error
	GetRule(ruleName string) (*ForwardRule, error)
	ListRules() []*ForwardRule

	// 规则查询
	GetRulesBySourceChannel(channelID string) []*ForwardRule
	GetRulesByTargetChannel(channelID string) []*ForwardRule

	// 规则执行
	ShouldForward(rule *ForwardRule, message interface{}) (bool, error)
	ForwardMessage(rule *ForwardRule, message interface{}) error

	// 统计和监控
	GetRuleStats(ruleName string) (*ForwardRuleStats, error)
	UpdateRuleStats(ruleName string, stats *ForwardRuleStats) error
}

// Validate 验证转发规则
func (fr *ForwardRule) Validate() error {
	// 自动生成名称（如果为空）
	if fr.Name == "" {
		fr.Name = GenerateAutoName(fr.GetSourceChannelID(), fr.GetTargetChannelID())
	}

	// 检查频道ID（支持新旧字段名）
	sourceID := fr.GetSourceChannelID()
	targetID := fr.GetTargetChannelID()

	if sourceID == "" {
		return fmt.Errorf("源频道ID不能为空")
	}

	if targetID == "" {
		return fmt.Errorf("目标频道ID不能为空")
	}

	if sourceID == targetID {
		return fmt.Errorf("源频道和目标频道不能相同")
	}

	// 验证转发模式
	validModes := []string{"direct", "transform"}
	if fr.ForwardConfig.Mode != "" {
		found := false
		for _, mode := range validModes {
			if fr.ForwardConfig.Mode == mode {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("无效的转发模式: %s", fr.ForwardConfig.Mode)
		}
	}

	// 验证关键词模式
	if fr.FilterConfig.Enabled && fr.FilterConfig.KeywordMode != "" {
		validKeywordModes := []string{"whitelist", "blacklist"}
		found := false
		for _, mode := range validKeywordModes {
			if fr.FilterConfig.KeywordMode == mode {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("无效的关键词模式: %s", fr.FilterConfig.KeywordMode)
		}
	}

	return nil
}

// GetSourceChannelID 获取源频道ID（支持新旧字段名）
func (fr *ForwardRule) GetSourceChannelID() string {
	if fr.InputChannel != "" {
		return fr.InputChannel
	}
	return fr.SourceChannelID
}

// GetTargetChannelID 获取目标频道ID（支持新旧字段名）
func (fr *ForwardRule) GetTargetChannelID() string {
	if fr.OutputChannel != "" {
		return fr.OutputChannel
	}
	return fr.TargetChannelID
}

// SetChannelIDs 设置频道ID（同时设置新旧字段）
func (fr *ForwardRule) SetChannelIDs(sourceID, targetID string) {
	fr.InputChannel = sourceID
	fr.OutputChannel = targetID
	fr.SourceChannelID = sourceID // 向后兼容
	fr.TargetChannelID = targetID // 向后兼容
}

// GenerateAutoName 生成自动命名（向后兼容，使用频道ID）
func GenerateAutoName(sourceChannelID, targetChannelID string) string {
	// 简化频道ID（取后6位）
	sourceShort := sourceChannelID
	if len(sourceChannelID) > 6 {
		sourceShort = sourceChannelID[len(sourceChannelID)-6:]
	}

	targetShort := targetChannelID
	if len(targetChannelID) > 6 {
		targetShort = targetChannelID[len(targetChannelID)-6:]
	}

	return fmt.Sprintf("forward_%s_to_%s", sourceShort, targetShort)
}

// GenerateAutoNameWithChannelNames 生成基于频道名称的自动命名（遵循设计文档）
func GenerateAutoNameWithChannelNames(sourceChannelName, targetChannelName string) string {
	// 清理频道名称
	sourceName := cleanChannelNameForRule(sourceChannelName)
	targetName := cleanChannelNameForRule(targetChannelName)

	return fmt.Sprintf("%s_to_%s", sourceName, targetName)
}

// cleanChannelNameForRule 清理频道名称用于规则命名
func cleanChannelNameForRule(name string) string {
	// 移除特殊字符，只保留字母、数字、下划线和连字符
	result := ""
	for _, r := range name {
		if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') ||
			(r >= '0' && r <= '9') || r == '_' || r == '-' {
			result += string(r)
		}
	}

	// 如果结果为空，使用默认名称
	if result == "" {
		result = "channel"
	}

	// 限制长度
	if len(result) > 20 {
		result = result[:20]
	}

	return result
}

// IsKeywordMatch 检查关键词匹配
func (fr *ForwardRule) IsKeywordMatch(content string) bool {
	if !fr.FilterConfig.Enabled || len(fr.FilterConfig.Keywords) == 0 {
		return true // 没有启用过滤或没有关键词，默认匹配
	}

	content = strings.ToLower(content)

	for _, keyword := range fr.FilterConfig.Keywords {
		keyword = strings.ToLower(keyword)
		if strings.Contains(content, keyword) {
			// 找到匹配的关键词
			if fr.FilterConfig.KeywordMode == "whitelist" {
				return true // 白名单模式，匹配则通过
			} else {
				return false // 黑名单模式，匹配则拒绝
			}
		}
	}

	// 没有找到匹配的关键词
	if fr.FilterConfig.KeywordMode == "whitelist" {
		return false // 白名单模式，不匹配则拒绝
	} else {
		return true // 黑名单模式，不匹配则通过
	}
}

// IsContentLengthValid 检查内容长度是否有效
func (fr *ForwardRule) IsContentLengthValid(content string) bool {
	if !fr.FilterConfig.Enabled {
		return true
	}

	length := len(content)

	if fr.FilterConfig.MinContentLength > 0 && length < fr.FilterConfig.MinContentLength {
		return false
	}

	if fr.FilterConfig.MaxContentLength > 0 && length > fr.FilterConfig.MaxContentLength {
		return false
	}

	return true
}

// UpdateStats 更新统计信息
func (fr *ForwardRule) UpdateStats(messageType string) {
	fr.Stats.TotalMessages++

	switch messageType {
	case "forwarded":
		fr.Stats.ForwardedMessages++
		fr.Stats.LastForwardTime = time.Now()
	case "filtered":
		fr.Stats.FilteredMessages++
	case "error":
		fr.Stats.ErrorMessages++
		fr.Stats.LastErrorTime = time.Now()
	}
}
