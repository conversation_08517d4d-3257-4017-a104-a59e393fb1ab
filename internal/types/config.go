package types

import (
	"fmt"
	"time"
)

// Config 主配置结构
type Config struct {
	Environment    string               `mapstructure:"environment" json:"environment"`
	Discord        DiscordConfig        `mapstructure:"discord" json:"discord"`
	Redis          RedisConfig          `mapstructure:"redis" json:"redis"`
	Queue          QueueConfig          `mapstructure:"queue" json:"queue"`
	Scheduler      SchedulerConfig      `mapstructure:"scheduler" json:"scheduler"`
	API            APIConfig            `mapstructure:"api" json:"api"`
	Database       DatabaseConfig       `mapstructure:"database" json:"database"`
	Logger         LoggerConfig         `mapstructure:"logger" json:"logger"`
	Colors         ColorConfig          `mapstructure:"colors" json:"colors"`
	Cooldowns      CooldownConfig       `mapstructure:"cooldowns" json:"cooldowns"`
	StatusMessages []StatusMessage      `mapstructure:"status_messages" json:"status_messages"`
	Permissions    PermissionConfig     `mapstructure:"permissions" json:"permissions"`
	ReactionFilter ReactionFilterConfig `mapstructure:"reaction_filter" json:"reaction_filter"`
}

// DiscordConfig Discord 相关配置
type DiscordConfig struct {
	Token    string   `mapstructure:"token" json:"token" validate:"required"`
	ClientID string   `mapstructure:"client_id" json:"client_id" validate:"required"`
	Prefix   string   `mapstructure:"prefix" json:"prefix"`
	GuildID  string   `mapstructure:"guild_id" json:"guild_id"`
	OwnerID  string   `mapstructure:"owner_id" json:"owner_id"`
	Owners   []string `mapstructure:"owners" json:"owners"`
}

// RedisConfig Redis 配置
type RedisConfig struct {
	Host     string `mapstructure:"host" json:"host"`
	Port     int    `mapstructure:"port" json:"port"`
	Password string `mapstructure:"password" json:"password"`
	DB       int    `mapstructure:"db" json:"db"`
	Prefix   string `mapstructure:"prefix" json:"prefix"`
}

// QueueConfig 队列配置
type QueueConfig struct {
	URL                       string         `mapstructure:"url" json:"url"`
	PrefetchCount             int            `mapstructure:"prefetch_count" json:"prefetch_count"`
	MaxRetries                int            `mapstructure:"max_retries" json:"max_retries"`
	HeartbeatInterval         time.Duration  `mapstructure:"heartbeat_interval" json:"heartbeat_interval"`
	ReconnectDelay            time.Duration  `mapstructure:"reconnect_delay" json:"reconnect_delay"`
	ConnectionTimeout         time.Duration  `mapstructure:"connection_timeout" json:"connection_timeout"`
	EnableGracefulDegradation bool           `mapstructure:"enable_graceful_degradation" json:"enable_graceful_degradation"`
	RequiredForStartup        bool           `mapstructure:"required_for_startup" json:"required_for_startup"`
	DefaultExchange           ExchangeConfig `mapstructure:"default_exchange" json:"default_exchange"`
}

// ExchangeConfig 交换机配置
type ExchangeConfig struct {
	Name    string          `mapstructure:"name" json:"name"`
	Type    string          `mapstructure:"type" json:"type"`
	Options ExchangeOptions `mapstructure:"options" json:"options"`
}

// ExchangeOptions 交换机选项
type ExchangeOptions struct {
	Durable    bool `mapstructure:"durable" json:"durable"`
	AutoDelete bool `mapstructure:"auto_delete" json:"auto_delete"`
}

// APIConfig API 配置
type APIConfig struct {
	Host     string `mapstructure:"host" json:"host"`
	Port     int    `mapstructure:"port" json:"port"`
	APIKey   string `mapstructure:"api_key" json:"api_key"`
	AdminKey string `mapstructure:"admin_key" json:"admin_key"`
	Enabled  bool   `mapstructure:"enabled" json:"enabled"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver          string `mapstructure:"driver" json:"driver"`
	Host            string `mapstructure:"host" json:"host"`
	Port            int    `mapstructure:"port" json:"port"`
	Username        string `mapstructure:"username" json:"username"`
	Password        string `mapstructure:"password" json:"password"`
	Database        string `mapstructure:"database" json:"database"`
	SSLMode         string `mapstructure:"ssl_mode" json:"ssl_mode"`
	MaxIdleConns    int    `mapstructure:"max_idle_conns" json:"max_idle_conns"`
	MaxOpenConns    int    `mapstructure:"max_open_conns" json:"max_open_conns"`
	ConnMaxLifetime string `mapstructure:"conn_max_lifetime" json:"conn_max_lifetime"`
	LogLevel        string `mapstructure:"log_level" json:"log_level"`
	Enabled         bool   `mapstructure:"enabled" json:"enabled"`
}

// LoggerConfig 日志配置
type LoggerConfig struct {
	Level   string           `mapstructure:"level" json:"level"`
	Format  string           `mapstructure:"format" json:"format"`
	Discord DiscordLogConfig `mapstructure:"discord" json:"discord"`
	File    FileLogConfig    `mapstructure:"file" json:"file"`
}

// DiscordLogConfig Discord 日志配置
type DiscordLogConfig struct {
	Enabled   bool   `mapstructure:"enabled" json:"enabled"`
	ChannelID string `mapstructure:"channel_id" json:"channel_id"`
	Level     string `mapstructure:"level" json:"level"`
}

// FileLogConfig 文件日志配置
type FileLogConfig struct {
	Enabled    bool   `mapstructure:"enabled" json:"enabled"`
	Path       string `mapstructure:"path" json:"path"`
	MaxSize    int    `mapstructure:"max_size" json:"max_size"`
	MaxBackups int    `mapstructure:"max_backups" json:"max_backups"`
	MaxAge     int    `mapstructure:"max_age" json:"max_age"`
	Compress   bool   `mapstructure:"compress" json:"compress"`
}

// ColorConfig 颜色配置
type ColorConfig struct {
	Primary string `mapstructure:"primary" json:"primary"`
	Success string `mapstructure:"success" json:"success"`
	Warning string `mapstructure:"warning" json:"warning"`
	Error   string `mapstructure:"error" json:"error"`
	Info    string `mapstructure:"info" json:"info"`
}

// CooldownConfig 冷却时间配置
type CooldownConfig struct {
	Default  time.Duration            `mapstructure:"default" json:"default"`
	Commands map[string]time.Duration `mapstructure:"commands" json:"commands"`
}

// StatusMessage 状态消息配置
type StatusMessage struct {
	Type    string `mapstructure:"type" json:"type"`
	Content string `mapstructure:"content" json:"content"`
}

// Validate 验证配置
func (c *Config) Validate() error {
	// 在生产环境下，Discord Token 和 Client ID 是必需的
	if c.Environment == "production" {
		if c.Discord.Token == "" {
			return ErrMissingDiscordToken
		}
		if c.Discord.ClientID == "" {
			return ErrMissingClientID
		}
	} else {
		// 在开发环境下，如果 Token 和 Client ID 都为空，给出警告信息
		if c.Discord.Token == "" && c.Discord.ClientID == "" {
			// 这里不返回错误，而是允许程序继续运行
			// 实际的 Discord 连接会在后续步骤中处理
		}
	}

	return nil
}

// GetRedisAddr 获取 Redis 地址
func (r *RedisConfig) GetRedisAddr() string {
	if r.Host == "" {
		return ""
	}
	return fmt.Sprintf("%s:%d", r.Host, r.Port)
}

// IsEnabled 检查 Redis 是否启用
func (r *RedisConfig) IsEnabled() bool {
	return r.Host != "" && r.Port > 0
}

// IsEnabled 检查队列是否启用
func (q *QueueConfig) IsEnabled() bool {
	return q.URL != ""
}

// SchedulerConfig 调度器配置
type SchedulerConfig struct {
	Workers         int           `mapstructure:"workers" json:"workers"`
	QueueSize       int           `mapstructure:"queue_size" json:"queue_size"`
	JobTimeout      time.Duration `mapstructure:"job_timeout" json:"job_timeout"`
	MaxRetries      int           `mapstructure:"max_retries" json:"max_retries"`
	CleanupInterval time.Duration `mapstructure:"cleanup_interval" json:"cleanup_interval"`
	Enabled         bool          `mapstructure:"enabled" json:"enabled"`
}

// IsEnabled 检查调度器是否启用
func (s *SchedulerConfig) IsEnabled() bool {
	return s.Enabled
}

// IsEnabled 检查 API 是否启用
func (a *APIConfig) IsEnabled() bool {
	return a.Enabled
}

// IsEnabled 检查数据库是否启用
func (d *DatabaseConfig) IsEnabled() bool {
	return d.Enabled
}

// PermissionConfig 权限配置
type PermissionConfig struct {
	BotOwners             []string                     `mapstructure:"bot_owners" json:"bot_owners"`
	EnablePermissionCheck bool                         `mapstructure:"enable_permission_check" json:"enable_permission_check"`
	CommandPermissions    map[string]CommandPermission `mapstructure:"command_permissions" json:"command_permissions"`
	ReactionPermissions   ReactionPermission           `mapstructure:"reaction_permissions" json:"reaction_permissions"`
}

// CommandPermission 命令权限配置
type CommandPermission struct {
	AllowedRoles        []string `mapstructure:"allowed_roles" json:"allowed_roles"`
	AllowedUsers        []string `mapstructure:"allowed_users" json:"allowed_users"`
	FallbackPermissions []string `mapstructure:"fallback_permissions" json:"fallback_permissions"`
}

// ReactionPermission 表情反应权限配置
type ReactionPermission struct {
	AllowedRoles        []string `mapstructure:"allowed_roles" json:"allowed_roles"`
	AllowedUsers        []string `mapstructure:"allowed_users" json:"allowed_users"`
	FallbackPermissions []string `mapstructure:"fallback_permissions" json:"fallback_permissions"`
}

// ReactionFilterConfig 表情反应过滤配置（遵循设计文档）
type ReactionFilterConfig struct {
	Enabled        bool     `mapstructure:"enabled" json:"enabled"`                 // 是否启用表情反应过滤
	WhitelistEmoji string   `mapstructure:"whitelist_emoji" json:"whitelist_emoji"` // 默认: ✅
	BlacklistEmoji string   `mapstructure:"blacklist_emoji" json:"blacklist_emoji"` // 默认: ❌
	AdminRoles     []string `mapstructure:"admin_roles" json:"admin_roles"`         // 管理员角色ID
	AdminUsers     []string `mapstructure:"admin_users" json:"admin_users"`         // 管理员用户ID
}
