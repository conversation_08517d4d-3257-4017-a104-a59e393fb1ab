package types

import (
	"strings"
)

// ProductItem 统一的产品信息模型
// 基于设计文档enhanced_system_design_v3.md中的规范
// 兼容现有的ProductData结构，并扩展支持Discord Embed字段
type ProductItem struct {
	// 核心产品信息
	Title       string  `json:"title"`                 // 产品标题
	URL         string  `json:"url"`                   // 产品链接
	Description *string `json:"description,omitempty"` // embed.Description
	Color       *int    `json:"color,omitempty"`       // embed.Color

	ProductID string `json:"productId"` // 产品ID (兼容现有ProductData)
	Price     string `json:"price"`     // 合并价格和货币：$19.99, €15.50, ¥1200

	// Embed图片信息
	ImageURL     *string `json:"image_url,omitempty"`     // embed.Image.URL (兼容现有ImageURL)
	ThumbnailURL *string `json:"thumbnail_url,omitempty"` // embed.Thumbnail.URL

	// Embed页脚和作者信息
	FooterText    *string `json:"footer_text,omitempty"`     // embed.Footer.Text
	FooterIconURL *string `json:"footer_icon_url,omitempty"` // embed.Footer.IconURL
	AuthorName    *string `json:"author_name,omitempty"`     // embed.Author.Name
	AuthorURL     *string `json:"author_url,omitempty"`      // embed.Author.URL
	AuthorIconURL *string `json:"author_icon_url,omitempty"` // embed.Author.IconURL

	// 库存和可用性
	Stock        int    `json:"stock"`        // 库存数量
	Availability string `json:"availability"` // 可用性状态

	// 可选产品信息 (兼容现有ProductData)
	SkuID       *string `json:"skuId,omitempty"`       // SKU ID
	OfferID     *string `json:"offerId,omitempty"`     // 优惠ID
	Addition    *string `json:"addition,omitempty"`    // 附加信息
	ReleaseDate *string `json:"releaseDate,omitempty"` // 发布日期
	AtcLink     *string `json:"atcLink,omitempty"`     // 加购链接

	// 平台信息 (兼容现有ProductData)
	Platform string `json:"platform,omitempty"` // 平台名称

	Metadata map[string]interface{} `json:"metadata"` // 扩展元数据
	// 时间戳
	Timestamp *string `json:"timestamp,omitempty"` // embed.Timestamp
}

// ProductExtractor 产品信息提取器
// 负责从Discord消息中提取产品信息，利用现有的extractMessageContent机制
type ProductExtractor struct {
	fieldMappings map[string]string // embed字段到产品字段的映射
}

// NewProductExtractor 创建产品信息提取器
func NewProductExtractor() *ProductExtractor {
	return &ProductExtractor{
		fieldMappings: make(map[string]string),
	}
}

// ExtractFromDiscordMessage 从Discord消息中提取产品信息
// 集成现有的extractMessageContent机制，支持完整的Discord消息处理
func (pe *ProductExtractor) ExtractFromDiscordMessage(message interface{}, extractedContent string) *ProductItem {
	product := &ProductItem{
		Metadata:     make(map[string]interface{}),
		Stock:        0,
		Availability: "unknown",
	}

	// 如果有提取的内容，使用作为标题
	if extractedContent != "" {
		product.Title = extractedContent
	}

	// 尝试从不同类型的消息中提取信息
	switch msg := message.(type) {
	case map[string]interface{}:
		// 处理序列化后的消息数据
		pe.extractFromMessageMap(product, msg)
	case []map[string]interface{}:
		// 处理Embed数组
		pe.extractFromEmbeds(product, msg)
	default:
		// 如果无法识别类型，至少保存基础信息
		if extractedContent != "" {
			product.Title = extractedContent
		}
	}

	return product
}

// ExtractFromMessage 从Discord消息中提取产品信息（向后兼容方法）
// 利用现有的extractMessageContent结果和Embed数据
func (pe *ProductExtractor) ExtractFromMessage(embeds []map[string]interface{}, extractedContent string) *ProductItem {
	product := &ProductItem{
		Metadata:     make(map[string]interface{}),
		Stock:        0,
		Availability: "unknown",
	}

	// 如果有提取的内容，使用作为标题
	if extractedContent != "" {
		product.Title = extractedContent
	}

	// 处理Embed数据
	if len(embeds) > 0 {
		pe.extractFromEmbeds(product, embeds)
	}

	return product
}

// extractFromMessageMap 从消息Map中提取产品信息
func (pe *ProductExtractor) extractFromMessageMap(product *ProductItem, messageMap map[string]interface{}) {
	// 提取基础消息信息
	if content, ok := messageMap["content"].(string); ok && content != "" {
		if product.Title == "" {
			product.Title = content
		}
	}

	// 提取Embed信息
	if embedsRaw, ok := messageMap["embeds"]; ok {
		if embedsList, ok := embedsRaw.([]interface{}); ok {
			embeds := make([]map[string]interface{}, 0, len(embedsList))
			for _, embedRaw := range embedsList {
				if embedMap, ok := embedRaw.(map[string]interface{}); ok {
					embeds = append(embeds, embedMap)
				}
			}
			if len(embeds) > 0 {
				pe.extractFromEmbeds(product, embeds)
			}
		}
	}

	// 提取作者信息
	if authorRaw, ok := messageMap["author"]; ok {
		if authorMap, ok := authorRaw.(map[string]interface{}); ok {
			if username, ok := authorMap["username"].(string); ok {
				product.AuthorName = &username
			}
		}
	}

	// 提取时间戳
	if timestamp, ok := messageMap["timestamp"].(string); ok {
		product.Timestamp = &timestamp
	}
}

// extractFromEmbeds 从Embed数据中提取产品信息
func (pe *ProductExtractor) extractFromEmbeds(product *ProductItem, embeds []map[string]interface{}) {
	for _, embed := range embeds {
		// 提取基础信息 - 只有在产品标题为空时才使用Embed标题
		if title, ok := embed["title"].(string); ok && title != "" && product.Title == "" {
			product.Title = title
		}
		if url, ok := embed["url"].(string); ok && url != "" {
			product.URL = url
		}
		if desc, ok := embed["description"].(string); ok && desc != "" {
			product.Description = &desc
		}
		if color, ok := embed["color"].(float64); ok {
			colorInt := int(color)
			product.Color = &colorInt
		}

		// 提取图片信息
		if image, ok := embed["image"].(map[string]interface{}); ok {
			if imageURL, ok := image["url"].(string); ok && imageURL != "" {
				product.ImageURL = &imageURL
			}
		}
		if thumbnail, ok := embed["thumbnail"].(map[string]interface{}); ok {
			if thumbURL, ok := thumbnail["url"].(string); ok && thumbURL != "" {
				product.ThumbnailURL = &thumbURL
			}
		}

		// 提取页脚信息
		if footer, ok := embed["footer"].(map[string]interface{}); ok {
			if footerText, ok := footer["text"].(string); ok && footerText != "" {
				product.FooterText = &footerText
			}
		}

		// 提取作者信息
		if author, ok := embed["author"].(map[string]interface{}); ok {
			if authorName, ok := author["name"].(string); ok && authorName != "" {
				product.AuthorName = &authorName
			}
			if authorURL, ok := author["url"].(string); ok && authorURL != "" {
				product.AuthorURL = &authorURL
			}
		}

		// 提取时间戳
		if timestamp, ok := embed["timestamp"].(string); ok && timestamp != "" {
			product.Timestamp = &timestamp
		}

		// 提取字段信息，尝试识别产品相关字段
		if fields, ok := embed["fields"].([]interface{}); ok {
			pe.extractFromFields(product, fields)
		}
	}
}

// extractFromFields 从Embed字段中提取产品信息
func (pe *ProductExtractor) extractFromFields(product *ProductItem, fields []interface{}) {
	for _, field := range fields {
		if fieldMap, ok := field.(map[string]interface{}); ok {
			name, nameOk := fieldMap["name"].(string)
			value, valueOk := fieldMap["value"].(string)

			if !nameOk || !valueOk || name == "" || value == "" {
				continue
			}

			// 智能字段识别和映射
			pe.mapFieldToProduct(product, name, value)
		}
	}
}

// mapFieldToProduct 将字段映射到产品信息
func (pe *ProductExtractor) mapFieldToProduct(product *ProductItem, fieldName, fieldValue string) {
	// 转换为小写进行匹配，提高识别准确性
	lowerName := strings.ToLower(fieldName)

	// 价格字段识别
	if pe.isPriceField(lowerName) {
		product.Price = fieldValue
		return
	}

	// 产品ID字段识别
	if pe.isProductIDField(lowerName) {
		product.ProductID = fieldValue
		return
	}

	// 平台字段识别
	if pe.isPlatformField(lowerName) {
		product.Platform = fieldValue
		return
	}

	// SKU字段识别
	if pe.isSKUField(lowerName) {
		product.SkuID = &fieldValue
		return
	}

	// Offer ID字段识别
	if pe.isOfferIDField(lowerName) {
		product.OfferID = &fieldValue
		return
	}

	// 发布日期字段识别
	if pe.isReleaseDateField(lowerName) {
		product.ReleaseDate = &fieldValue
		return
	}

	// 将未识别的字段存储到元数据中
	product.Metadata[fieldName] = fieldValue
}

// 字段识别辅助方法
func (pe *ProductExtractor) isPriceField(fieldName string) bool {
	priceFields := []string{"price", "价格", "cost", "费用", "金额", "售价", "定价"}
	return pe.containsAny(fieldName, priceFields)
}

func (pe *ProductExtractor) isProductIDField(fieldName string) bool {
	idFields := []string{"productid", "product_id", "asin", "id", "产品id", "商品id", "编号"}
	return pe.containsAny(fieldName, idFields)
}

func (pe *ProductExtractor) isStockField(fieldName string) bool {
	stockFields := []string{"stock", "库存", "inventory", "数量", "现货", "存货"}
	return pe.containsAny(fieldName, stockFields)
}

func (pe *ProductExtractor) isPlatformField(fieldName string) bool {
	platformFields := []string{"platform", "平台", "site", "网站", "商城", "店铺"}
	return pe.containsAny(fieldName, platformFields)
}

func (pe *ProductExtractor) isSKUField(fieldName string) bool {
	skuFields := []string{"sku", "skuid", "sku_id", "型号", "规格"}
	return pe.containsAny(fieldName, skuFields)
}

func (pe *ProductExtractor) isOfferIDField(fieldName string) bool {
	offerFields := []string{"offerid", "offer_id", "优惠id", "活动id"}
	return pe.containsAny(fieldName, offerFields)
}

func (pe *ProductExtractor) isReleaseDateField(fieldName string) bool {
	dateFields := []string{"releasedate", "release_date", "发布日期", "上市日期", "发售日期"}
	return pe.containsAny(fieldName, dateFields)
}

// containsAny 检查字段名是否包含任何关键词
func (pe *ProductExtractor) containsAny(fieldName string, keywords []string) bool {
	for _, keyword := range keywords {
		if strings.Contains(fieldName, keyword) {
			return true
		}
	}
	return false
}

// ToLegacyProductData 转换为兼容现有系统的ProductData结构
func (pi *ProductItem) ToLegacyProductData() map[string]interface{} {
	data := map[string]interface{}{
		"platform":  pi.Platform,
		"productId": pi.ProductID,
		"title":     pi.Title,
		"price":     pi.Price,
	}

	if pi.OfferID != nil {
		data["offerId"] = *pi.OfferID
	}
	if pi.SkuID != nil {
		data["skuId"] = *pi.SkuID
	}
	if pi.ImageURL != nil {
		data["imageUrl"] = *pi.ImageURL
	}

	return data
}

// FromLegacyProductData 从现有ProductData结构创建ProductItem
func FromLegacyProductData(data map[string]interface{}) *ProductItem {
	product := &ProductItem{
		Metadata:     make(map[string]interface{}),
		Stock:        0,
		Availability: "unknown",
	}

	if platform, ok := data["platform"].(string); ok {
		product.Platform = platform
	}
	if productID, ok := data["productId"].(string); ok {
		product.ProductID = productID
	}
	if title, ok := data["title"].(string); ok {
		product.Title = title
	}
	if price, ok := data["price"].(string); ok {
		product.Price = price
	}

	if offerID, ok := data["offerId"].(string); ok {
		product.OfferID = &offerID
	}
	if skuID, ok := data["skuId"].(string); ok {
		product.SkuID = &skuID
	}
	if imageURL, ok := data["imageUrl"].(string); ok {
		product.ImageURL = &imageURL
	}

	return product
}
