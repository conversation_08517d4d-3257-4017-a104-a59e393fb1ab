package types

import (
	"errors"
	"fmt"
	"time"
)

// 预定义错误
var (
	// 配置相关错误
	ErrMissingDiscordToken = errors.New("缺少 Discord Token")
	ErrMissingClientID     = errors.New("缺少 Discord Client ID")
	ErrInvalidConfig       = errors.New("无效的配置")

	// Bot 相关错误
	ErrBotNotStarted     = errors.New("Bot 未启动")
	ErrBotAlreadyStarted = errors.New("Bot 已经启动")
	ErrBotShutdown       = errors.New("Bot 正在关闭")

	// 命令相关错误
	ErrCommandNotFound         = errors.New("命令未找到")
	ErrCommandCooldown         = errors.New("命令冷却中")
	ErrInsufficientPermissions = errors.New("权限不足")
	ErrInvalidArguments        = errors.New("无效的参数")

	// 服务相关错误
	ErrServiceNotAvailable = errors.New("服务不可用")
	ErrCacheNotAvailable   = errors.New("缓存服务不可用")
	ErrQueueNotAvailable   = errors.New("队列服务不可用")

	// 队列连接相关错误
	ErrQueueConnectionFailed   = errors.New("队列连接失败")
	ErrQueueConnectionLost     = errors.New("队列连接丢失")
	ErrQueueReconnectFailed    = errors.New("队列重连失败")
	ErrQueueMaxRetriesExceeded = errors.New("队列重连达到最大次数")
	ErrQueueChannelClosed      = errors.New("队列通道已关闭")
	ErrQueuePublishFailed      = errors.New("队列消息发布失败")
	ErrQueueConsumeFailed      = errors.New("队列消息消费失败")

	// 通知相关错误
	ErrTemplateNotFound     = errors.New("模板未找到")
	ErrTemplateRenderFailed = errors.New("模板渲染失败")
	ErrNotificationFailed   = errors.New("通知发送失败")

	// 任务相关错误
	ErrInvalidTaskData      = errors.New("无效的任务数据")
	ErrTaskHandlerNotFound  = errors.New("任务处理器未找到")
	ErrTaskProcessingFailed = errors.New("任务处理失败")
)

// BotError Bot 错误类型
type BotError struct {
	Code    string
	Message string
	Cause   error
}

// Error 实现 error 接口
func (e *BotError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("[%s] %s: %v", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// Unwrap 返回原始错误
func (e *BotError) Unwrap() error {
	return e.Cause
}

// NewBotError 创建新的 Bot 错误
func NewBotError(code, message string, cause error) *BotError {
	return &BotError{
		Code:    code,
		Message: message,
		Cause:   cause,
	}
}

// CommandError 命令错误类型
type CommandError struct {
	Command string
	User    string
	Guild   string
	Message string
	Cause   error
}

// Error 实现 error 接口
func (e *CommandError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("命令错误 [%s] 用户 [%s] 服务器 [%s]: %s (%v)",
			e.Command, e.User, e.Guild, e.Message, e.Cause)
	}
	return fmt.Sprintf("命令错误 [%s] 用户 [%s] 服务器 [%s]: %s",
		e.Command, e.User, e.Guild, e.Message)
}

// Unwrap 返回原始错误
func (e *CommandError) Unwrap() error {
	return e.Cause
}

// NewCommandError 创建新的命令错误
func NewCommandError(command, user, guild, message string, cause error) *CommandError {
	return &CommandError{
		Command: command,
		User:    user,
		Guild:   guild,
		Message: message,
		Cause:   cause,
	}
}

// ValidationError 验证错误类型
type ValidationError struct {
	Field   string
	Value   interface{}
	Message string
}

// Error 实现 error 接口
func (e *ValidationError) Error() string {
	return fmt.Sprintf("验证错误 [%s=%v]: %s", e.Field, e.Value, e.Message)
}

// NewValidationError 创建新的验证错误
func NewValidationError(field string, value interface{}, message string) *ValidationError {
	return &ValidationError{
		Field:   field,
		Value:   value,
		Message: message,
	}
}

// ServiceError 服务错误类型
type ServiceError struct {
	Service   string
	Operation string
	Message   string
	Cause     error
}

// Error 实现 error 接口
func (e *ServiceError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("服务错误 [%s.%s]: %s (%v)",
			e.Service, e.Operation, e.Message, e.Cause)
	}
	return fmt.Sprintf("服务错误 [%s.%s]: %s",
		e.Service, e.Operation, e.Message)
}

// Unwrap 返回原始错误
func (e *ServiceError) Unwrap() error {
	return e.Cause
}

// NewServiceError 创建新的服务错误
func NewServiceError(service, operation, message string, cause error) *ServiceError {
	return &ServiceError{
		Service:   service,
		Operation: operation,
		Message:   message,
		Cause:     cause,
	}
}

// ErrorCode 错误码类型
type ErrorCode string

// 错误码常量
const (
	// 系统级错误码
	ErrCodeSystem     ErrorCode = "SYSTEM_ERROR"
	ErrCodeConfig     ErrorCode = "CONFIG_ERROR"
	ErrCodeNetwork    ErrorCode = "NETWORK_ERROR"
	ErrCodeTimeout    ErrorCode = "TIMEOUT_ERROR"
	ErrCodePermission ErrorCode = "PERMISSION_ERROR"

	// 服务级错误码
	ErrCodeServiceUnavailable ErrorCode = "SERVICE_UNAVAILABLE"
	ErrCodeServiceTimeout     ErrorCode = "SERVICE_TIMEOUT"
	ErrCodeServiceOverload    ErrorCode = "SERVICE_OVERLOAD"

	// 业务级错误码
	ErrCodeValidation ErrorCode = "VALIDATION_ERROR"
	ErrCodeNotFound   ErrorCode = "NOT_FOUND"
	ErrCodeConflict   ErrorCode = "CONFLICT_ERROR"
	ErrCodeRateLimit  ErrorCode = "RATE_LIMIT"

	// Discord相关错误码
	ErrCodeDiscordAPI     ErrorCode = "DISCORD_API_ERROR"
	ErrCodeDiscordAuth    ErrorCode = "DISCORD_AUTH_ERROR"
	ErrCodeDiscordLimit   ErrorCode = "DISCORD_RATE_LIMIT"
	ErrCodeDiscordChannel ErrorCode = "DISCORD_CHANNEL_ERROR"

	// 任务相关错误码
	ErrCodeTaskInvalid   ErrorCode = "TASK_INVALID"
	ErrCodeTaskTimeout   ErrorCode = "TASK_TIMEOUT"
	ErrCodeTaskFailed    ErrorCode = "TASK_FAILED"
	ErrCodeTaskNotFound  ErrorCode = "TASK_NOT_FOUND"
	ErrCodeTaskDuplicate ErrorCode = "TASK_DUPLICATE"

	// 队列相关错误码
	ErrCodeQueueConnection ErrorCode = "QUEUE_CONNECTION_ERROR"
	ErrCodeQueuePublish    ErrorCode = "QUEUE_PUBLISH_ERROR"
	ErrCodeQueueConsume    ErrorCode = "QUEUE_CONSUME_ERROR"
	ErrCodeQueueTimeout    ErrorCode = "QUEUE_TIMEOUT"

	// 缓存相关错误码
	ErrCodeCacheConnection ErrorCode = "CACHE_CONNECTION_ERROR"
	ErrCodeCacheTimeout    ErrorCode = "CACHE_TIMEOUT"
	ErrCodeCacheKeyInvalid ErrorCode = "CACHE_KEY_INVALID"
)

// TaskError 任务错误类型
type TaskError struct {
	Code      ErrorCode
	TaskID    string
	TaskType  string
	Message   string
	Cause     error
	Timestamp time.Time
	Context   map[string]interface{}
}

// Error 实现 error 接口
func (e *TaskError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("任务错误 [%s] ID:%s Type:%s - %s (%v)",
			e.Code, e.TaskID, e.TaskType, e.Message, e.Cause)
	}
	return fmt.Sprintf("任务错误 [%s] ID:%s Type:%s - %s",
		e.Code, e.TaskID, e.TaskType, e.Message)
}

// Unwrap 返回原始错误
func (e *TaskError) Unwrap() error {
	return e.Cause
}

// NewTaskError 创建新的任务错误
func NewTaskError(code ErrorCode, taskID, taskType, message string, cause error) *TaskError {
	return &TaskError{
		Code:      code,
		TaskID:    taskID,
		TaskType:  taskType,
		Message:   message,
		Cause:     cause,
		Timestamp: time.Now(),
		Context:   make(map[string]interface{}),
	}
}

// WithContext 添加上下文信息
func (e *TaskError) WithContext(key string, value interface{}) *TaskError {
	e.Context[key] = value
	return e
}

// NetworkError 网络错误类型
type NetworkError struct {
	Code      ErrorCode
	Operation string
	URL       string
	Method    string
	Status    int
	Message   string
	Cause     error
	Timestamp time.Time
	Duration  time.Duration
}

// Error 实现 error 接口
func (e *NetworkError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("网络错误 [%s] %s %s (状态:%d, 耗时:%v) - %s (%v)",
			e.Code, e.Method, e.URL, e.Status, e.Duration, e.Message, e.Cause)
	}
	return fmt.Sprintf("网络错误 [%s] %s %s (状态:%d, 耗时:%v) - %s",
		e.Code, e.Method, e.URL, e.Status, e.Duration, e.Message)
}

// Unwrap 返回原始错误
func (e *NetworkError) Unwrap() error {
	return e.Cause
}

// NewNetworkError 创建新的网络错误
func NewNetworkError(code ErrorCode, operation, url, method string, status int, message string, cause error, duration time.Duration) *NetworkError {
	return &NetworkError{
		Code:      code,
		Operation: operation,
		URL:       url,
		Method:    method,
		Status:    status,
		Message:   message,
		Cause:     cause,
		Timestamp: time.Now(),
		Duration:  duration,
	}
}

// DiscordError Discord相关错误类型
type DiscordError struct {
	Code      ErrorCode
	Operation string
	GuildID   string
	ChannelID string
	UserID    string
	Message   string
	Cause     error
	Timestamp time.Time
	RateLimit *RateLimitInfo
}

// RateLimitInfo 速率限制信息
type RateLimitInfo struct {
	Limit     int
	Remaining int
	ResetAt   time.Time
	Bucket    string
}

// Error 实现 error 接口
func (e *DiscordError) Error() string {
	context := fmt.Sprintf("Guild:%s Channel:%s User:%s", e.GuildID, e.ChannelID, e.UserID)
	if e.Cause != nil {
		return fmt.Sprintf("Discord错误 [%s] %s (%s) - %s (%v)",
			e.Code, e.Operation, context, e.Message, e.Cause)
	}
	return fmt.Sprintf("Discord错误 [%s] %s (%s) - %s",
		e.Code, e.Operation, context, e.Message)
}

// Unwrap 返回原始错误
func (e *DiscordError) Unwrap() error {
	return e.Cause
}

// NewDiscordError 创建新的Discord错误
func NewDiscordError(code ErrorCode, operation, guildID, channelID, userID, message string, cause error) *DiscordError {
	return &DiscordError{
		Code:      code,
		Operation: operation,
		GuildID:   guildID,
		ChannelID: channelID,
		UserID:    userID,
		Message:   message,
		Cause:     cause,
		Timestamp: time.Now(),
	}
}

// WithRateLimit 添加速率限制信息
func (e *DiscordError) WithRateLimit(limit, remaining int, resetAt time.Time, bucket string) *DiscordError {
	e.RateLimit = &RateLimitInfo{
		Limit:     limit,
		Remaining: remaining,
		ResetAt:   resetAt,
		Bucket:    bucket,
	}
	return e
}

// ConfigError 配置错误类型
type ConfigError struct {
	Code      ErrorCode
	Section   string
	Field     string
	Value     interface{}
	Message   string
	Cause     error
	Timestamp time.Time
}

// Error 实现 error 接口
func (e *ConfigError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("配置错误 [%s] %s.%s=%v - %s (%v)",
			e.Code, e.Section, e.Field, e.Value, e.Message, e.Cause)
	}
	return fmt.Sprintf("配置错误 [%s] %s.%s=%v - %s",
		e.Code, e.Section, e.Field, e.Value, e.Message)
}

// Unwrap 返回原始错误
func (e *ConfigError) Unwrap() error {
	return e.Cause
}

// NewConfigError 创建新的配置错误
func NewConfigError(code ErrorCode, section, field string, value interface{}, message string, cause error) *ConfigError {
	return &ConfigError{
		Code:      code,
		Section:   section,
		Field:     field,
		Value:     value,
		Message:   message,
		Cause:     cause,
		Timestamp: time.Now(),
	}
}
