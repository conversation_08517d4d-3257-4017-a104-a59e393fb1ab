package types

import (
	"fmt"
	"regexp"
	"strings"
	"time"
)

// FieldMappingGroup 字段映射组结构
// 基于 docs/enhanced_system_design_v3.md 中的设计规范
type FieldMappingGroup struct {
	Name        string                 `yaml:"name" json:"name"`
	AuthorName  string                 `yaml:"author_name" json:"author_name"`
	Platform    string                 `yaml:"platform" json:"platform"`
	Description string                 `yaml:"description" json:"description"`
	Mappings    []FieldMapping         `yaml:"mappings" json:"mappings"`
	Defaults    map[string]interface{} `yaml:"defaults" json:"defaults"`
	Transforms  map[string]Transform   `yaml:"transforms" json:"transforms"`
}

// FieldMapping 字段映射规则
type FieldMapping struct {
	SourceField    string   `yaml:"source_field" json:"source_field"`
	TargetField    string   `yaml:"target_field" json:"target_field"`
	Required       bool     `yaml:"required" json:"required"`
	FallbackFields []string `yaml:"fallback_fields" json:"fallback_fields"`
	Transform      string   `yaml:"transform" json:"transform"`
	Description    string   `yaml:"description" json:"description"`
}

// Transform 数据转换规则
type Transform struct {
	Type        string `yaml:"type" json:"type"`
	Pattern     string `yaml:"pattern" json:"pattern"`
	Template    string `yaml:"template" json:"template"`
	Description string `yaml:"description" json:"description"`
}

// FieldMappingGroupsConfig 字段映射组配置文件结构
type FieldMappingGroupsConfig struct {
	FieldMappingGroups map[string]FieldMappingGroup `yaml:"field_mapping_groups" json:"field_mapping_groups"`
	GlobalSettings     GlobalMappingSettings        `yaml:"global_settings" json:"global_settings"`
}

// GlobalMappingSettings 全局映射设置
type GlobalMappingSettings struct {
	BaseMappings        []FieldMapping `yaml:"base_mappings" json:"base_mappings"`
	DefaultMappingGroup string         `yaml:"default_mapping_group" json:"default_mapping_group"`
	EnableAutoDetection bool           `yaml:"enable_auto_detection" json:"enable_auto_detection"`
	CacheSettings       CacheSettings  `yaml:"cache_settings" json:"cache_settings"`
	Logging             MappingLogging `yaml:"logging" json:"logging"`
	ErrorHandling       ErrorHandling  `yaml:"error_handling" json:"error_handling"`
}

// CacheSettings 缓存设置
type CacheSettings struct {
	Enabled bool          `yaml:"enabled" json:"enabled"`
	TTL     time.Duration `yaml:"ttl" json:"ttl"`
	MaxSize int           `yaml:"max_size" json:"max_size"`
}

// MappingLogging 映射日志设置
type MappingLogging struct {
	Enabled           bool   `yaml:"enabled" json:"enabled"`
	Level             string `yaml:"level" json:"level"`
	LogMappingResults bool   `yaml:"log_mapping_results" json:"log_mapping_results"`
}

// ErrorHandling 错误处理设置
type ErrorHandling struct {
	IgnoreMissingRequiredFields bool `yaml:"ignore_missing_required_fields" json:"ignore_missing_required_fields"`
	UseFallbackOnTransformError bool `yaml:"use_fallback_on_transform_error" json:"use_fallback_on_transform_error"`
	LogTransformErrors          bool `yaml:"log_transform_errors" json:"log_transform_errors"`
}

// FieldMapper 字段映射器接口
type FieldMapper interface {
	MapFields(sourceData map[string]interface{}, mappingGroup string) (*MappingResult, error)
	GetMappingGroup(name string) (*FieldMappingGroup, error)
	ListMappingGroups() []string
	FindMappingGroupByAuthor(authorName string) (string, error)
}

// MappingResult 映射结果
type MappingResult struct {
	Product      *ProductItem         `json:"product"`
	MappingGroup string               `json:"mapping_group"`
	AppliedRules []AppliedMappingRule `json:"applied_rules"`
	Errors       []MappingError       `json:"errors"`
	Warnings     []MappingWarning     `json:"warnings"`
}

// AppliedMappingRule 已应用的映射规则
type AppliedMappingRule struct {
	SourceField     string      `json:"source_field"`
	TargetField     string      `json:"target_field"`
	Value           interface{} `json:"value"`
	Transform       string      `json:"transform,omitempty"`
	UsedFallback    bool        `json:"used_fallback"`
	MatchedField    string      `json:"matched_field"`    // 实际匹配到的字段名
	FallbackIndex   int         `json:"fallback_index"`   // 使用的fallback字段索引，-1表示使用主字段
	AvailableFields []string    `json:"available_fields"` // 所有可用的字段名（用于调试）
}

// MappingError 映射错误
type MappingError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Type    string `json:"type"`
}

// MappingWarning 映射警告
type MappingWarning struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Type    string `json:"type"`
}

// ApplyMapping 应用字段映射到产品信息
func (fmg *FieldMappingGroup) ApplyMapping(sourceData map[string]interface{}) (*MappingResult, error) {
	result := &MappingResult{
		Product: &ProductItem{
			Metadata:     make(map[string]interface{}),
			Stock:        0,
			Availability: "unknown",
		},
		MappingGroup: fmg.Name,
		AppliedRules: []AppliedMappingRule{},
		Errors:       []MappingError{},
		Warnings:     []MappingWarning{},
	}

	// 应用默认值
	fmg.applyDefaults(result.Product)

	// 应用字段映射
	for _, mapping := range fmg.Mappings {
		if err := fmg.applyFieldMapping(sourceData, result, &mapping); err != nil {
			result.Errors = append(result.Errors, MappingError{
				Field:   mapping.TargetField,
				Message: err.Error(),
				Type:    "mapping_error",
			})
		}
	}

	return result, nil
}

// applyDefaults 应用默认值
func (fmg *FieldMappingGroup) applyDefaults(product *ProductItem) {
	for key, value := range fmg.Defaults {
		switch key {
		case "Platform":
			if strVal, ok := value.(string); ok {
				product.Platform = strVal
			}
		case "Country":
			if strVal, ok := value.(string); ok {
				product.Metadata["Country"] = strVal
			}
		case "Availability":
			if strVal, ok := value.(string); ok {
				product.Availability = strVal
			}
		case "Stock":
			if intVal, ok := value.(int); ok {
				product.Stock = intVal
			}
		default:
			product.Metadata[key] = value
		}
	}
}

// applyFieldMapping 应用单个字段映射
func (fmg *FieldMappingGroup) applyFieldMapping(sourceData map[string]interface{}, result *MappingResult, mapping *FieldMapping) error {
	// 尝试从源字段获取值
	matchInfo := fmg.getFieldValue(sourceData, mapping.SourceField, mapping.FallbackFields)

	if !matchInfo.Found {
		if mapping.Required {
			return fmt.Errorf("required field %s not found", mapping.SourceField)
		}
		result.Warnings = append(result.Warnings, MappingWarning{
			Field:   mapping.SourceField,
			Message: fmt.Sprintf("Optional field %s not found", mapping.SourceField),
			Type:    "missing_field",
		})
		return nil
	}

	value := matchInfo.Value

	// 应用转换
	if mapping.Transform != "" {
		if transform, exists := fmg.Transforms[mapping.Transform]; exists {
			transformedValue, err := fmg.applyTransform(value, &transform)
			if err != nil {
				result.Warnings = append(result.Warnings, MappingWarning{
					Field:   mapping.SourceField,
					Message: fmt.Sprintf("Transform failed: %v", err),
					Type:    "transform_error",
				})
			} else {
				value = transformedValue
			}
		}
	}

	// 设置目标字段值
	fmg.setProductField(result.Product, mapping.TargetField, value)

	// 记录应用的规则，包含字段匹配信息
	result.AppliedRules = append(result.AppliedRules, AppliedMappingRule{
		SourceField:     mapping.SourceField,
		TargetField:     mapping.TargetField,
		Value:           value,
		Transform:       mapping.Transform,
		UsedFallback:    matchInfo.FallbackIndex >= 0,
		MatchedField:    matchInfo.MatchedField,
		FallbackIndex:   matchInfo.FallbackIndex,
		AvailableFields: matchInfo.AvailableFields,
	})

	return nil
}

// FieldMatchInfo 字段匹配信息
type FieldMatchInfo struct {
	Value           interface{}
	Found           bool
	MatchedField    string
	FallbackIndex   int      // -1表示使用主字段，>=0表示使用fallback字段的索引
	AvailableFields []string // 所有可用的字段名
}

// getFieldValue 获取字段值，支持回退字段，返回详细的匹配信息
func (fmg *FieldMappingGroup) getFieldValue(sourceData map[string]interface{}, primaryField string, fallbackFields []string) *FieldMatchInfo {
	// 收集所有可用的字段名（用于调试）
	availableFields := make([]string, 0, len(sourceData))
	for key := range sourceData {
		availableFields = append(availableFields, key)
	}

	// 尝试主字段
	if value, exists := sourceData[primaryField]; exists {
		return &FieldMatchInfo{
			Value:           value,
			Found:           true,
			MatchedField:    primaryField,
			FallbackIndex:   -1,
			AvailableFields: availableFields,
		}
	}

	// 尝试回退字段
	for i, fallbackField := range fallbackFields {
		if value, exists := sourceData[fallbackField]; exists {
			return &FieldMatchInfo{
				Value:           value,
				Found:           true,
				MatchedField:    fallbackField,
				FallbackIndex:   i,
				AvailableFields: availableFields,
			}
		}
	}

	return &FieldMatchInfo{
		Value:           nil,
		Found:           false,
		MatchedField:    "",
		FallbackIndex:   -1,
		AvailableFields: availableFields,
	}
}

// applyTransform 应用数据转换
func (fmg *FieldMappingGroup) applyTransform(value interface{}, transform *Transform) (interface{}, error) {
	strValue := fmt.Sprintf("%v", value)

	switch transform.Type {
	case "regex":
		re, err := regexp.Compile(transform.Pattern)
		if err != nil {
			return nil, fmt.Errorf("invalid regex pattern: %w", err)
		}
		matches := re.FindStringSubmatch(strValue)

		// 添加详细的调试信息
		fmt.Printf("[DEBUG] 正则表达式转换: pattern='%s', input_length=%d, matches_found=%d\n",
			transform.Pattern, len(strValue), len(matches))
		fmt.Printf("[DEBUG] 输入值: %s\n", strValue)
		fmt.Printf("[DEBUG] 所有匹配: %v\n", matches)

		if len(matches) > 1 {
			extractedValue := matches[1]
			fmt.Printf("[INFO] 正则表达式匹配成功: pattern='%s', extracted='%s'\n",
				transform.Pattern, extractedValue)
			return extractedValue, nil
		}

		// 如果正则表达式没有匹配到任何内容，返回详细错误信息
		inputPreview := strValue
		if len(strValue) > 100 {
			inputPreview = strValue[:100] + "..."
		}
		fmt.Printf("[WARN] 正则表达式匹配失败: pattern='%s', input_preview='%s'\n",
			transform.Pattern, inputPreview)
		return nil, fmt.Errorf("regex pattern '%s' did not match value '%s'", transform.Pattern, strValue)

	case "template":
		result := strings.ReplaceAll(transform.Template, "{value}", strValue)
		return result, nil

	default:
		return value, fmt.Errorf("unknown transform type: %s", transform.Type)
	}
}

// setProductField 设置产品字段值
func (fmg *FieldMappingGroup) setProductField(product *ProductItem, fieldName string, value interface{}) {
	strValue := fmt.Sprintf("%v", value)

	switch fieldName {
	case "Title":
		product.Title = strValue
	case "URL":
		product.URL = strValue
	case "ProductID":
		product.ProductID = strValue
	case "Price":
		product.Price = strValue
	case "Platform":
		product.Platform = strValue
	case "Description":
		product.Description = &strValue
	case "ImageURL":
		product.ImageURL = &strValue
	case "ThumbnailURL":
		product.ThumbnailURL = &strValue
	case "SkuID":
		product.SkuID = &strValue
	case "OfferID":
		product.OfferID = &strValue
	case "AtcLink":
		product.AtcLink = &strValue
	case "ReleaseDate":
		product.ReleaseDate = &strValue
	case "AuthorName":
		product.AuthorName = &strValue
	case "AuthorURL":
		product.AuthorURL = &strValue
	case "AuthorIconURL":
		product.AuthorIconURL = &strValue
	case "FooterText":
		product.FooterText = &strValue
	case "FooterIconURL":
		product.FooterIconURL = &strValue
	case "Timestamp":
		product.Timestamp = &strValue
	case "Color":
		// 处理颜色字段，尝试转换为int
		if intVal, ok := value.(int); ok {
			product.Color = &intVal
		} else {
			// 如果不是int，存储到metadata
			product.Metadata[fieldName] = value
		}
	case "Stock", "Inventory":
		// 处理库存字段，尝试转换为int
		if intVal, ok := value.(int); ok {
			product.Stock = intVal
		} else if strVal, ok := value.(string); ok {
			// 尝试解析字符串为数字
			var stock int
			if n, err := fmt.Sscanf(strVal, "%d", &stock); err == nil && n == 1 {
				product.Stock = stock
			} else {
				// 解析失败，存储原始值到metadata
				product.Metadata["stock_info"] = strVal
			}
		}
	case "Availability":
		product.Availability = strValue
	case "Addition":
		product.Addition = &strValue
	default:
		// 未知字段存储到元数据中
		product.Metadata[fieldName] = value
	}
}

// Validate 验证映射组配置
func (fmg *FieldMappingGroup) Validate() error {
	if fmg.Name == "" {
		return fmt.Errorf("mapping group name cannot be empty")
	}

	if len(fmg.Mappings) == 0 {
		return fmt.Errorf("mapping group must have at least one mapping")
	}

	for i, mapping := range fmg.Mappings {
		if mapping.SourceField == "" {
			return fmt.Errorf("mapping %d: source field cannot be empty", i)
		}
		if mapping.TargetField == "" {
			return fmt.Errorf("mapping %d: target field cannot be empty", i)
		}
	}

	return nil
}
