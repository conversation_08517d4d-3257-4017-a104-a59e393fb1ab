package api

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"
)

// HealthResponse 健康检查响应
type HealthResponse struct {
	Status    string                   `json:"status"`    // "healthy", "unhealthy", "degraded"
	Timestamp time.Time                `json:"timestamp"` // 检查时间
	Services  map[string]ServiceHealth `json:"services"`  // 各服务状态
	Summary   HealthSummary            `json:"summary"`   // 状态摘要
}

// ServiceHealth 服务健康状态
type ServiceHealth struct {
	Status  string      `json:"status"`            // "healthy", "unhealthy", "degraded"
	Message string      `json:"message"`           // 状态描述
	Details interface{} `json:"details,omitempty"` // 详细信息
}

// HealthSummary 健康状态摘要
type HealthSummary struct {
	TotalServices     int `json:"total_services"`
	HealthyServices   int `json:"healthy_services"`
	UnhealthyServices int `json:"unhealthy_services"`
	DegradedServices  int `json:"degraded_services"`
}

// HealthChecker 健康检查器
type HealthChecker struct {
	cacheService types.CacheService
	queueService types.QueueService
}

// NewHealthChecker 创建健康检查器
func NewHealthChecker(cacheService types.CacheService, queueService types.QueueService) *HealthChecker {
	return &HealthChecker{
		cacheService: cacheService,
		queueService: queueService,
	}
}

// CheckHealth 执行健康检查
func (h *HealthChecker) CheckHealth() *HealthResponse {
	response := &HealthResponse{
		Timestamp: time.Now(),
		Services:  make(map[string]ServiceHealth),
		Summary:   HealthSummary{},
	}

	// 检查Redis缓存服务
	h.checkCacheService(response)

	// 检查RabbitMQ队列服务
	h.checkQueueService(response)

	// 计算总体状态
	h.calculateOverallStatus(response)

	return response
}

// checkCacheService 检查缓存服务
func (h *HealthChecker) checkCacheService(response *HealthResponse) {
	if h.cacheService == nil {
		response.Services["cache"] = ServiceHealth{
			Status:  "unhealthy",
			Message: "缓存服务未配置",
		}
		return
	}

	// 尝试ping缓存服务
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := h.cacheService.Ping(ctx); err != nil {
		response.Services["cache"] = ServiceHealth{
			Status:  "unhealthy",
			Message: "缓存服务连接失败: " + err.Error(),
		}
	} else {
		response.Services["cache"] = ServiceHealth{
			Status:  "healthy",
			Message: "缓存服务正常",
		}
	}
}

// checkQueueService 检查队列服务
func (h *HealthChecker) checkQueueService(response *HealthResponse) {
	if h.queueService == nil {
		response.Services["queue"] = ServiceHealth{
			Status:  "degraded",
			Message: "队列服务未配置（优雅降级模式）",
		}
		return
	}

	// 检查队列服务连接状态
	if !h.queueService.IsConnected() {
		// 尝试获取详细的健康状态
		if rabbitMQService, ok := h.queueService.(interface {
			GetHealthStatus() *types.QueueHealthStatus
		}); ok {
			healthStatus := rabbitMQService.GetHealthStatus()
			response.Services["queue"] = ServiceHealth{
				Status:  healthStatus.Status,
				Message: healthStatus.Message,
				Details: healthStatus,
			}
		} else {
			response.Services["queue"] = ServiceHealth{
				Status:  "unhealthy",
				Message: "队列服务连接失败",
			}
		}
	} else {
		response.Services["queue"] = ServiceHealth{
			Status:  "healthy",
			Message: "队列服务正常",
		}
	}
}

// calculateOverallStatus 计算总体状态
func (h *HealthChecker) calculateOverallStatus(response *HealthResponse) {
	summary := &response.Summary
	summary.TotalServices = len(response.Services)

	for _, service := range response.Services {
		switch service.Status {
		case "healthy":
			summary.HealthyServices++
		case "unhealthy":
			summary.UnhealthyServices++
		case "degraded":
			summary.DegradedServices++
		}
	}

	// 确定总体状态
	if summary.UnhealthyServices > 0 {
		response.Status = "unhealthy"
	} else if summary.DegradedServices > 0 {
		response.Status = "degraded"
	} else {
		response.Status = "healthy"
	}
}

// HealthHandler HTTP健康检查处理器
func (h *HealthChecker) HealthHandler(w http.ResponseWriter, r *http.Request) {
	logger.Debug("执行健康检查", "remote_addr", r.RemoteAddr)

	health := h.CheckHealth()

	w.Header().Set("Content-Type", "application/json")

	// 根据健康状态设置HTTP状态码
	switch health.Status {
	case "healthy":
		w.WriteHeader(http.StatusOK)
	case "degraded":
		w.WriteHeader(http.StatusOK) // 降级状态仍返回200，但在响应中标明
	case "unhealthy":
		w.WriteHeader(http.StatusServiceUnavailable)
	default:
		w.WriteHeader(http.StatusInternalServerError)
	}

	if err := json.NewEncoder(w).Encode(health); err != nil {
		logger.Error("编码健康检查响应失败", "error", err)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
	}
}

// ReadinessHandler 就绪检查处理器（用于Kubernetes等）
func (h *HealthChecker) ReadinessHandler(w http.ResponseWriter, r *http.Request) {
	health := h.CheckHealth()

	w.Header().Set("Content-Type", "application/json")

	// 就绪检查更严格，降级状态也认为未就绪
	if health.Status == "healthy" {
		w.WriteHeader(http.StatusOK)
	} else {
		w.WriteHeader(http.StatusServiceUnavailable)
	}

	response := map[string]interface{}{
		"ready":     health.Status == "healthy",
		"status":    health.Status,
		"timestamp": health.Timestamp,
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		logger.Error("编码就绪检查响应失败", "error", err)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
	}
}

// LivenessHandler 存活检查处理器（用于Kubernetes等）
func (h *HealthChecker) LivenessHandler(w http.ResponseWriter, r *http.Request) {
	// 存活检查只检查应用本身是否运行，不检查依赖服务
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	response := map[string]interface{}{
		"alive":     true,
		"timestamp": time.Now(),
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		logger.Error("编码存活检查响应失败", "error", err)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
	}
}
