package events

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// GuildMemberAddHandler 成员加入事件处理器
type GuildMemberAddHandler struct {
	eventType string
	priority  int
	config    *types.Config
}

// NewGuildMemberAddHandler 创建成员加入事件处理器
func NewGuildMemberAddHandler(config *types.Config) *GuildMemberAddHandler {
	return &GuildMemberAddHandler{
		eventType: "GUILD_MEMBER_ADD",
		priority:  0,
		config:    config,
	}
}

// GetEventType 获取事件类型
func (h *GuildMemberAddHandler) GetEventType() string {
	return h.eventType
}

// GetPriority 获取优先级
func (h *GuildMemberAddHandler) GetPriority() int {
	return h.priority
}

// ShouldHandle 判断是否应该处理此事件
func (h *GuildMemberAddHandler) ShouldHandle(event interface{}) bool {
	_, ok := event.(*discordgo.GuildMemberAdd)
	return ok
}

// Handle 处理事件
func (h *GuildMemberAddHandler) Handle(ctx context.Context, client *types.Client, event interface{}) error {
	member, ok := event.(*discordgo.GuildMemberAdd)
	if !ok {
		return nil
	}

	logger.Info("新成员加入",
		"user", member.User.Username,
		"user_id", member.User.ID,
		"guild", member.GuildID)

	// 发送欢迎消息
	if err := h.sendWelcomeMessage(ctx, client, member); err != nil {
		logger.Error("发送欢迎消息失败", "error", err)
	}

	// 自动分配角色
	if err := h.assignDefaultRoles(ctx, client, member); err != nil {
		logger.Error("分配默认角色失败", "error", err)
	}

	// 记录加入日志
	if err := h.logMemberJoin(ctx, client, member); err != nil {
		logger.Error("记录成员加入日志失败", "error", err)
	}

	return nil
}

// sendWelcomeMessage 发送欢迎消息
func (h *GuildMemberAddHandler) sendWelcomeMessage(ctx context.Context, client *types.Client, member *discordgo.GuildMemberAdd) error {
	// 检查是否配置了欢迎频道
	welcomeChannelID := h.getWelcomeChannelID(member.GuildID)
	if welcomeChannelID == "" {
		return nil
	}

	// 获取服务器信息
	guild, err := client.Session.Guild(member.GuildID)
	if err != nil {
		return fmt.Errorf("获取服务器信息失败: %w", err)
	}

	// 使用通知服务发送欢迎消息
	if client.GetNotificationService() != nil {
		notificationOptions := types.NotificationOptions{
			TemplateID: "member_welcome",
			Target: types.NotificationTarget{
				Type: "channel",
				ID:   welcomeChannelID,
			},
			Variables: map[string]interface{}{
				"user":         member.User,
				"guild":        guild,
				"memberCount":  guild.MemberCount,
				"creationTime": h.getUserCreationTime(member.User.ID),
			},
			GuildID:  member.GuildID,
			Priority: types.PriorityNormal,
		}

		_, err := client.GetNotificationService().SendNotification(ctx, notificationOptions)
		if err != nil {
			logger.Error("通过通知服务发送欢迎消息失败", "error", err)
			// 降级到直接发送消息
			return h.sendDirectWelcomeMessage(ctx, client, member, guild, welcomeChannelID)
		}
		return nil
	}

	// 降级到直接发送消息
	return h.sendDirectWelcomeMessage(ctx, client, member, guild, welcomeChannelID)
}

// sendDirectWelcomeMessage 直接发送欢迎消息（降级方案）
func (h *GuildMemberAddHandler) sendDirectWelcomeMessage(ctx context.Context, client *types.Client, member *discordgo.GuildMemberAdd, guild *discordgo.Guild, welcomeChannelID string) error {
	// 创建欢迎嵌入消息
	embed := &discordgo.MessageEmbed{
		Title:       fmt.Sprintf("🎉 欢迎来到 %s！", guild.Name),
		Description: fmt.Sprintf("欢迎 %s 加入我们的社区！", member.User.Mention()),
		Color:       0x00ff00,
		Thumbnail: &discordgo.MessageEmbedThumbnail{
			URL: member.User.AvatarURL("256"),
		},
		Fields: []*discordgo.MessageEmbedField{
			{
				Name:   "👤 用户",
				Value:  fmt.Sprintf("%s#%s", member.User.Username, member.User.Discriminator),
				Inline: true,
			},
			{
				Name:   "🆔 用户 ID",
				Value:  member.User.ID,
				Inline: true,
			},
			{
				Name:   "📅 账户创建时间",
				Value:  h.getUserCreationTime(member.User.ID),
				Inline: true,
			},
			{
				Name:   "📊 成员数量",
				Value:  fmt.Sprintf("你是第 %d 位成员！", guild.MemberCount),
				Inline: false,
			},
		},
		Footer: &discordgo.MessageEmbedFooter{
			Text: "请阅读服务器规则并享受你的时光！",
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	_, err := client.Session.ChannelMessageSendEmbed(welcomeChannelID, embed)
	return err
}

// assignDefaultRoles 分配默认角色
func (h *GuildMemberAddHandler) assignDefaultRoles(ctx context.Context, client *types.Client, member *discordgo.GuildMemberAdd) error {
	defaultRoles := h.getDefaultRoles(member.GuildID)
	if len(defaultRoles) == 0 {
		return nil
	}

	for _, roleID := range defaultRoles {
		err := client.Session.GuildMemberRoleAdd(member.GuildID, member.User.ID, roleID)
		if err != nil {
			logger.Error("分配角色失败",
				"user", member.User.Username,
				"role", roleID,
				"guild", member.GuildID,
				"error", err)
			continue
		}

		logger.Debug("成功分配角色",
			"user", member.User.Username,
			"role", roleID,
			"guild", member.GuildID)
	}

	return nil
}

// logMemberJoin 记录成员加入日志
func (h *GuildMemberAddHandler) logMemberJoin(ctx context.Context, client *types.Client, member *discordgo.GuildMemberAdd) error {
	logChannelID := h.getLogChannelID(member.GuildID)
	if logChannelID == "" {
		return nil
	}

	embed := &discordgo.MessageEmbed{
		Title: "📥 成员加入",
		Color: 0x00ff00,
		Fields: []*discordgo.MessageEmbedField{
			{
				Name:   "👤 用户",
				Value:  fmt.Sprintf("%s (%s)", member.User.Username, member.User.ID),
				Inline: true,
			},
			{
				Name:   "📅 加入时间",
				Value:  fmt.Sprintf("<t:%d:F>", time.Now().Unix()),
				Inline: true,
			},
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	_, err := client.Session.ChannelMessageSendEmbed(logChannelID, embed)
	return err
}

// getWelcomeChannelID 获取欢迎频道 ID
func (h *GuildMemberAddHandler) getWelcomeChannelID(guildID string) string {
	// 这里应该从配置或数据库中获取
	// 暂时返回空字符串
	return ""
}

// getDefaultRoles 获取默认角色列表
func (h *GuildMemberAddHandler) getDefaultRoles(guildID string) []string {
	// 这里应该从配置或数据库中获取
	// 暂时返回空列表
	return []string{}
}

// getLogChannelID 获取日志频道 ID
func (h *GuildMemberAddHandler) getLogChannelID(guildID string) string {
	// 这里应该从配置或数据库中获取
	// 暂时返回空字符串
	return ""
}

// getUserCreationTime 获取用户创建时间
func (h *GuildMemberAddHandler) getUserCreationTime(userID string) string {
	// Discord ID 转换为时间戳
	id, err := strconv.ParseInt(userID, 10, 64)
	if err != nil {
		return "未知"
	}

	// Discord 时间戳计算：(id >> 22) + 1420070400000
	timestamp := (id >> 22) + 1420070400000
	creationTime := time.Unix(timestamp/1000, 0)

	return fmt.Sprintf("<t:%d:F>", creationTime.Unix())
}

// GuildMemberRemoveHandler 成员离开事件处理器
type GuildMemberRemoveHandler struct {
	eventType string
	priority  int
	config    *types.Config
}

// NewGuildMemberRemoveHandler 创建成员离开事件处理器
func NewGuildMemberRemoveHandler(config *types.Config) *GuildMemberRemoveHandler {
	return &GuildMemberRemoveHandler{
		eventType: "GUILD_MEMBER_REMOVE",
		priority:  0,
		config:    config,
	}
}

// GetEventType 获取事件类型
func (h *GuildMemberRemoveHandler) GetEventType() string {
	return h.eventType
}

// GetPriority 获取优先级
func (h *GuildMemberRemoveHandler) GetPriority() int {
	return h.priority
}

// ShouldHandle 判断是否应该处理此事件
func (h *GuildMemberRemoveHandler) ShouldHandle(event interface{}) bool {
	_, ok := event.(*discordgo.GuildMemberRemove)
	return ok
}

// Handle 处理事件
func (h *GuildMemberRemoveHandler) Handle(ctx context.Context, client *types.Client, event interface{}) error {
	member, ok := event.(*discordgo.GuildMemberRemove)
	if !ok {
		return nil
	}

	logger.Info("成员离开",
		"user", member.User.Username,
		"user_id", member.User.ID,
		"guild", member.GuildID)

	// 发送告别消息
	if err := h.sendGoodbyeMessage(ctx, client, member); err != nil {
		logger.Error("发送告别消息失败", "error", err)
	}

	// 记录离开日志
	if err := h.logMemberLeave(ctx, client, member); err != nil {
		logger.Error("记录成员离开日志失败", "error", err)
	}

	return nil
}

// sendGoodbyeMessage 发送告别消息
func (h *GuildMemberRemoveHandler) sendGoodbyeMessage(ctx context.Context, client *types.Client, member *discordgo.GuildMemberRemove) error {
	goodbyeChannelID := h.getGoodbyeChannelID(member.GuildID)
	if goodbyeChannelID == "" {
		return nil
	}

	embed := &discordgo.MessageEmbed{
		Title:       "👋 再见！",
		Description: fmt.Sprintf("%s#%s 离开了服务器", member.User.Username, member.User.Discriminator),
		Color:       0xff9900,
		Thumbnail: &discordgo.MessageEmbedThumbnail{
			URL: member.User.AvatarURL("256"),
		},
		Footer: &discordgo.MessageEmbedFooter{
			Text: "希望你能再次回来！",
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	_, err := client.Session.ChannelMessageSendEmbed(goodbyeChannelID, embed)
	return err
}

// logMemberLeave 记录成员离开日志
func (h *GuildMemberRemoveHandler) logMemberLeave(ctx context.Context, client *types.Client, member *discordgo.GuildMemberRemove) error {
	logChannelID := h.getLogChannelID(member.GuildID)
	if logChannelID == "" {
		return nil
	}

	embed := &discordgo.MessageEmbed{
		Title: "📤 成员离开",
		Color: 0xff9900,
		Fields: []*discordgo.MessageEmbedField{
			{
				Name:   "👤 用户",
				Value:  fmt.Sprintf("%s (%s)", member.User.Username, member.User.ID),
				Inline: true,
			},
			{
				Name:   "📅 离开时间",
				Value:  fmt.Sprintf("<t:%d:F>", time.Now().Unix()),
				Inline: true,
			},
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	_, err := client.Session.ChannelMessageSendEmbed(logChannelID, embed)
	return err
}

// getGoodbyeChannelID 获取告别频道 ID
func (h *GuildMemberRemoveHandler) getGoodbyeChannelID(guildID string) string {
	// 这里应该从配置或数据库中获取
	// 暂时返回空字符串
	return ""
}

// getLogChannelID 获取日志频道 ID
func (h *GuildMemberRemoveHandler) getLogChannelID(guildID string) string {
	// 这里应该从配置或数据库中获取
	// 暂时返回空字符串
	return ""
}
