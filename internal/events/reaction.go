package events

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// MessageReactionAddHandler 消息反应添加事件处理器
type MessageReactionAddHandler struct {
	eventType     string
	priority      int
	config        *types.Config
	filterService types.FilterEngine
}

// NewMessageReactionAddHandler 创建消息反应添加事件处理器
func NewMessageReactionAddHandler(config *types.Config, filterService types.FilterEngine) *MessageReactionAddHandler {
	return &MessageReactionAddHandler{
		eventType:     "MESSAGE_REACTION_ADD",
		priority:      0,
		config:        config,
		filterService: filterService,
	}
}

// GetEventType 获取事件类型
func (h *MessageReactionAddHandler) GetEventType() string {
	return h.eventType
}

// GetPriority 获取优先级
func (h *MessageReactionAddHandler) GetPriority() int {
	return h.priority
}

// ShouldHandle 判断是否应该处理此事件
func (h *MessageReactionAddHandler) ShouldHandle(event interface{}) bool {
	reaction, ok := event.(*discordgo.MessageReactionAdd)
	if !ok {
		return false
	}

	// 忽略机器人的反应（如果有 Member 信息）
	if reaction.Member != nil && reaction.Member.User.Bot {
		return false
	}

	return true
}

// Handle 处理事件
func (h *MessageReactionAddHandler) Handle(ctx context.Context, client *types.Client, event interface{}) error {
	reaction, ok := event.(*discordgo.MessageReactionAdd)
	if !ok {
		return nil
	}

	logger.Info("用户添加反应",
		"user_id", reaction.UserID,
		"message_id", reaction.MessageID,
		"channel_id", reaction.ChannelID,
		"guild_id", reaction.GuildID,
		"emoji", reaction.Emoji.Name)

	// 检查用户权限
	if err := h.checkUserPermissions(ctx, client, reaction); err != nil {
		logger.Warn("用户权限检查失败", "error", err, "user_id", reaction.UserID)
		return nil // 不返回错误，只记录警告
	}

	// 检查机器人权限
	if err := h.checkBotPermissions(ctx, client, reaction.ChannelID); err != nil {
		logger.Warn("机器人权限检查失败", "error", err, "channel_id", reaction.ChannelID)
		return nil
	}

	// 根据配置决定是否记录反应事件
	if h.shouldLogReaction(reaction.GuildID) {
		if err := h.logReactionEvent(ctx, client, reaction, "添加"); err != nil {
			logger.Error("记录反应添加事件失败", "error", err)
		}
	}

	// 处理自动化操作
	if err := h.handleAutomation(ctx, client, reaction); err != nil {
		logger.Error("处理反应自动化操作失败", "error", err)
	}

	return nil
}

// checkUserPermissions 检查用户权限
func (h *MessageReactionAddHandler) checkUserPermissions(ctx context.Context, client *types.Client, reaction *discordgo.MessageReactionAdd) error {
	userID := reaction.UserID

	// 检查是否启用权限检查
	if !h.config.Permissions.EnablePermissionCheck {
		return nil
	}

	// 检查Bot所有者
	for _, ownerID := range h.config.Permissions.BotOwners {
		if userID == ownerID {
			logger.Debug("Bot所有者添加表情反应", "user", userID)
			return nil
		}
	}

	// 检查基于角色ID的权限
	if reaction.Member != nil {
		if h.checkReactionRolePermissions(reaction.Member) {
			logger.Debug("角色权限检查通过", "user", userID)
			return nil
		}
	}

	// 回退到Discord权限位检查
	permissions, err := client.Session.UserChannelPermissions(reaction.UserID, reaction.ChannelID)
	if err != nil {
		return fmt.Errorf("获取用户权限失败: %w", err)
	}

	// 检查是否有添加反应的权限
	if permissions&discordgo.PermissionAddReactions == 0 {
		return fmt.Errorf("用户没有添加反应的权限")
	}

	// 检查回退权限配置
	for _, permission := range h.config.Permissions.ReactionPermissions.FallbackPermissions {
		if h.hasPermission(permissions, permission) {
			logger.Debug("回退权限检查通过", "user", userID, "permission", permission)
			return nil
		}
	}

	return fmt.Errorf("用户没有表情反应权限")
}

// checkBotPermissions 检查机器人权限
func (h *MessageReactionAddHandler) checkBotPermissions(ctx context.Context, client *types.Client, channelID string) error {
	// 获取机器人在频道中的权限
	permissions, err := client.Session.UserChannelPermissions(client.Session.State.User.ID, channelID)
	if err != nil {
		return fmt.Errorf("获取机器人权限失败: %w", err)
	}

	// 检查是否有读取消息历史的权限
	if permissions&discordgo.PermissionReadMessageHistory == 0 {
		return fmt.Errorf("机器人没有读取消息历史的权限")
	}

	return nil
}

// shouldLogReaction 根据配置决定是否记录反应事件
func (h *MessageReactionAddHandler) shouldLogReaction(guildID string) bool {
	// 这里可以根据配置文件或数据库设置来决定
	// 暂时默认记录所有反应事件
	return true
}

// logReactionEvent 记录反应事件到日志频道
func (h *MessageReactionAddHandler) logReactionEvent(ctx context.Context, client *types.Client, reaction *discordgo.MessageReactionAdd, action string) error {
	logChannelID := h.getLogChannelID(reaction.GuildID)
	if logChannelID == "" {
		return nil
	}

	// 获取用户信息
	user, err := client.Session.User(reaction.UserID)
	if err != nil {
		logger.Warn("获取用户信息失败", "error", err, "user_id", reaction.UserID)
		user = &discordgo.User{ID: reaction.UserID, Username: "未知用户"}
	}

	embed := &discordgo.MessageEmbed{
		Title: "👍 反应" + action,
		Color: 0x00ff00,
		Fields: []*discordgo.MessageEmbedField{
			{
				Name:   "👤 用户",
				Value:  fmt.Sprintf("%s (%s)", user.Username, user.ID),
				Inline: true,
			},
			{
				Name:   "📝 消息",
				Value:  fmt.Sprintf("[跳转到消息](https://discord.com/channels/%s/%s/%s)", reaction.GuildID, reaction.ChannelID, reaction.MessageID),
				Inline: true,
			},
			{
				Name:   "😀 表情",
				Value:  reaction.Emoji.MessageFormat(),
				Inline: true,
			},
			{
				Name:   "📅 时间",
				Value:  fmt.Sprintf("<t:%d:F>", time.Now().Unix()),
				Inline: true,
			},
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	_, err = client.Session.ChannelMessageSendEmbed(logChannelID, embed)
	return err
}

// getLogChannelID 获取日志频道ID
func (h *MessageReactionAddHandler) getLogChannelID(guildID string) string {
	// 这里可以从配置文件或数据库中获取日志频道ID
	// 暂时返回空字符串，表示不记录日志
	return ""
}

// handleAutomation 处理自动化操作
func (h *MessageReactionAddHandler) handleAutomation(ctx context.Context, client *types.Client, reaction *discordgo.MessageReactionAdd) error {
	// 检查是否启用表情反应过滤
	if !h.config.ReactionFilter.Enabled {
		logger.Debug("表情反应过滤未启用", "message_id", reaction.MessageID)
		return nil
	}

	// 检查是否为过滤表情
	emojiName := reaction.Emoji.Name
	if emojiName != h.config.ReactionFilter.WhitelistEmoji && emojiName != h.config.ReactionFilter.BlacklistEmoji {
		logger.Debug("非过滤表情，跳过处理", "emoji", emojiName, "message_id", reaction.MessageID)
		return nil
	}

	// 检查用户是否有权限创建过滤规则
	if !h.hasReactionFilterPermission(reaction) {
		logger.Debug("用户无权限创建过滤规则，静默跳过", "user_id", reaction.UserID, "emoji", emojiName)
		return nil // 静默跳过，不给用户反馈
	}

	// 检查频道是否存在转发规则
	if !h.hasForwardRules(ctx, client, reaction.ChannelID) {
		logger.Debug("频道无转发规则，跳过过滤规则创建", "channel_id", reaction.ChannelID)
		return nil
	}

	// 获取消息并提取ProductID
	productID, err := h.extractProductIDFromMessage(ctx, client, reaction.ChannelID, reaction.MessageID)
	if err != nil {
		logger.Error("提取ProductID失败", "error", err, "message_id", reaction.MessageID)
		return nil // 不返回错误，避免影响其他处理
	}

	if productID == "" {
		logger.Debug("消息中未找到ProductID，跳过过滤规则创建", "message_id", reaction.MessageID)
		return nil
	}

	// 确定过滤模式
	mode := "blacklist"
	if emojiName == h.config.ReactionFilter.WhitelistEmoji {
		mode = "whitelist"
	}

	// 创建过滤规则
	filterRule := &types.FilterRule{
		Channel:       reaction.ChannelID,
		Keyword:       productID,
		Mode:          mode,
		Source:        "reaction",
		MessageID:     reaction.MessageID,
		ReactionEmoji: emojiName,
		CreatedBy:     reaction.UserID,
	}

	// 添加过滤规则
	if err := h.filterService.AddRule(filterRule); err != nil {
		logger.Error("添加过滤规则失败", "error", err, "product_id", productID, "mode", mode)
		return nil
	}

	logger.Info("表情反应过滤规则已创建",
		"user_id", reaction.UserID,
		"channel_id", reaction.ChannelID,
		"message_id", reaction.MessageID,
		"product_id", productID,
		"mode", mode,
		"emoji", emojiName)

	return nil
}

// MessageReactionRemoveHandler 消息反应移除事件处理器
type MessageReactionRemoveHandler struct {
	eventType     string
	priority      int
	config        *types.Config
	filterService types.FilterEngine
}

// NewMessageReactionRemoveHandler 创建消息反应移除事件处理器
func NewMessageReactionRemoveHandler(config *types.Config, filterService types.FilterEngine) *MessageReactionRemoveHandler {
	return &MessageReactionRemoveHandler{
		eventType:     "MESSAGE_REACTION_REMOVE",
		priority:      0,
		config:        config,
		filterService: filterService,
	}
}

// GetEventType 获取事件类型
func (h *MessageReactionRemoveHandler) GetEventType() string {
	return h.eventType
}

// GetPriority 获取优先级
func (h *MessageReactionRemoveHandler) GetPriority() int {
	return h.priority
}

// ShouldHandle 判断是否应该处理此事件
func (h *MessageReactionRemoveHandler) ShouldHandle(event interface{}) bool {
	_, ok := event.(*discordgo.MessageReactionRemove)
	return ok
}

// Handle 处理事件
func (h *MessageReactionRemoveHandler) Handle(ctx context.Context, client *types.Client, event interface{}) error {
	reaction, ok := event.(*discordgo.MessageReactionRemove)
	if !ok {
		return nil
	}

	logger.Info("用户移除反应",
		"user_id", reaction.UserID,
		"message_id", reaction.MessageID,
		"channel_id", reaction.ChannelID,
		"guild_id", reaction.GuildID,
		"emoji", reaction.Emoji.Name)

	// 检查机器人权限
	if err := h.checkBotPermissions(ctx, client, reaction.ChannelID); err != nil {
		logger.Warn("机器人权限检查失败", "error", err, "channel_id", reaction.ChannelID)
		return nil
	}

	// 根据配置决定是否记录反应事件
	if h.shouldLogReaction(reaction.GuildID) {
		if err := h.logReactionEvent(ctx, client, reaction, "移除"); err != nil {
			logger.Error("记录反应移除事件失败", "error", err)
		}
	}

	// 处理自动化操作
	if err := h.handleAutomation(ctx, client, reaction); err != nil {
		logger.Error("处理反应自动化操作失败", "error", err)
	}

	return nil
}

// checkBotPermissions 检查机器人权限
func (h *MessageReactionRemoveHandler) checkBotPermissions(ctx context.Context, client *types.Client, channelID string) error {
	// 获取机器人在频道中的权限
	permissions, err := client.Session.UserChannelPermissions(client.Session.State.User.ID, channelID)
	if err != nil {
		return fmt.Errorf("获取机器人权限失败: %w", err)
	}

	// 检查是否有读取消息历史的权限
	if permissions&discordgo.PermissionReadMessageHistory == 0 {
		return fmt.Errorf("机器人没有读取消息历史的权限")
	}

	return nil
}

// shouldLogReaction 根据配置决定是否记录反应事件
func (h *MessageReactionRemoveHandler) shouldLogReaction(guildID string) bool {
	// 这里可以根据配置文件或数据库设置来决定
	// 暂时默认记录所有反应事件
	return true
}

// logReactionEvent 记录反应事件到日志频道
func (h *MessageReactionRemoveHandler) logReactionEvent(ctx context.Context, client *types.Client, reaction *discordgo.MessageReactionRemove, action string) error {
	logChannelID := h.getLogChannelID(reaction.GuildID)
	if logChannelID == "" {
		return nil
	}

	// 获取用户信息
	user, err := client.Session.User(reaction.UserID)
	if err != nil {
		logger.Warn("获取用户信息失败", "error", err, "user_id", reaction.UserID)
		user = &discordgo.User{ID: reaction.UserID, Username: "未知用户"}
	}

	embed := &discordgo.MessageEmbed{
		Title: "👎 反应" + action,
		Color: 0xff9900,
		Fields: []*discordgo.MessageEmbedField{
			{
				Name:   "👤 用户",
				Value:  fmt.Sprintf("%s (%s)", user.Username, user.ID),
				Inline: true,
			},
			{
				Name:   "📝 消息",
				Value:  fmt.Sprintf("[跳转到消息](https://discord.com/channels/%s/%s/%s)", reaction.GuildID, reaction.ChannelID, reaction.MessageID),
				Inline: true,
			},
			{
				Name:   "😀 表情",
				Value:  reaction.Emoji.MessageFormat(),
				Inline: true,
			},
			{
				Name:   "📅 时间",
				Value:  fmt.Sprintf("<t:%d:F>", time.Now().Unix()),
				Inline: true,
			},
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	_, err = client.Session.ChannelMessageSendEmbed(logChannelID, embed)
	return err
}

// getLogChannelID 获取日志频道ID
func (h *MessageReactionRemoveHandler) getLogChannelID(guildID string) string {
	// 这里可以从配置文件或数据库中获取日志频道ID
	// 暂时返回空字符串，表示不记录日志
	return ""
}

// handleAutomation 处理自动化操作
func (h *MessageReactionRemoveHandler) handleAutomation(ctx context.Context, client *types.Client, reaction *discordgo.MessageReactionRemove) error {
	// 检查是否启用表情反应过滤
	if !h.config.ReactionFilter.Enabled {
		logger.Debug("表情反应过滤未启用", "message_id", reaction.MessageID)
		return nil
	}

	// 检查是否为过滤表情
	emojiName := reaction.Emoji.Name
	if emojiName != h.config.ReactionFilter.WhitelistEmoji && emojiName != h.config.ReactionFilter.BlacklistEmoji {
		logger.Debug("非过滤表情，跳过处理", "emoji", emojiName, "message_id", reaction.MessageID)
		return nil
	}

	// 获取消息并提取ProductID
	productID, err := h.extractProductIDFromMessage(ctx, client, reaction.ChannelID, reaction.MessageID)
	if err != nil {
		logger.Error("提取ProductID失败", "error", err, "message_id", reaction.MessageID)
		return nil
	}

	if productID == "" {
		logger.Debug("消息中未找到ProductID，跳过过滤规则删除", "message_id", reaction.MessageID)
		return nil
	}

	// 确定过滤模式
	mode := "blacklist"
	if emojiName == h.config.ReactionFilter.WhitelistEmoji {
		mode = "whitelist"
	}

	// 构造唯一ID来查找要删除的规则
	tempRule := &types.FilterRule{
		Channel: reaction.ChannelID,
		Keyword: productID,
		Mode:    mode,
	}
	uniqueID := tempRule.GetUniqueID()

	// 删除过滤规则
	if err := h.filterService.RemoveRule(uniqueID); err != nil {
		logger.Error("删除过滤规则失败", "error", err, "unique_id", uniqueID)
		return nil
	}

	logger.Info("表情反应过滤规则已删除",
		"user_id", reaction.UserID,
		"channel_id", reaction.ChannelID,
		"message_id", reaction.MessageID,
		"product_id", productID,
		"mode", mode,
		"emoji", emojiName)

	return nil
}

// extractProductIDFromMessage 从消息中提取ProductID（复用AddHandler的逻辑）
func (h *MessageReactionRemoveHandler) extractProductIDFromMessage(ctx context.Context, client *types.Client, channelID, messageID string) (string, error) {
	// 获取消息
	message, err := client.Session.ChannelMessage(channelID, messageID)
	if err != nil {
		return "", fmt.Errorf("获取消息失败: %w", err)
	}

	// 使用ProductExtractor提取产品信息
	extractor := types.NewProductExtractor()

	// 将Discord消息转换为map格式
	messageData := h.convertDiscordMessageToMap(message)

	// 提取产品信息
	product := extractor.ExtractFromDiscordMessage(messageData, message.Content)

	if product.ProductID != "" {
		return product.ProductID, nil
	}

	// 如果ProductExtractor没有找到ProductID，尝试使用正则表达式
	productID := h.extractProductIDWithRegex(message.Content)
	if productID != "" {
		return productID, nil
	}

	// 从Embeds中提取
	for _, embed := range message.Embeds {
		if embed.Fields != nil {
			for _, field := range embed.Fields {
				if h.isProductIDField(strings.ToLower(field.Name)) {
					return field.Value, nil
				}
			}
		}
	}

	return "", nil
}

// convertDiscordMessageToMap 将Discord消息转换为map格式（复用AddHandler的逻辑）
func (h *MessageReactionRemoveHandler) convertDiscordMessageToMap(message *discordgo.Message) map[string]interface{} {
	messageMap := map[string]interface{}{
		"id":         message.ID,
		"channel_id": message.ChannelID,
		"content":    message.Content,
		"timestamp":  message.Timestamp,
	}

	if message.Author != nil {
		messageMap["author"] = map[string]interface{}{
			"id":       message.Author.ID,
			"username": message.Author.Username,
		}
	}

	if len(message.Embeds) > 0 {
		embeds := make([]map[string]interface{}, len(message.Embeds))
		for i, embed := range message.Embeds {
			embedMap := make(map[string]interface{})

			if embed.Title != "" {
				embedMap["title"] = embed.Title
			}
			if embed.Description != "" {
				embedMap["description"] = embed.Description
			}
			if embed.URL != "" {
				embedMap["url"] = embed.URL
			}
			if embed.Color != 0 {
				embedMap["color"] = embed.Color
			}

			if embed.Fields != nil {
				fields := make([]map[string]interface{}, len(embed.Fields))
				for j, field := range embed.Fields {
					fields[j] = map[string]interface{}{
						"name":   field.Name,
						"value":  field.Value,
						"inline": field.Inline,
					}
				}
				embedMap["fields"] = fields
			}

			embeds[i] = embedMap
		}
		messageMap["embeds"] = embeds
	}

	return messageMap
}

// extractProductIDWithRegex 使用正则表达式提取ProductID（复用AddHandler的逻辑）
func (h *MessageReactionRemoveHandler) extractProductIDWithRegex(content string) string {
	// 常见的产品ID模式
	patterns := []string{
		`(?i)asin[:\s]*([A-Z0-9]{10})`,         // Amazon ASIN
		`(?i)product[_\s]*id[:\s]*([A-Z0-9]+)`, // 通用Product ID
		`(?i)sku[:\s]*([A-Z0-9\-]+)`,           // SKU
		`(?i)item[_\s]*id[:\s]*([A-Z0-9]+)`,    // Item ID
		`\b[A-Z0-9]{8,15}\b`,                   // 8-15位字母数字组合
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(content)
		if len(matches) > 1 {
			return matches[1]
		}
	}

	return ""
}

// isProductIDField 检查字段名是否为ProductID字段（复用AddHandler的逻辑）
func (h *MessageReactionRemoveHandler) isProductIDField(fieldName string) bool {
	productIDFields := []string{"productid", "product_id", "asin", "id", "产品id", "商品id", "编号", "sku", "item_id"}
	for _, field := range productIDFields {
		if strings.Contains(fieldName, field) {
			return true
		}
	}
	return false
}

// checkReactionRolePermissions 检查表情反应的角色权限
func (h *MessageReactionAddHandler) checkReactionRolePermissions(member *discordgo.Member) bool {
	userID := member.User.ID
	reactionPerms := h.config.Permissions.ReactionPermissions

	// 检查用户ID权限
	for _, allowedUser := range reactionPerms.AllowedUsers {
		if userID == allowedUser {
			logger.Debug("用户ID权限匹配", "user", userID)
			return true
		}
	}

	// 检查角色ID权限
	for _, userRole := range member.Roles {
		for _, allowedRole := range reactionPerms.AllowedRoles {
			if userRole == allowedRole {
				logger.Debug("角色ID权限匹配", "user", userID, "role", userRole)
				return true
			}
		}
	}

	return false
}

// hasPermission 检查是否拥有指定权限
func (h *MessageReactionAddHandler) hasPermission(userPermissions int64, permission string) bool {
	permissionValue := h.getPermissionValue(permission)
	if permissionValue == 0 {
		logger.Warn("未知权限", "permission", permission)
		return false
	}

	return userPermissions&permissionValue != 0
}

// getPermissionValue 获取权限值
func (h *MessageReactionAddHandler) getPermissionValue(permission string) int64 {
	permissionMap := map[string]int64{
		"ADMINISTRATOR":        discordgo.PermissionAdministrator,
		"MANAGE_GUILD":         discordgo.PermissionManageGuild,
		"MANAGE_CHANNELS":      discordgo.PermissionManageChannels,
		"MANAGE_ROLES":         discordgo.PermissionManageRoles,
		"MANAGE_MESSAGES":      discordgo.PermissionManageMessages,
		"KICK_MEMBERS":         discordgo.PermissionKickMembers,
		"BAN_MEMBERS":          discordgo.PermissionBanMembers,
		"SEND_MESSAGES":        discordgo.PermissionSendMessages,
		"READ_MESSAGE_HISTORY": discordgo.PermissionReadMessageHistory,
		"MODERATE_MEMBERS":     discordgo.PermissionModerateMembers,
		"VIEW_CHANNEL":         discordgo.PermissionViewChannel,
		"ADD_REACTIONS":        discordgo.PermissionAddReactions,
		"USE_EXTERNAL_EMOJIS":  discordgo.PermissionUseExternalEmojis,
		"MENTION_EVERYONE":     discordgo.PermissionMentionEveryone,
		"MANAGE_WEBHOOKS":      discordgo.PermissionManageWebhooks,
	}

	return permissionMap[permission]
}

// hasReactionFilterPermission 检查用户是否有权限创建过滤规则
func (h *MessageReactionAddHandler) hasReactionFilterPermission(reaction *discordgo.MessageReactionAdd) bool {
	userID := reaction.UserID

	// 检查管理员用户列表
	for _, adminUser := range h.config.ReactionFilter.AdminUsers {
		if userID == adminUser {
			return true
		}
	}

	// 检查管理员角色列表
	if reaction.Member != nil {
		for _, userRole := range reaction.Member.Roles {
			for _, adminRole := range h.config.ReactionFilter.AdminRoles {
				if userRole == adminRole {
					return true
				}
			}
		}
	}

	// 检查Bot所有者
	for _, ownerID := range h.config.Permissions.BotOwners {
		if userID == ownerID {
			return true
		}
	}

	return false
}

// hasForwardRules 检查频道是否存在转发规则
func (h *MessageReactionAddHandler) hasForwardRules(ctx context.Context, client *types.Client, channelID string) bool {
	// 这里需要检查频道是否存在转发规则（作为源频道或目标频道）
	// 由于我们没有直接访问ForwardRuleService的权限，这里先返回true
	// 在实际实现中，应该通过服务管理器获取ForwardRuleService并检查
	logger.Debug("检查频道转发规则", "channel_id", channelID)
	return true // 临时实现，后续需要完善
}

// extractProductIDFromMessage 从消息中提取ProductID
func (h *MessageReactionAddHandler) extractProductIDFromMessage(ctx context.Context, client *types.Client, channelID, messageID string) (string, error) {
	// 获取消息
	message, err := client.Session.ChannelMessage(channelID, messageID)
	if err != nil {
		return "", fmt.Errorf("获取消息失败: %w", err)
	}

	// 使用ProductExtractor提取产品信息
	extractor := types.NewProductExtractor()

	// 将Discord消息转换为map格式
	messageData := h.convertDiscordMessageToMap(message)

	// 提取产品信息
	product := extractor.ExtractFromDiscordMessage(messageData, message.Content)

	if product.ProductID != "" {
		return product.ProductID, nil
	}

	// 如果ProductExtractor没有找到ProductID，尝试使用正则表达式
	productID := h.extractProductIDWithRegex(message.Content)
	if productID != "" {
		return productID, nil
	}

	// 从Embeds中提取
	for _, embed := range message.Embeds {
		if embed.Fields != nil {
			for _, field := range embed.Fields {
				if h.isProductIDField(strings.ToLower(field.Name)) {
					return field.Value, nil
				}
			}
		}
	}

	return "", nil
}

// convertDiscordMessageToMap 将Discord消息转换为map格式
func (h *MessageReactionAddHandler) convertDiscordMessageToMap(message *discordgo.Message) map[string]interface{} {
	messageMap := map[string]interface{}{
		"id":         message.ID,
		"channel_id": message.ChannelID,
		"content":    message.Content,
		"timestamp":  message.Timestamp,
	}

	if message.Author != nil {
		messageMap["author"] = map[string]interface{}{
			"id":       message.Author.ID,
			"username": message.Author.Username,
		}
	}

	if len(message.Embeds) > 0 {
		embeds := make([]map[string]interface{}, len(message.Embeds))
		for i, embed := range message.Embeds {
			embedMap := make(map[string]interface{})

			if embed.Title != "" {
				embedMap["title"] = embed.Title
			}
			if embed.Description != "" {
				embedMap["description"] = embed.Description
			}
			if embed.URL != "" {
				embedMap["url"] = embed.URL
			}
			if embed.Color != 0 {
				embedMap["color"] = embed.Color
			}

			if embed.Fields != nil {
				fields := make([]map[string]interface{}, len(embed.Fields))
				for j, field := range embed.Fields {
					fields[j] = map[string]interface{}{
						"name":   field.Name,
						"value":  field.Value,
						"inline": field.Inline,
					}
				}
				embedMap["fields"] = fields
			}

			embeds[i] = embedMap
		}
		messageMap["embeds"] = embeds
	}

	return messageMap
}

// extractProductIDWithRegex 使用正则表达式提取ProductID
func (h *MessageReactionAddHandler) extractProductIDWithRegex(content string) string {
	// 常见的产品ID模式
	patterns := []string{
		`(?i)asin[:\s]*([A-Z0-9]{10})`,         // Amazon ASIN
		`(?i)product[_\s]*id[:\s]*([A-Z0-9]+)`, // 通用Product ID
		`(?i)sku[:\s]*([A-Z0-9\-]+)`,           // SKU
		`(?i)item[_\s]*id[:\s]*([A-Z0-9]+)`,    // Item ID
		`\b[A-Z0-9]{8,15}\b`,                   // 8-15位字母数字组合
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(content)
		if len(matches) > 1 {
			return matches[1]
		}
	}

	return ""
}

// isProductIDField 检查字段名是否为ProductID字段
func (h *MessageReactionAddHandler) isProductIDField(fieldName string) bool {
	productIDFields := []string{"productid", "product_id", "asin", "id", "产品id", "商品id", "编号", "sku", "item_id"}
	for _, field := range productIDFields {
		if strings.Contains(fieldName, field) {
			return true
		}
	}
	return false
}
