package bot

import (
	"context"
	"fmt"

	"zeka-go/internal/services"
	"zeka-go/internal/services/channel"
	"zeka-go/internal/services/filter"
	"zeka-go/internal/services/forward"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/services/mapping"
	"zeka-go/internal/services/notification"
	"zeka-go/internal/services/template"
	"zeka-go/internal/tasks"
	"zeka-go/internal/tasks/instock"
	"zeka-go/internal/tasks/message_forward"
	"zeka-go/internal/types"
)

// BotInitializer Bot初始化器 - 负责所有复杂的初始化逻辑
type BotInitializer struct {
	config         *types.Config
	serviceManager *services.ServiceManager
	client         *types.Client
}

// NewBotInitializer 创建Bot初始化器
func NewBotInitializer(config *types.Config, serviceManager *services.ServiceManager, client *types.Client) *BotInitializer {
	return &BotInitializer{
		config:         config,
		serviceManager: serviceManager,
		client:         client,
	}
}

// RegisterAllServices 注册所有服务
func (bi *BotInitializer) RegisterAllServices() error {
	logger.Info("正在注册所有服务...")

	// 注册基础服务
	if err := bi.registerBasicServices(); err != nil {
		return fmt.Errorf("注册基础服务失败: %w", err)
	}

	// 注册业务服务
	if err := bi.registerBusinessServices(); err != nil {
		return fmt.Errorf("注册业务服务失败: %w", err)
	}

	// 注册通知服务
	if err := bi.registerNotificationServices(); err != nil {
		return fmt.Errorf("注册通知服务失败: %w", err)
	}

	logger.Info("✅ 所有服务注册完成")
	return nil
}

// registerBasicServices 注册基础服务
func (bi *BotInitializer) registerBasicServices() error {
	// 注册缓存服务
	if bi.config.Redis.IsEnabled() {
		cacheAdapter := services.NewCacheServiceAdapter(bi.config.Redis)
		if err := bi.serviceManager.RegisterService(cacheAdapter); err != nil {
			return fmt.Errorf("注册缓存服务失败: %w", err)
		}
		logger.Debug("✅ 缓存服务注册成功")
	}

	// 注册队列服务
	if bi.config.Queue.IsEnabled() {
		queueAdapter := services.NewQueueServiceAdapter(bi.config.Queue)
		if err := bi.serviceManager.RegisterService(queueAdapter); err != nil {
			return fmt.Errorf("注册队列服务失败: %w", err)
		}
		logger.Debug("✅ 队列服务注册成功")
	}

	// 注册内存监控服务
	monitorAdapter := services.NewMemoryMonitorServiceAdapter()
	if err := bi.serviceManager.RegisterService(monitorAdapter); err != nil {
		return fmt.Errorf("注册内存监控服务失败: %w", err)
	}
	logger.Debug("✅ 内存监控服务注册成功")

	return nil
}

// registerBusinessServices 注册业务服务
func (bi *BotInitializer) registerBusinessServices() error {
	// 注册字段映射服务
	fieldMappingService := mapping.NewFieldMappingService("configs/field_mapping_groups.yaml")
	if err := bi.serviceManager.RegisterService(fieldMappingService); err != nil {
		return fmt.Errorf("注册字段映射服务失败: %w", err)
	}
	logger.Debug("✅ 字段映射服务注册成功")

	// 注册转发规则服务
	forwardRuleService := forward.NewForwardRuleService("configs/forward_rules.yaml")
	if err := bi.serviceManager.RegisterService(forwardRuleService); err != nil {
		return fmt.Errorf("注册转发规则服务失败: %w", err)
	}
	logger.Debug("✅ 转发规则服务注册成功")

	// 注册过滤规则服务
	filterRuleService := filter.NewFilterRuleService("configs/filter_rules")
	if err := bi.serviceManager.RegisterService(filterRuleService); err != nil {
		return fmt.Errorf("注册过滤规则服务失败: %w", err)
	}
	logger.Debug("✅ 过滤规则服务注册成功")

	// 注册频道名称缓存服务
	channelCache := channel.NewChannelNameCache(bi.client.Session)
	if err := bi.serviceManager.RegisterService(channelCache); err != nil {
		return fmt.Errorf("注册频道名称缓存服务失败: %w", err)
	}
	logger.Debug("✅ 频道名称缓存服务注册成功")

	return nil
}

// registerNotificationServices 注册通知服务
func (bi *BotInitializer) registerNotificationServices() error {
	// 注册产品通知服务
	productNotificationAdapter := notification.NewProductNotificationServiceAdapter()
	if err := bi.serviceManager.RegisterService(productNotificationAdapter); err != nil {
		return fmt.Errorf("注册产品通知服务失败: %w", err)
	}
	logger.Debug("✅ 产品通知服务注册成功")

	return nil
}

// InjectAllDependencies 注入所有服务依赖
func (bi *BotInitializer) InjectAllDependencies() error {
	logger.Info("正在注入服务依赖...")

	// 注入转发服务依赖
	if err := bi.injectForwardServiceDependencies(); err != nil {
		logger.Warn("注入转发服务依赖失败", "error", err)
	}

	// 注入通知服务依赖
	if err := bi.injectNotificationServiceDependencies(); err != nil {
		logger.Warn("注入通知服务依赖失败", "error", err)
	}

	logger.Info("✅ 服务依赖注入完成")
	return nil
}

// injectForwardServiceDependencies 注入转发服务依赖
func (bi *BotInitializer) injectForwardServiceDependencies() error {
	forwardServiceRaw, err := bi.serviceManager.GetService("ForwardRuleService")
	if err != nil {
		return fmt.Errorf("获取ForwardRuleService失败: %w", err)
	}

	forwardService, ok := forwardServiceRaw.(*forward.ForwardRuleService)
	if !ok {
		return fmt.Errorf("ForwardRuleService类型断言失败")
	}

	// 注入基础依赖
	bi.injectBasicDependencies(forwardService)

	logger.Debug("✅ 转发服务依赖注入完成")
	return nil
}

// injectBasicDependencies 注入基础依赖
func (bi *BotInitializer) injectBasicDependencies(forwardService *forward.ForwardRuleService) {
	// 注入队列服务
	if queueServiceRaw, err := bi.serviceManager.GetService("queue"); err == nil {
		if queueAdapter, ok := queueServiceRaw.(*services.QueueServiceAdapter); ok {
			if queueService := queueAdapter.GetService(); queueService != nil {
				forwardService.SetQueueService(queueService)
				logger.Debug("队列服务已注入")
			}
		}
	}

	// 注入映射服务
	if mappingServiceRaw, err := bi.serviceManager.GetService("FieldMappingService"); err == nil {
		if mappingService, ok := mappingServiceRaw.(types.FieldMapper); ok {
			forwardService.SetMappingService(mappingService)
			logger.Debug("映射服务已注入")
		}
	}

	// 注入过滤服务
	if filterServiceRaw, err := bi.serviceManager.GetService("FilterRuleService"); err == nil {
		if filterService, ok := filterServiceRaw.(types.FilterEngine); ok {
			forwardService.SetFilterService(filterService)
			logger.Debug("过滤服务已注入")
		}
	}

	// 注入频道缓存
	if channelCacheRaw, err := bi.serviceManager.GetService("ChannelNameCache"); err == nil {
		if channelCache, ok := channelCacheRaw.(*channel.ChannelNameCache); ok {
			forwardService.SetChannelCache(channelCache)
			logger.Debug("频道缓存已注入")
		}
	}
}

// injectNotificationServiceDependencies 注入通知服务依赖
func (bi *BotInitializer) injectNotificationServiceDependencies() error {
	productNotificationServiceRaw, err := bi.serviceManager.GetService("product_notification")
	if err != nil {
		return fmt.Errorf("获取产品通知服务失败: %w", err)
	}

	productNotificationAdapter, ok := productNotificationServiceRaw.(*notification.ProductNotificationServiceAdapter)
	if !ok {
		return fmt.Errorf("产品通知服务类型断言失败")
	}

	// 注入Discord客户端
	if err := productNotificationAdapter.SetDiscordClient(bi.client); err != nil {
		logger.Warn("注入Discord客户端失败", "error", err)
	} else {
		logger.Debug("Discord客户端已注入到通知服务")
	}

	// 注入模板服务
	templateManager := template.NewManager("templates")
	if err := templateManager.Initialize(context.Background()); err == nil {
		if err := productNotificationAdapter.SetTemplateManager(templateManager); err != nil {
			logger.Warn("注入模板管理器失败", "error", err)
		} else {
			logger.Debug("模板管理器已注入到通知服务")
		}
	}

	logger.Debug("✅ 通知服务依赖注入完成")
	return nil
}

// RegisterTaskModules 注册任务模块
func (bi *BotInitializer) RegisterTaskModules(taskLoader *tasks.TaskLoader) error {
	logger.Info("📦 注册内置任务模块...")

	// 创建所有内置模块
	modules := []tasks.TaskModule{
		instock.NewInStockTaskModule(),
		message_forward.NewMessageForwardTaskModule(),
	}

	// 注册每个模块
	for _, module := range modules {
		// 注入依赖
		bi.injectModuleDependencies(module)

		// 注册模块到任务加载器
		if err := taskLoader.RegisterModule(module); err != nil {
			logger.Error("注册任务模块失败", "module", module.GetName(), "error", err)
			continue
		}

		logger.Info("✅ 任务模块注册成功", "module", module.GetName())
	}

	logger.Info("✅ 所有内置任务模块注册完成", "count", len(modules))
	return nil
}

// injectModuleDependencies 注入模块依赖
func (bi *BotInitializer) injectModuleDependencies(module tasks.TaskModule) {
	if module.GetName() == "message_forward" {
		// 注入转发服务
		if forwardService := bi.getForwardService(); forwardService != nil {
			bi.injectForwardServiceToModule(module, forwardService)
		}

		// 注入通知服务
		if notificationService := bi.getNotificationService(); notificationService != nil {
			bi.injectNotificationServiceToModule(module, notificationService)
		}
	}
}

// getForwardService 获取转发服务
func (bi *BotInitializer) getForwardService() types.ForwardRuleManager {
	if bi.client != nil && bi.client.Services != nil && bi.client.Services.ForwardService != nil {
		return bi.client.Services.ForwardService
	}
	return nil
}

// getNotificationService 获取通知服务
func (bi *BotInitializer) getNotificationService() types.ProductNotificationService {
	// 优先从客户端服务容器获取
	if bi.client != nil && bi.client.Services != nil && bi.client.Services.ProductNotificationService != nil {
		logger.Debug("从客户端服务容器获取产品通知服务")
		return bi.client.Services.ProductNotificationService
	}

	// 从服务管理器获取
	productNotificationServiceRaw, err := bi.serviceManager.GetService("product_notification")
	if err != nil {
		logger.Debug("从服务管理器获取产品通知服务失败", "error", err)
		return nil
	}

	if productNotificationAdapter, ok := productNotificationServiceRaw.(*notification.ProductNotificationServiceAdapter); ok {
		service := productNotificationAdapter.GetService()
		if service != nil {
			logger.Debug("从服务管理器获取产品通知服务成功")
		} else {
			logger.Debug("服务适配器返回了 nil 服务实例")
		}
		return service
	}

	logger.Debug("产品通知服务类型断言失败")
	return nil
}

// injectForwardServiceToModule 注入转发服务到模块
func (bi *BotInitializer) injectForwardServiceToModule(module tasks.TaskModule, forwardService types.ForwardRuleManager) {
	handlers := module.GetHandlers()
	if forwardHandler, exists := handlers["message_forward"]; exists {
		if msgForwardHandler, ok := forwardHandler.(interface {
			SetForwardService(types.ForwardRuleManager)
		}); ok {
			msgForwardHandler.SetForwardService(forwardService)
			logger.Debug("转发服务已注入到消息转发任务处理器")
		}
	}
}

// injectNotificationServiceToModule 注入通知服务到模块
func (bi *BotInitializer) injectNotificationServiceToModule(module tasks.TaskModule, notificationService types.ProductNotificationService) {
	if msgForwardModule, ok := module.(interface {
		SetNotificationService(types.ProductNotificationService)
	}); ok {
		msgForwardModule.SetNotificationService(notificationService)
		logger.Debug("通知服务已注入到消息转发任务模块")
	}
}
