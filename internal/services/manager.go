package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"zeka-go/internal/services/cache"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/services/monitor"
	"zeka-go/internal/services/queue"
	"zeka-go/internal/types"
)

// ServiceStatus 服务状态
type ServiceStatus string

const (
	StatusStopped  ServiceStatus = "stopped"
	StatusStarting ServiceStatus = "starting"
	StatusRunning  ServiceStatus = "running"
	StatusStopping ServiceStatus = "stopping"
	StatusError    ServiceStatus = "error"
	StatusDegraded ServiceStatus = "degraded"
)

// ServiceInfo 服务信息
type ServiceInfo struct {
	Name         string                 `json:"name"`
	Type         string                 `json:"type"`
	Status       ServiceStatus          `json:"status"`
	LastError    string                 `json:"last_error,omitempty"`
	StartTime    *time.Time             `json:"start_time,omitempty"`
	HealthCheck  *HealthCheckResult     `json:"health_check,omitempty"`
	Dependencies []string               `json:"dependencies,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// HealthCheckResult 健康检查结果
type HealthCheckResult struct {
	Healthy      bool          `json:"healthy"`
	Message      string        `json:"message,omitempty"`
	CheckTime    time.Time     `json:"check_time"`
	ResponseTime time.Duration `json:"response_time"`
}

// Service 服务接口
type Service interface {
	// 基础生命周期
	Initialize(ctx context.Context) error
	Start(ctx context.Context) error
	Stop(ctx context.Context) error

	// 健康检查
	HealthCheck(ctx context.Context) *HealthCheckResult

	// 服务信息
	GetName() string
	GetType() string
	GetDependencies() []string
}

// ServiceManager 服务管理器
type ServiceManager struct {
	services     map[string]Service
	serviceInfos map[string]*ServiceInfo
	config       *types.Config

	// 状态管理
	mu        sync.RWMutex
	isRunning bool
	ctx       context.Context
	cancel    context.CancelFunc

	// 健康检查
	healthCheckInterval time.Duration
	healthCheckTicker   *time.Ticker

	// 事件回调
	onServiceStatusChange func(serviceName string, oldStatus, newStatus ServiceStatus)
}

// NewServiceManager 创建服务管理器
func NewServiceManager(config *types.Config) *ServiceManager {
	return &ServiceManager{
		services:            make(map[string]Service),
		serviceInfos:        make(map[string]*ServiceInfo),
		config:              config,
		healthCheckInterval: 30 * time.Second,
	}
}

// RegisterService 注册服务
func (sm *ServiceManager) RegisterService(service Service) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	name := service.GetName()
	if _, exists := sm.services[name]; exists {
		return fmt.Errorf("服务 %s 已存在", name)
	}

	sm.services[name] = service
	sm.serviceInfos[name] = &ServiceInfo{
		Name:         name,
		Type:         service.GetType(),
		Status:       StatusStopped,
		Dependencies: service.GetDependencies(),
		Metadata:     make(map[string]interface{}),
	}

	logger.Info("服务已注册", "name", name, "type", service.GetType())
	return nil
}

// InitializeServices 初始化所有服务
func (sm *ServiceManager) InitializeServices(ctx context.Context) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	logger.Info("开始初始化服务", "count", len(sm.services))

	// 按依赖顺序初始化服务
	initOrder, err := sm.getInitializationOrder()
	if err != nil {
		return fmt.Errorf("计算服务初始化顺序失败: %w", err)
	}

	for _, serviceName := range initOrder {
		service := sm.services[serviceName]
		info := sm.serviceInfos[serviceName]

		logger.Info("正在初始化服务", "name", serviceName)
		sm.updateServiceStatus(serviceName, StatusStarting)

		if err := service.Initialize(ctx); err != nil {
			sm.updateServiceStatus(serviceName, StatusError)
			info.LastError = err.Error()
			logger.Error("服务初始化失败", "name", serviceName, "error", err)
			return fmt.Errorf("初始化服务 %s 失败: %w", serviceName, err)
		}

		sm.updateServiceStatus(serviceName, StatusStopped)
		logger.Info("服务初始化成功", "name", serviceName)
	}

	logger.Info("所有服务初始化完成")
	return nil
}

// StartServices 启动所有服务
func (sm *ServiceManager) StartServices(ctx context.Context) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if sm.isRunning {
		return fmt.Errorf("服务管理器已在运行")
	}

	sm.ctx, sm.cancel = context.WithCancel(ctx)

	logger.Info("开始启动服务", "count", len(sm.services))

	// 按依赖顺序启动服务
	startOrder, err := sm.getInitializationOrder()
	if err != nil {
		return fmt.Errorf("计算服务启动顺序失败: %w", err)
	}

	for _, serviceName := range startOrder {
		service := sm.services[serviceName]
		info := sm.serviceInfos[serviceName]

		logger.Info("正在启动服务", "name", serviceName)
		sm.updateServiceStatus(serviceName, StatusStarting)

		startTime := time.Now()
		if err := service.Start(sm.ctx); err != nil {
			sm.updateServiceStatus(serviceName, StatusError)
			info.LastError = err.Error()
			logger.Error("服务启动失败", "name", serviceName, "error", err)

			// 检查是否允许优雅降级
			if sm.canDegrade(serviceName) {
				logger.Warn("服务启动失败，启用降级模式", "name", serviceName)
				sm.updateServiceStatus(serviceName, StatusDegraded)
				continue
			}

			return fmt.Errorf("启动服务 %s 失败: %w", serviceName, err)
		}

		info.StartTime = &startTime
		sm.updateServiceStatus(serviceName, StatusRunning)
		logger.Info("服务启动成功", "name", serviceName)
	}

	sm.isRunning = true

	// 启动健康检查
	sm.startHealthCheck()

	logger.Info("所有服务启动完成")
	return nil
}

// StopServices 停止所有服务
func (sm *ServiceManager) StopServices(ctx context.Context) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if !sm.isRunning {
		return fmt.Errorf("服务管理器未运行")
	}

	logger.Info("开始停止服务", "count", len(sm.services))

	// 停止健康检查
	sm.stopHealthCheck()

	// 按相反顺序停止服务
	stopOrder, err := sm.getInitializationOrder()
	if err != nil {
		logger.Error("计算服务停止顺序失败", "error", err)
		stopOrder = sm.getAllServiceNames()
	}

	// 反转顺序
	for i := len(stopOrder)/2 - 1; i >= 0; i-- {
		opp := len(stopOrder) - 1 - i
		stopOrder[i], stopOrder[opp] = stopOrder[opp], stopOrder[i]
	}

	var errors []error

	for _, serviceName := range stopOrder {
		service := sm.services[serviceName]
		info := sm.serviceInfos[serviceName]

		if info.Status == StatusStopped || info.Status == StatusError {
			continue
		}

		logger.Info("正在停止服务", "name", serviceName)
		sm.updateServiceStatus(serviceName, StatusStopping)

		if err := service.Stop(ctx); err != nil {
			errors = append(errors, fmt.Errorf("停止服务 %s 失败: %w", serviceName, err))
			sm.updateServiceStatus(serviceName, StatusError)
			info.LastError = err.Error()
			logger.Error("服务停止失败", "name", serviceName, "error", err)
		} else {
			sm.updateServiceStatus(serviceName, StatusStopped)
			info.StartTime = nil
			logger.Info("服务停止成功", "name", serviceName)
		}
	}

	sm.isRunning = false

	if sm.cancel != nil {
		sm.cancel()
	}

	if len(errors) > 0 {
		logger.Warn("部分服务停止失败", "errors", len(errors))
		return fmt.Errorf("停止服务时出现 %d 个错误", len(errors))
	}

	logger.Info("所有服务停止完成")
	return nil
}

// GetService 获取服务实例
func (sm *ServiceManager) GetService(serviceName string) (Service, error) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	service, exists := sm.services[serviceName]
	if !exists {
		return nil, fmt.Errorf("服务 %s 不存在", serviceName)
	}

	return service, nil
}

// GetServiceInfo 获取服务信息
func (sm *ServiceManager) GetServiceInfo(serviceName string) (*ServiceInfo, error) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	info, exists := sm.serviceInfos[serviceName]
	if !exists {
		return nil, fmt.Errorf("服务 %s 不存在", serviceName)
	}

	// 返回副本
	infoCopy := *info
	return &infoCopy, nil
}

// GetAllServicesInfo 获取所有服务信息
func (sm *ServiceManager) GetAllServicesInfo() map[string]*ServiceInfo {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	result := make(map[string]*ServiceInfo)
	for name, info := range sm.serviceInfos {
		infoCopy := *info
		result[name] = &infoCopy
	}

	return result
}

// PerformHealthCheck 执行健康检查
func (sm *ServiceManager) PerformHealthCheck(ctx context.Context) map[string]*HealthCheckResult {
	sm.mu.RLock()
	services := make(map[string]Service)
	for name, service := range sm.services {
		services[name] = service
	}
	sm.mu.RUnlock()

	results := make(map[string]*HealthCheckResult)

	for name, service := range services {
		start := time.Now()
		result := service.HealthCheck(ctx)
		result.ResponseTime = time.Since(start)

		sm.mu.Lock()
		if info, exists := sm.serviceInfos[name]; exists {
			info.HealthCheck = result
		}
		sm.mu.Unlock()

		results[name] = result
	}

	return results
}

// SetOnServiceStatusChange 设置服务状态变更回调
func (sm *ServiceManager) SetOnServiceStatusChange(callback func(serviceName string, oldStatus, newStatus ServiceStatus)) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	sm.onServiceStatusChange = callback
}

// updateServiceStatus 更新服务状态
func (sm *ServiceManager) updateServiceStatus(serviceName string, newStatus ServiceStatus) {
	info := sm.serviceInfos[serviceName]
	oldStatus := info.Status
	info.Status = newStatus

	if sm.onServiceStatusChange != nil {
		go sm.onServiceStatusChange(serviceName, oldStatus, newStatus)
	}
}

// getInitializationOrder 获取服务初始化顺序
func (sm *ServiceManager) getInitializationOrder() ([]string, error) {
	// 简单的拓扑排序实现
	// 这里可以根据需要实现更复杂的依赖解析

	var order []string
	visited := make(map[string]bool)

	// 优先启动基础服务
	baseServices := []string{"logger", "cache", "queue", "monitor"}

	for _, serviceName := range baseServices {
		if _, exists := sm.services[serviceName]; exists && !visited[serviceName] {
			order = append(order, serviceName)
			visited[serviceName] = true
		}
	}

	// 添加其他服务
	for serviceName := range sm.services {
		if !visited[serviceName] {
			order = append(order, serviceName)
			visited[serviceName] = true
		}
	}

	return order, nil
}

// getAllServiceNames 获取所有服务名称
func (sm *ServiceManager) getAllServiceNames() []string {
	var names []string
	for name := range sm.services {
		names = append(names, name)
	}
	return names
}

// canDegrade 检查服务是否可以降级
func (sm *ServiceManager) canDegrade(serviceName string) bool {
	// 根据配置和服务类型判断是否允许降级
	switch serviceName {
	case "cache":
		// 缓存服务通常可以降级（使用内存缓存或禁用缓存）
		return true
	case "queue":
		return sm.config.Queue.EnableGracefulDegradation
	default:
		return false
	}
}

// startHealthCheck 启动健康检查
func (sm *ServiceManager) startHealthCheck() {
	sm.healthCheckTicker = time.NewTicker(sm.healthCheckInterval)

	go func() {
		for {
			select {
			case <-sm.ctx.Done():
				return
			case <-sm.healthCheckTicker.C:
				// 使用context.Background()而不是sm.ctx，因为sm.ctx可能为nil
				ctx := context.Background()
				if sm.ctx != nil {
					ctx = sm.ctx
				}
				sm.PerformHealthCheck(ctx)
			}
		}
	}()

	logger.Info("健康检查已启动", "interval", sm.healthCheckInterval)
}

// stopHealthCheck 停止健康检查
func (sm *ServiceManager) stopHealthCheck() {
	if sm.healthCheckTicker != nil {
		sm.healthCheckTicker.Stop()
		sm.healthCheckTicker = nil
		logger.Info("健康检查已停止")
	}
}

// CacheServiceAdapter 缓存服务适配器
type CacheServiceAdapter struct {
	service types.CacheService
	config  types.RedisConfig
}

// NewCacheServiceAdapter 创建缓存服务适配器
func NewCacheServiceAdapter(config types.RedisConfig) *CacheServiceAdapter {
	return &CacheServiceAdapter{
		config: config,
	}
}

func (c *CacheServiceAdapter) Initialize(ctx context.Context) error {
	if !c.config.IsEnabled() {
		return nil // 服务未启用
	}

	cacheService, err := cache.NewRedisCache(c.config)
	if err != nil {
		return err
	}

	c.service = cacheService
	return nil
}

func (c *CacheServiceAdapter) Start(ctx context.Context) error {
	if c.service == nil {
		return fmt.Errorf("缓存服务未初始化")
	}

	return c.service.Ping(ctx)
}

func (c *CacheServiceAdapter) Stop(ctx context.Context) error {
	if c.service == nil {
		return nil
	}

	return c.service.Close()
}

func (c *CacheServiceAdapter) HealthCheck(ctx context.Context) *HealthCheckResult {
	if c.service == nil {
		return &HealthCheckResult{
			Healthy:   false,
			Message:   "服务未初始化",
			CheckTime: time.Now(),
		}
	}

	start := time.Now()
	err := c.service.Ping(ctx)
	duration := time.Since(start)

	if err != nil {
		return &HealthCheckResult{
			Healthy:      false,
			Message:      err.Error(),
			CheckTime:    time.Now(),
			ResponseTime: duration,
		}
	}

	return &HealthCheckResult{
		Healthy:      true,
		Message:      "服务正常",
		CheckTime:    time.Now(),
		ResponseTime: duration,
	}
}

func (c *CacheServiceAdapter) GetName() string {
	return "cache"
}

func (c *CacheServiceAdapter) GetType() string {
	return "redis"
}

func (c *CacheServiceAdapter) GetDependencies() []string {
	return []string{}
}

func (c *CacheServiceAdapter) GetService() types.CacheService {
	return c.service
}

// QueueServiceAdapter 队列服务适配器
type QueueServiceAdapter struct {
	service types.QueueService
	config  types.QueueConfig
}

// NewQueueServiceAdapter 创建队列服务适配器
func NewQueueServiceAdapter(config types.QueueConfig) *QueueServiceAdapter {
	return &QueueServiceAdapter{
		config: config,
	}
}

func (q *QueueServiceAdapter) Initialize(ctx context.Context) error {
	if !q.config.IsEnabled() {
		return nil // 服务未启用
	}

	queueService, err := queue.NewRabbitMQService(q.config)
	if err != nil {
		return err
	}

	q.service = queueService
	return nil
}

func (q *QueueServiceAdapter) Start(ctx context.Context) error {
	if q.service == nil {
		return fmt.Errorf("队列服务未初始化")
	}

	return q.service.Connect(ctx)
}

func (q *QueueServiceAdapter) Stop(ctx context.Context) error {
	if q.service == nil {
		return nil
	}

	return q.service.Close()
}

func (q *QueueServiceAdapter) HealthCheck(ctx context.Context) *HealthCheckResult {
	if q.service == nil {
		return &HealthCheckResult{
			Healthy:   false,
			Message:   "服务未初始化",
			CheckTime: time.Now(),
		}
	}

	start := time.Now()
	// 这里可以添加具体的健康检查逻辑
	// 暂时返回成功
	duration := time.Since(start)

	return &HealthCheckResult{
		Healthy:      true,
		Message:      "服务正常",
		CheckTime:    time.Now(),
		ResponseTime: duration,
	}
}

func (q *QueueServiceAdapter) GetName() string {
	return "queue"
}

func (q *QueueServiceAdapter) GetType() string {
	return "rabbitmq"
}

func (q *QueueServiceAdapter) GetDependencies() []string {
	return []string{}
}

func (q *QueueServiceAdapter) GetService() types.QueueService {
	return q.service
}

// MemoryMonitorServiceAdapter 内存监控服务适配器
type MemoryMonitorServiceAdapter struct {
	monitor *monitor.MemoryMonitor
}

// NewMemoryMonitorServiceAdapter 创建内存监控服务适配器
func NewMemoryMonitorServiceAdapter() *MemoryMonitorServiceAdapter {
	return &MemoryMonitorServiceAdapter{}
}

func (m *MemoryMonitorServiceAdapter) Initialize(ctx context.Context) error {
	m.monitor = monitor.NewMemoryMonitor(30 * time.Second)
	return nil
}

func (m *MemoryMonitorServiceAdapter) Start(ctx context.Context) error {
	if m.monitor == nil {
		return fmt.Errorf("内存监控未初始化")
	}

	go m.monitor.Start(ctx)
	return nil
}

func (m *MemoryMonitorServiceAdapter) Stop(ctx context.Context) error {
	if m.monitor == nil {
		return nil
	}

	m.monitor.Stop()
	return nil
}

func (m *MemoryMonitorServiceAdapter) HealthCheck(ctx context.Context) *HealthCheckResult {
	if m.monitor == nil {
		return &HealthCheckResult{
			Healthy:   false,
			Message:   "服务未初始化",
			CheckTime: time.Now(),
		}
	}

	stats := m.monitor.GetStats()
	if stats == nil {
		return &HealthCheckResult{
			Healthy:   false,
			Message:   "无法获取内存统计",
			CheckTime: time.Now(),
		}
	}

	return &HealthCheckResult{
		Healthy:   true,
		Message:   fmt.Sprintf("内存使用: %.1f%%", m.monitor.GetMemoryUsagePercent()),
		CheckTime: time.Now(),
	}
}

func (m *MemoryMonitorServiceAdapter) GetName() string {
	return "monitor"
}

func (m *MemoryMonitorServiceAdapter) GetType() string {
	return "memory"
}

func (m *MemoryMonitorServiceAdapter) GetDependencies() []string {
	return []string{}
}

func (m *MemoryMonitorServiceAdapter) GetMonitor() *monitor.MemoryMonitor {
	return m.monitor
}
