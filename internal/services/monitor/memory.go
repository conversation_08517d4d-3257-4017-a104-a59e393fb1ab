package monitor

import (
	"context"
	"runtime"
	"sync"
	"time"

	"zeka-go/internal/services/logger"
)

// MemoryStats 内存统计信息
type MemoryStats struct {
	// 基础内存信息
	Alloc      uint64 // 当前分配的内存
	TotalAlloc uint64 // 累计分配的内存
	Sys        uint64 // 系统内存
	Lookups    uint64 // 指针查找次数
	Mallocs    uint64 // 内存分配次数
	Frees      uint64 // 内存释放次数

	// 堆内存信息
	HeapAlloc    uint64 // 堆分配的内存
	HeapSys      uint64 // 堆系统内存
	HeapIdle     uint64 // 堆空闲内存
	HeapInuse    uint64 // 堆使用中的内存
	HeapReleased uint64 // 堆释放的内存
	HeapObjects  uint64 // 堆对象数量

	// GC信息
	NextGC       uint64 // 下次GC的内存阈值
	LastGC       uint64 // 上次GC时间
	PauseTotalNs uint64 // GC暂停总时间
	NumGC        uint32 // GC次数
	NumForcedGC  uint32 // 强制GC次数

	// Goroutine信息
	NumGoroutine int // Goroutine数量

	// 时间戳
	Timestamp time.Time
}

// MemoryAlert 内存告警
type MemoryAlert struct {
	Type      string    // 告警类型
	Level     string    // 告警级别 (warning, critical)
	Message   string    // 告警消息
	Value     uint64    // 当前值
	Threshold uint64    // 阈值
	Timestamp time.Time // 告警时间
}

// MemoryMonitor 内存监控器
type MemoryMonitor struct {
	// 配置
	interval       time.Duration
	alertThreshold MemoryThreshold

	// 状态
	isRunning bool
	stopCh    chan struct{}
	mu        sync.RWMutex

	// 统计
	stats       *MemoryStats
	alerts      []MemoryAlert
	maxAlerts   int
	alertsMu    sync.RWMutex

	// 回调
	alertCallback func(alert MemoryAlert)
}

// MemoryThreshold 内存阈值配置
type MemoryThreshold struct {
	// 内存使用阈值 (字节)
	AllocWarning   uint64 // 分配内存警告阈值
	AllocCritical  uint64 // 分配内存严重阈值
	HeapWarning    uint64 // 堆内存警告阈值
	HeapCritical   uint64 // 堆内存严重阈值

	// Goroutine数量阈值
	GoroutineWarning  int // Goroutine警告阈值
	GoroutineCritical int // Goroutine严重阈值

	// GC频率阈值 (每分钟)
	GCFrequencyWarning  int // GC频率警告阈值
	GCFrequencyCritical int // GC频率严重阈值
}

// NewMemoryMonitor 创建内存监控器
func NewMemoryMonitor(interval time.Duration) *MemoryMonitor {
	return &MemoryMonitor{
		interval: interval,
		alertThreshold: MemoryThreshold{
			AllocWarning:        100 * 1024 * 1024,  // 100MB
			AllocCritical:       500 * 1024 * 1024,  // 500MB
			HeapWarning:         200 * 1024 * 1024,  // 200MB
			HeapCritical:        1024 * 1024 * 1024, // 1GB
			GoroutineWarning:    1000,
			GoroutineCritical:   5000,
			GCFrequencyWarning:  10,
			GCFrequencyCritical: 30,
		},
		stopCh:    make(chan struct{}),
		maxAlerts: 100,
		stats:     &MemoryStats{},
	}
}

// SetAlertCallback 设置告警回调
func (mm *MemoryMonitor) SetAlertCallback(callback func(alert MemoryAlert)) {
	mm.mu.Lock()
	defer mm.mu.Unlock()
	mm.alertCallback = callback
}

// SetThreshold 设置阈值
func (mm *MemoryMonitor) SetThreshold(threshold MemoryThreshold) {
	mm.mu.Lock()
	defer mm.mu.Unlock()
	mm.alertThreshold = threshold
}

// Start 启动内存监控
func (mm *MemoryMonitor) Start(ctx context.Context) {
	mm.mu.Lock()
	if mm.isRunning {
		mm.mu.Unlock()
		return
	}
	mm.isRunning = true
	mm.mu.Unlock()

	logger.Info("内存监控器启动", "interval", mm.interval)

	ticker := time.NewTicker(mm.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			mm.stop()
			return
		case <-mm.stopCh:
			return
		case <-ticker.C:
			mm.collectStats()
			mm.checkAlerts()
		}
	}
}

// Stop 停止内存监控
func (mm *MemoryMonitor) Stop() {
	mm.mu.Lock()
	defer mm.mu.Unlock()

	if !mm.isRunning {
		return
	}

	mm.stop()
}

// stop 内部停止方法
func (mm *MemoryMonitor) stop() {
	mm.isRunning = false
	close(mm.stopCh)
	logger.Info("内存监控器已停止")
}

// collectStats 收集内存统计信息
func (mm *MemoryMonitor) collectStats() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	mm.mu.Lock()
	mm.stats = &MemoryStats{
		Alloc:        m.Alloc,
		TotalAlloc:   m.TotalAlloc,
		Sys:          m.Sys,
		Lookups:      m.Lookups,
		Mallocs:      m.Mallocs,
		Frees:        m.Frees,
		HeapAlloc:    m.HeapAlloc,
		HeapSys:      m.HeapSys,
		HeapIdle:     m.HeapIdle,
		HeapInuse:    m.HeapInuse,
		HeapReleased: m.HeapReleased,
		HeapObjects:  m.HeapObjects,
		NextGC:       m.NextGC,
		LastGC:       m.LastGC,
		PauseTotalNs: m.PauseTotalNs,
		NumGC:        m.NumGC,
		NumForcedGC:  m.NumForcedGC,
		NumGoroutine: runtime.NumGoroutine(),
		Timestamp:    time.Now(),
	}
	mm.mu.Unlock()

	// 记录详细的内存信息
	logger.Debug("内存统计",
		"alloc_mb", mm.stats.Alloc/1024/1024,
		"heap_alloc_mb", mm.stats.HeapAlloc/1024/1024,
		"heap_sys_mb", mm.stats.HeapSys/1024/1024,
		"heap_objects", mm.stats.HeapObjects,
		"num_goroutine", mm.stats.NumGoroutine,
		"num_gc", mm.stats.NumGC)
}

// checkAlerts 检查告警条件
func (mm *MemoryMonitor) checkAlerts() {
	mm.mu.RLock()
	stats := mm.stats
	threshold := mm.alertThreshold
	mm.mu.RUnlock()

	// 检查分配内存
	if stats.Alloc >= threshold.AllocCritical {
		mm.addAlert("memory_alloc", "critical", 
			"分配内存使用过高", stats.Alloc, threshold.AllocCritical)
	} else if stats.Alloc >= threshold.AllocWarning {
		mm.addAlert("memory_alloc", "warning", 
			"分配内存使用较高", stats.Alloc, threshold.AllocWarning)
	}

	// 检查堆内存
	if stats.HeapAlloc >= threshold.HeapCritical {
		mm.addAlert("heap_alloc", "critical", 
			"堆内存使用过高", stats.HeapAlloc, threshold.HeapCritical)
	} else if stats.HeapAlloc >= threshold.HeapWarning {
		mm.addAlert("heap_alloc", "warning", 
			"堆内存使用较高", stats.HeapAlloc, threshold.HeapWarning)
	}

	// 检查Goroutine数量
	if stats.NumGoroutine >= threshold.GoroutineCritical {
		mm.addAlert("goroutine_count", "critical", 
			"Goroutine数量过多", uint64(stats.NumGoroutine), uint64(threshold.GoroutineCritical))
	} else if stats.NumGoroutine >= threshold.GoroutineWarning {
		mm.addAlert("goroutine_count", "warning", 
			"Goroutine数量较多", uint64(stats.NumGoroutine), uint64(threshold.GoroutineWarning))
	}
}

// addAlert 添加告警
func (mm *MemoryMonitor) addAlert(alertType, level, message string, value, threshold uint64) {
	alert := MemoryAlert{
		Type:      alertType,
		Level:     level,
		Message:   message,
		Value:     value,
		Threshold: threshold,
		Timestamp: time.Now(),
	}

	mm.alertsMu.Lock()
	// 限制告警数量
	if len(mm.alerts) >= mm.maxAlerts {
		mm.alerts = mm.alerts[1:]
	}
	mm.alerts = append(mm.alerts, alert)
	mm.alertsMu.Unlock()

	// 记录告警日志
	if level == "critical" {
		logger.Error("内存告警", 
			"type", alertType,
			"level", level,
			"message", message,
			"value_mb", value/1024/1024,
			"threshold_mb", threshold/1024/1024)
	} else {
		logger.Warn("内存告警", 
			"type", alertType,
			"level", level,
			"message", message,
			"value_mb", value/1024/1024,
			"threshold_mb", threshold/1024/1024)
	}

	// 调用回调函数
	mm.mu.RLock()
	callback := mm.alertCallback
	mm.mu.RUnlock()

	if callback != nil {
		go callback(alert)
	}
}

// GetStats 获取当前内存统计
func (mm *MemoryMonitor) GetStats() *MemoryStats {
	mm.mu.RLock()
	defer mm.mu.RUnlock()
	
	if mm.stats == nil {
		return nil
	}
	
	// 返回副本
	stats := *mm.stats
	return &stats
}

// GetAlerts 获取告警历史
func (mm *MemoryMonitor) GetAlerts() []MemoryAlert {
	mm.alertsMu.RLock()
	defer mm.alertsMu.RUnlock()
	
	// 返回副本
	alerts := make([]MemoryAlert, len(mm.alerts))
	copy(alerts, mm.alerts)
	return alerts
}

// ForceGC 强制执行垃圾回收
func (mm *MemoryMonitor) ForceGC() {
	logger.Info("强制执行垃圾回收")
	runtime.GC()
	
	// 重新收集统计信息
	mm.collectStats()
}

// GetMemoryUsagePercent 获取内存使用百分比
func (mm *MemoryMonitor) GetMemoryUsagePercent() float64 {
	mm.mu.RLock()
	defer mm.mu.RUnlock()
	
	if mm.stats == nil || mm.stats.Sys == 0 {
		return 0
	}
	
	return float64(mm.stats.Alloc) / float64(mm.stats.Sys) * 100
}
