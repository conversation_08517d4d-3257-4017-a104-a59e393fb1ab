package health

import (
	"context"
	"fmt"
	"sync"
	"time"

	"zeka-go/internal/services/logger"
)

// HealthStatus 健康状态
type HealthStatus string

const (
	StatusHealthy   HealthStatus = "healthy"
	StatusUnhealthy HealthStatus = "unhealthy"
	StatusDegraded  HealthStatus = "degraded"
	StatusUnknown   HealthStatus = "unknown"
)

// CheckResult 健康检查结果
type CheckResult struct {
	Name         string                 `json:"name"`
	Status       HealthStatus           `json:"status"`
	Message      string                 `json:"message,omitempty"`
	Error        string                 `json:"error,omitempty"`
	ResponseTime time.Duration          `json:"response_time"`
	CheckTime    time.Time              `json:"check_time"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// HealthChecker 健康检查器接口
type HealthChecker interface {
	// GetName 获取检查器名称
	GetName() string
	
	// Check 执行健康检查
	Check(ctx context.Context) *CheckResult
	
	// GetTimeout 获取检查超时时间
	GetTimeout() time.Duration
}

// ServiceHealthChecker 服务健康检查器
type ServiceHealthChecker struct {
	name        string
	description string
	timeout     time.Duration
	checkFunc   func(context.Context) error
	metadata    map[string]interface{}
}

// NewServiceHealthChecker 创建服务健康检查器
func NewServiceHealthChecker(name, description string, timeout time.Duration, checkFunc func(context.Context) error) *ServiceHealthChecker {
	return &ServiceHealthChecker{
		name:        name,
		description: description,
		timeout:     timeout,
		checkFunc:   checkFunc,
		metadata:    make(map[string]interface{}),
	}
}

// GetName 获取检查器名称
func (shc *ServiceHealthChecker) GetName() string {
	return shc.name
}

// Check 执行健康检查
func (shc *ServiceHealthChecker) Check(ctx context.Context) *CheckResult {
	start := time.Now()
	
	// 创建带超时的上下文
	checkCtx, cancel := context.WithTimeout(ctx, shc.timeout)
	defer cancel()
	
	result := &CheckResult{
		Name:      shc.name,
		CheckTime: start,
		Metadata:  make(map[string]interface{}),
	}
	
	// 复制元数据
	for k, v := range shc.metadata {
		result.Metadata[k] = v
	}
	
	// 执行检查
	err := shc.checkFunc(checkCtx)
	result.ResponseTime = time.Since(start)
	
	if err != nil {
		result.Status = StatusUnhealthy
		result.Error = err.Error()
		result.Message = fmt.Sprintf("健康检查失败: %s", err.Error())
	} else {
		result.Status = StatusHealthy
		result.Message = "服务正常"
	}
	
	return result
}

// GetTimeout 获取检查超时时间
func (shc *ServiceHealthChecker) GetTimeout() time.Duration {
	return shc.timeout
}

// SetMetadata 设置元数据
func (shc *ServiceHealthChecker) SetMetadata(key string, value interface{}) {
	shc.metadata[key] = value
}

// HealthCheckManager 健康检查管理器
type HealthCheckManager struct {
	checkers map[string]HealthChecker
	mu       sync.RWMutex
	
	// 配置
	defaultTimeout time.Duration
	maxConcurrent  int
	
	// 统计
	stats *ManagerStats
}

// ManagerStats 管理器统计信息
type ManagerStats struct {
	TotalChecks    int64     `json:"total_checks"`
	SuccessfulChecks int64   `json:"successful_checks"`
	FailedChecks   int64     `json:"failed_checks"`
	LastCheckTime  time.Time `json:"last_check_time"`
	AverageResponseTime time.Duration `json:"average_response_time"`
}

// NewHealthCheckManager 创建健康检查管理器
func NewHealthCheckManager() *HealthCheckManager {
	return &HealthCheckManager{
		checkers:       make(map[string]HealthChecker),
		defaultTimeout: 30 * time.Second,
		maxConcurrent:  10,
		stats: &ManagerStats{
			LastCheckTime: time.Now(),
		},
	}
}

// RegisterChecker 注册健康检查器
func (hcm *HealthCheckManager) RegisterChecker(checker HealthChecker) error {
	hcm.mu.Lock()
	defer hcm.mu.Unlock()
	
	name := checker.GetName()
	if _, exists := hcm.checkers[name]; exists {
		return fmt.Errorf("健康检查器 %s 已存在", name)
	}
	
	hcm.checkers[name] = checker
	logger.Info("健康检查器已注册", "name", name)
	return nil
}

// UnregisterChecker 注销健康检查器
func (hcm *HealthCheckManager) UnregisterChecker(name string) error {
	hcm.mu.Lock()
	defer hcm.mu.Unlock()
	
	if _, exists := hcm.checkers[name]; !exists {
		return fmt.Errorf("健康检查器 %s 不存在", name)
	}
	
	delete(hcm.checkers, name)
	logger.Info("健康检查器已注销", "name", name)
	return nil
}

// CheckAll 执行所有健康检查
func (hcm *HealthCheckManager) CheckAll(ctx context.Context) map[string]*CheckResult {
	hcm.mu.RLock()
	checkers := make(map[string]HealthChecker)
	for name, checker := range hcm.checkers {
		checkers[name] = checker
	}
	hcm.mu.RUnlock()
	
	results := make(map[string]*CheckResult)
	resultsChan := make(chan struct {
		name   string
		result *CheckResult
	}, len(checkers))
	
	// 限制并发数
	semaphore := make(chan struct{}, hcm.maxConcurrent)
	
	// 启动所有检查
	for name, checker := range checkers {
		go func(n string, c HealthChecker) {
			semaphore <- struct{}{} // 获取信号量
			defer func() { <-semaphore }() // 释放信号量
			
			result := c.Check(ctx)
			resultsChan <- struct {
				name   string
				result *CheckResult
			}{n, result}
		}(name, checker)
	}
	
	// 收集结果
	for i := 0; i < len(checkers); i++ {
		select {
		case res := <-resultsChan:
			results[res.name] = res.result
		case <-ctx.Done():
			// 上下文取消，返回已收集的结果
			logger.Warn("健康检查被取消", "collected", len(results), "total", len(checkers))
			break
		}
	}
	
	// 更新统计信息
	hcm.updateStats(results)
	
	return results
}

// CheckOne 执行单个健康检查
func (hcm *HealthCheckManager) CheckOne(ctx context.Context, name string) (*CheckResult, error) {
	hcm.mu.RLock()
	checker, exists := hcm.checkers[name]
	hcm.mu.RUnlock()
	
	if !exists {
		return nil, fmt.Errorf("健康检查器 %s 不存在", name)
	}
	
	result := checker.Check(ctx)
	
	// 更新统计信息
	hcm.updateStats(map[string]*CheckResult{name: result})
	
	return result, nil
}

// GetOverallStatus 获取整体健康状态
func (hcm *HealthCheckManager) GetOverallStatus(ctx context.Context) HealthStatus {
	results := hcm.CheckAll(ctx)
	
	if len(results) == 0 {
		return StatusUnknown
	}
	
	healthyCount := 0
	degradedCount := 0
	
	for _, result := range results {
		switch result.Status {
		case StatusHealthy:
			healthyCount++
		case StatusDegraded:
			degradedCount++
		case StatusUnhealthy:
			// 如果有任何服务不健康，整体状态为不健康
			return StatusUnhealthy
		}
	}
	
	// 如果所有服务都健康
	if healthyCount == len(results) {
		return StatusHealthy
	}
	
	// 如果有降级服务但没有不健康的服务
	if degradedCount > 0 {
		return StatusDegraded
	}
	
	return StatusUnknown
}

// GetStats 获取统计信息
func (hcm *HealthCheckManager) GetStats() *ManagerStats {
	hcm.mu.RLock()
	defer hcm.mu.RUnlock()
	
	// 返回副本
	stats := *hcm.stats
	return &stats
}

// GetCheckerNames 获取所有检查器名称
func (hcm *HealthCheckManager) GetCheckerNames() []string {
	hcm.mu.RLock()
	defer hcm.mu.RUnlock()
	
	names := make([]string, 0, len(hcm.checkers))
	for name := range hcm.checkers {
		names = append(names, name)
	}
	
	return names
}

// SetDefaultTimeout 设置默认超时时间
func (hcm *HealthCheckManager) SetDefaultTimeout(timeout time.Duration) {
	hcm.mu.Lock()
	defer hcm.mu.Unlock()
	
	hcm.defaultTimeout = timeout
}

// SetMaxConcurrent 设置最大并发数
func (hcm *HealthCheckManager) SetMaxConcurrent(max int) {
	hcm.mu.Lock()
	defer hcm.mu.Unlock()
	
	hcm.maxConcurrent = max
}

// updateStats 更新统计信息
func (hcm *HealthCheckManager) updateStats(results map[string]*CheckResult) {
	hcm.mu.Lock()
	defer hcm.mu.Unlock()
	
	totalResponseTime := time.Duration(0)
	successCount := int64(0)
	failCount := int64(0)
	
	for _, result := range results {
		hcm.stats.TotalChecks++
		totalResponseTime += result.ResponseTime
		
		if result.Status == StatusHealthy {
			successCount++
		} else {
			failCount++
		}
	}
	
	hcm.stats.SuccessfulChecks += successCount
	hcm.stats.FailedChecks += failCount
	hcm.stats.LastCheckTime = time.Now()
	
	// 计算平均响应时间
	if len(results) > 0 {
		hcm.stats.AverageResponseTime = totalResponseTime / time.Duration(len(results))
	}
}

// CreateRedisHealthChecker 创建Redis健康检查器
func CreateRedisHealthChecker(pingFunc func(context.Context) error) HealthChecker {
	return NewServiceHealthChecker(
		"redis",
		"Redis缓存服务健康检查",
		5*time.Second,
		pingFunc,
	)
}

// CreateRabbitMQHealthChecker 创建RabbitMQ健康检查器
func CreateRabbitMQHealthChecker(checkFunc func(context.Context) error) HealthChecker {
	return NewServiceHealthChecker(
		"rabbitmq",
		"RabbitMQ队列服务健康检查",
		10*time.Second,
		checkFunc,
	)
}

// CreateDiscordHealthChecker 创建Discord健康检查器
func CreateDiscordHealthChecker(checkFunc func(context.Context) error) HealthChecker {
	return NewServiceHealthChecker(
		"discord",
		"Discord API连接健康检查",
		15*time.Second,
		checkFunc,
	)
}

// CreateDatabaseHealthChecker 创建数据库健康检查器
func CreateDatabaseHealthChecker(pingFunc func(context.Context) error) HealthChecker {
	return NewServiceHealthChecker(
		"database",
		"数据库连接健康检查",
		10*time.Second,
		pingFunc,
	)
}

// CreateCustomHealthChecker 创建自定义健康检查器
func CreateCustomHealthChecker(name, description string, timeout time.Duration, checkFunc func(context.Context) error) HealthChecker {
	return NewServiceHealthChecker(name, description, timeout, checkFunc)
}
