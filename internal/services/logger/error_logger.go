package logger

import (
	"context"
	"fmt"
	"runtime"
	"time"

	"zeka-go/internal/types"
)

// ErrorLogger 标准化错误日志记录器
type ErrorLogger struct {
	logger *Logger
}

// NewErrorLogger 创建错误日志记录器
func NewErrorLogger(logger *Logger) *ErrorLogger {
	return &ErrorLogger{
		logger: logger,
	}
}

// StandardErrorFields 标准错误字段
type StandardErrorFields struct {
	// 基础字段
	ErrorMessage string                 `json:"error_message"`
	ErrorType    string                 `json:"error_type"`
	ErrorCode    string                 `json:"error_code,omitempty"`
	Timestamp    time.Time              `json:"timestamp"`
	
	// 上下文字段
	UserID    string `json:"user_id,omitempty"`
	GuildID   string `json:"guild_id,omitempty"`
	ChannelID string `json:"channel_id,omitempty"`
	
	// 操作字段
	Operation string `json:"operation,omitempty"`
	Component string `json:"component,omitempty"`
	Function  string `json:"function,omitempty"`
	
	// 请求字段
	RequestID   string        `json:"request_id,omitempty"`
	Duration    time.Duration `json:"duration,omitempty"`
	
	// 技术字段
	StackTrace string                 `json:"stack_trace,omitempty"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// LogError 记录标准化错误
func (el *ErrorLogger) LogError(ctx context.Context, err error, operation string, fields ...interface{}) {
	standardFields := el.buildStandardFields(ctx, err, operation)
	
	// 构建日志字段
	logFields := []interface{}{
		"error_message", standardFields.ErrorMessage,
		"error_type", standardFields.ErrorType,
		"timestamp", standardFields.Timestamp,
		"operation", standardFields.Operation,
	}
	
	// 添加错误码（如果有）
	if standardFields.ErrorCode != "" {
		logFields = append(logFields, "error_code", standardFields.ErrorCode)
	}
	
	// 添加上下文字段
	if standardFields.UserID != "" {
		logFields = append(logFields, "user_id", standardFields.UserID)
	}
	if standardFields.GuildID != "" {
		logFields = append(logFields, "guild_id", standardFields.GuildID)
	}
	if standardFields.ChannelID != "" {
		logFields = append(logFields, "channel_id", standardFields.ChannelID)
	}
	if standardFields.RequestID != "" {
		logFields = append(logFields, "request_id", standardFields.RequestID)
	}
	
	// 添加组件和函数信息
	if standardFields.Component != "" {
		logFields = append(logFields, "component", standardFields.Component)
	}
	if standardFields.Function != "" {
		logFields = append(logFields, "function", standardFields.Function)
	}
	
	// 添加持续时间
	if standardFields.Duration > 0 {
		logFields = append(logFields, "duration", standardFields.Duration)
	}
	
	// 添加堆栈跟踪（对于严重错误）
	if standardFields.StackTrace != "" {
		logFields = append(logFields, "stack_trace", standardFields.StackTrace)
	}
	
	// 添加元数据
	if len(standardFields.Metadata) > 0 {
		for key, value := range standardFields.Metadata {
			logFields = append(logFields, key, value)
		}
	}
	
	// 添加额外字段
	logFields = append(logFields, fields...)
	
	// 记录错误
	el.logger.Error("错误发生", logFields...)
}

// LogErrorWithSeverity 记录带严重程度的错误
func (el *ErrorLogger) LogErrorWithSeverity(ctx context.Context, err error, operation string, severity string, fields ...interface{}) {
	// 添加严重程度字段
	allFields := append([]interface{}{"severity", severity}, fields...)
	el.LogError(ctx, err, operation, allFields...)
}

// LogPanic 记录panic错误
func (el *ErrorLogger) LogPanic(ctx context.Context, panicValue interface{}, operation string) {
	// 获取堆栈信息
	buf := make([]byte, 4096)
	n := runtime.Stack(buf, false)
	stackTrace := string(buf[:n])
	
	// 创建panic错误
	err := fmt.Errorf("panic: %v", panicValue)
	
	el.LogError(ctx, err, operation,
		"panic_value", panicValue,
		"stack_trace", stackTrace,
		"severity", "critical",
		"error_type", "panic")
}

// LogServiceError 记录服务错误
func (el *ErrorLogger) LogServiceError(ctx context.Context, service, operation string, err error, fields ...interface{}) {
	allFields := append([]interface{}{
		"service", service,
		"component", "service",
	}, fields...)
	
	el.LogError(ctx, err, operation, allFields...)
}

// LogTaskError 记录任务错误
func (el *ErrorLogger) LogTaskError(ctx context.Context, taskID, taskType string, err error, fields ...interface{}) {
	allFields := append([]interface{}{
		"task_id", taskID,
		"task_type", taskType,
		"component", "task",
	}, fields...)
	
	el.LogError(ctx, err, "task_processing", allFields...)
}

// LogDiscordError 记录Discord相关错误
func (el *ErrorLogger) LogDiscordError(ctx context.Context, operation string, err error, fields ...interface{}) {
	allFields := append([]interface{}{
		"component", "discord",
		"api", "discord",
	}, fields...)
	
	el.LogError(ctx, err, operation, allFields...)
}

// LogNetworkError 记录网络错误
func (el *ErrorLogger) LogNetworkError(ctx context.Context, method, url string, statusCode int, err error, duration time.Duration, fields ...interface{}) {
	allFields := append([]interface{}{
		"http_method", method,
		"url", url,
		"status_code", statusCode,
		"duration", duration,
		"component", "network",
	}, fields...)
	
	el.LogError(ctx, err, "network_request", allFields...)
}

// buildStandardFields 构建标准字段
func (el *ErrorLogger) buildStandardFields(ctx context.Context, err error, operation string) *StandardErrorFields {
	fields := &StandardErrorFields{
		ErrorMessage: err.Error(),
		ErrorType:    fmt.Sprintf("%T", err),
		Timestamp:    time.Now(),
		Operation:    operation,
		Metadata:     make(map[string]interface{}),
	}
	
	// 从上下文提取信息
	if userID := ctx.Value("user_id"); userID != nil {
		if uid, ok := userID.(string); ok {
			fields.UserID = uid
		}
	}
	
	if guildID := ctx.Value("guild_id"); guildID != nil {
		if gid, ok := guildID.(string); ok {
			fields.GuildID = gid
		}
	}
	
	if channelID := ctx.Value("channel_id"); channelID != nil {
		if cid, ok := channelID.(string); ok {
			fields.ChannelID = cid
		}
	}
	
	if requestID := ctx.Value("request_id"); requestID != nil {
		if rid, ok := requestID.(string); ok {
			fields.RequestID = rid
		}
	}
	
	if startTime := ctx.Value("start_time"); startTime != nil {
		if st, ok := startTime.(time.Time); ok {
			fields.Duration = time.Since(st)
		}
	}
	
	// 从错误类型提取特定信息
	switch e := err.(type) {
	case *types.TaskError:
		fields.ErrorCode = string(e.Code)
		fields.Component = "task"
		fields.Metadata["task_id"] = e.TaskID
		fields.Metadata["task_type"] = e.TaskType
		
	case *types.ServiceError:
		fields.Component = "service"
		fields.Metadata["service"] = e.Service
		fields.Metadata["service_operation"] = e.Operation
		
	case *types.DiscordError:
		fields.ErrorCode = string(e.Code)
		fields.Component = "discord"
		fields.Metadata["discord_operation"] = e.Operation
		if e.GuildID != "" {
			fields.GuildID = e.GuildID
		}
		if e.ChannelID != "" {
			fields.ChannelID = e.ChannelID
		}
		if e.UserID != "" {
			fields.UserID = e.UserID
		}
		
	case *types.NetworkError:
		fields.ErrorCode = string(e.Code)
		fields.Component = "network"
		fields.Duration = e.Duration
		fields.Metadata["url"] = e.URL
		fields.Metadata["method"] = e.Method
		fields.Metadata["status"] = e.Status
		
	case *types.ConfigError:
		fields.ErrorCode = string(e.Code)
		fields.Component = "config"
		fields.Metadata["section"] = e.Section
		fields.Metadata["field"] = e.Field
		fields.Metadata["value"] = e.Value
		
	case *types.BotError:
		fields.ErrorCode = e.Code
		fields.Component = "bot"
		
	case *types.CommandError:
		fields.Component = "command"
		fields.Metadata["command"] = e.Command
		if e.User != "" {
			fields.UserID = e.User
		}
		if e.Guild != "" {
			fields.GuildID = e.Guild
		}
	}
	
	// 获取调用者信息
	if pc, file, line, ok := runtime.Caller(3); ok {
		if fn := runtime.FuncForPC(pc); fn != nil {
			fields.Function = fn.Name()
		}
		fields.Metadata["file"] = fmt.Sprintf("%s:%d", file, line)
	}
	
	return fields
}

// 全局错误日志记录器实例
var globalErrorLogger *ErrorLogger

// InitializeErrorLogger 初始化全局错误日志记录器
func InitializeErrorLogger(logger *Logger) {
	globalErrorLogger = NewErrorLogger(logger)
}

// 全局错误日志记录函数
func LogError(ctx context.Context, err error, operation string, fields ...interface{}) {
	if globalErrorLogger != nil {
		globalErrorLogger.LogError(ctx, err, operation, fields...)
	} else {
		// 降级到普通错误日志
		Error("错误发生", append([]interface{}{"error", err, "operation", operation}, fields...)...)
	}
}

func LogErrorWithSeverity(ctx context.Context, err error, operation string, severity string, fields ...interface{}) {
	if globalErrorLogger != nil {
		globalErrorLogger.LogErrorWithSeverity(ctx, err, operation, severity, fields...)
	} else {
		LogError(ctx, err, operation, append([]interface{}{"severity", severity}, fields...)...)
	}
}

func LogPanic(ctx context.Context, panicValue interface{}, operation string) {
	if globalErrorLogger != nil {
		globalErrorLogger.LogPanic(ctx, panicValue, operation)
	} else {
		Error("panic发生", "panic", panicValue, "operation", operation)
	}
}

func LogServiceError(ctx context.Context, service, operation string, err error, fields ...interface{}) {
	if globalErrorLogger != nil {
		globalErrorLogger.LogServiceError(ctx, service, operation, err, fields...)
	} else {
		LogError(ctx, err, operation, append([]interface{}{"service", service}, fields...)...)
	}
}

func LogTaskError(ctx context.Context, taskID, taskType string, err error, fields ...interface{}) {
	if globalErrorLogger != nil {
		globalErrorLogger.LogTaskError(ctx, taskID, taskType, err, fields...)
	} else {
		LogError(ctx, err, "task_processing", append([]interface{}{"task_id", taskID, "task_type", taskType}, fields...)...)
	}
}

func LogDiscordError(ctx context.Context, operation string, err error, fields ...interface{}) {
	if globalErrorLogger != nil {
		globalErrorLogger.LogDiscordError(ctx, operation, err, fields...)
	} else {
		LogError(ctx, err, operation, append([]interface{}{"component", "discord"}, fields...)...)
	}
}

func LogNetworkError(ctx context.Context, method, url string, statusCode int, err error, duration time.Duration, fields ...interface{}) {
	if globalErrorLogger != nil {
		globalErrorLogger.LogNetworkError(ctx, method, url, statusCode, err, duration, fields...)
	} else {
		LogError(ctx, err, "network_request", append([]interface{}{
			"method", method, "url", url, "status_code", statusCode, "duration", duration,
		}, fields...)...)
	}
}
