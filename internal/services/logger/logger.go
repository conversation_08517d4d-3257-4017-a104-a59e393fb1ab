package logger

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sync"

	"zeka-go/internal/types"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// Logger 日志服务实现
type Logger struct {
	zap    *zap.Logger
	sugar  *zap.SugaredLogger
	config types.LoggerConfig
}

// LoggerManager 日志管理器
type LoggerManager struct {
	logger      *Logger
	errorLogger *ErrorLogger
	mu          sync.RWMutex
}

// NewLoggerManager 创建日志管理器
func NewLoggerManager() *LoggerManager {
	return &LoggerManager{}
}

// Initialize 初始化日志管理器
func (lm *LoggerManager) Initialize(config types.LoggerConfig) error {
	lm.mu.Lock()
	defer lm.mu.Unlock()

	logger, err := NewLogger(config)
	if err != nil {
		return err
	}

	lm.logger = logger
	lm.errorLogger = NewErrorLogger(logger)

	return nil
}

// GetLogger 获取日志实例
func (lm *LoggerManager) GetLogger() *Logger {
	lm.mu.RLock()
	defer lm.mu.RUnlock()
	return lm.logger
}

// GetErrorLogger 获取错误日志实例
func (lm *LoggerManager) GetErrorLogger() *ErrorLogger {
	lm.mu.RLock()
	defer lm.mu.RUnlock()
	return lm.errorLogger
}

// Shutdown 关闭日志管理器
func (lm *LoggerManager) Shutdown() error {
	lm.mu.Lock()
	defer lm.mu.Unlock()

	if lm.logger != nil {
		if err := lm.logger.Sync(); err != nil {
			return err
		}
	}

	lm.logger = nil
	lm.errorLogger = nil

	return nil
}

// 全局日志管理器实例
var defaultLoggerManager = NewLoggerManager()

// globalLogger 全局日志实例（保持向后兼容）
var globalLogger *Logger

// Initialize 初始化日志系统
func Initialize(config types.LoggerConfig) error {
	// 使用新的日志管理器
	if err := defaultLoggerManager.Initialize(config); err != nil {
		return err
	}

	// 保持向后兼容性
	globalLogger = defaultLoggerManager.GetLogger()

	// 初始化错误日志记录器
	InitializeErrorLogger(globalLogger)

	return nil
}

// GetLoggerManager 获取默认日志管理器
func GetLoggerManager() *LoggerManager {
	return defaultLoggerManager
}

// Shutdown 关闭日志系统
func Shutdown() error {
	return defaultLoggerManager.Shutdown()
}

// NewLogger 创建新的日志实例
func NewLogger(config types.LoggerConfig) (*Logger, error) {
	// 解析日志级别
	level, err := zapcore.ParseLevel(config.Level)
	if err != nil {
		level = zapcore.InfoLevel
	}

	// 创建编码器配置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "timestamp",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "message",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 创建编码器
	var encoder zapcore.Encoder
	if config.Format == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	// 创建写入器
	var cores []zapcore.Core

	// 控制台输出
	consoleWriter := zapcore.AddSync(os.Stdout)
	cores = append(cores, zapcore.NewCore(encoder, consoleWriter, level))

	// 文件输出
	if config.File.Enabled {
		fileWriter := getFileWriter(config.File)
		cores = append(cores, zapcore.NewCore(encoder, fileWriter, level))
	}

	// 创建核心
	core := zapcore.NewTee(cores...)

	// 创建日志器
	zapLogger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	return &Logger{
		zap:    zapLogger,
		sugar:  zapLogger.Sugar(),
		config: config,
	}, nil
}

// getFileWriter 获取文件写入器
func getFileWriter(config types.FileLogConfig) zapcore.WriteSyncer {
	// 确保日志目录存在
	if err := os.MkdirAll(config.Path, 0755); err != nil {
		// 如果创建目录失败，使用当前目录
		config.Path = "."
	}

	lumberJackLogger := &lumberjack.Logger{
		Filename:   filepath.Join(config.Path, "zeka.log"),
		MaxSize:    config.MaxSize, // MB
		MaxBackups: config.MaxBackups,
		MaxAge:     config.MaxAge, // days
		Compress:   config.Compress,
	}

	return zapcore.AddSync(lumberJackLogger)
}

// Debug 记录调试信息
func (l *Logger) Debug(msg string, fields ...interface{}) {
	l.sugar.Debugw(msg, fields...)
}

// Info 记录信息
func (l *Logger) Info(msg string, fields ...interface{}) {
	l.sugar.Infow(msg, fields...)
}

// Warn 记录警告
func (l *Logger) Warn(msg string, fields ...interface{}) {
	l.sugar.Warnw(msg, fields...)
}

// Error 记录错误
func (l *Logger) Error(msg string, fields ...interface{}) {
	l.sugar.Errorw(msg, fields...)
}

// Fatal 记录致命错误并退出
func (l *Logger) Fatal(msg string, fields ...interface{}) {
	l.sugar.Fatalw(msg, fields...)
}

// Debugf 格式化调试信息
func (l *Logger) Debugf(format string, args ...interface{}) {
	l.sugar.Debugf(format, args...)
}

// Infof 格式化信息
func (l *Logger) Infof(format string, args ...interface{}) {
	l.sugar.Infof(format, args...)
}

// Warnf 格式化警告
func (l *Logger) Warnf(format string, args ...interface{}) {
	l.sugar.Warnf(format, args...)
}

// Errorf 格式化错误
func (l *Logger) Errorf(format string, args ...interface{}) {
	l.sugar.Errorf(format, args...)
}

// Fatalf 格式化致命错误并退出
func (l *Logger) Fatalf(format string, args ...interface{}) {
	l.sugar.Fatalf(format, args...)
}

// DebugContext 带上下文的调试信息
func (l *Logger) DebugContext(ctx context.Context, msg string, fields ...interface{}) {
	l.withContext(ctx).Debug(msg, fields...)
}

// InfoContext 带上下文的信息
func (l *Logger) InfoContext(ctx context.Context, msg string, fields ...interface{}) {
	l.withContext(ctx).Info(msg, fields...)
}

// WarnContext 带上下文的警告
func (l *Logger) WarnContext(ctx context.Context, msg string, fields ...interface{}) {
	l.withContext(ctx).Warn(msg, fields...)
}

// ErrorContext 带上下文的错误
func (l *Logger) ErrorContext(ctx context.Context, msg string, fields ...interface{}) {
	l.withContext(ctx).Error(msg, fields...)
}

// With 添加结构化字段
func (l *Logger) With(fields ...interface{}) types.LoggerService {
	return &Logger{
		zap:    l.zap,
		sugar:  l.sugar.With(fields...),
		config: l.config,
	}
}

// WithContext 添加上下文
func (l *Logger) WithContext(ctx context.Context) types.LoggerService {
	return l.withContext(ctx)
}

// WithError 添加错误字段
func (l *Logger) WithError(err error) types.LoggerService {
	return l.With("error", err)
}

// withContext 内部方法：添加上下文信息
func (l *Logger) withContext(ctx context.Context) *Logger {
	fields := extractContextFields(ctx)
	return &Logger{
		zap:    l.zap,
		sugar:  l.sugar.With(fields...),
		config: l.config,
	}
}

// extractContextFields 从上下文中提取字段
func extractContextFields(ctx context.Context) []interface{} {
	var fields []interface{}

	// 提取请求ID
	if requestID := ctx.Value("request_id"); requestID != nil {
		fields = append(fields, "request_id", requestID)
	}

	// 提取用户ID
	if userID := ctx.Value("user_id"); userID != nil {
		fields = append(fields, "user_id", userID)
	}

	// 提取服务器ID
	if guildID := ctx.Value("guild_id"); guildID != nil {
		fields = append(fields, "guild_id", guildID)
	}

	// 提取频道ID
	if channelID := ctx.Value("channel_id"); channelID != nil {
		fields = append(fields, "channel_id", channelID)
	}

	return fields
}

// SetLevel 设置日志级别
func (l *Logger) SetLevel(level string) error {
	// 注意：zap 不支持动态设置级别，这里只是示例
	// 实际实现可能需要重新创建 logger
	return fmt.Errorf("动态设置日志级别暂不支持")
}

// GetLevel 获取当前日志级别
func (l *Logger) GetLevel() string {
	return l.config.Level
}

// Sync 同步日志缓冲区
func (l *Logger) Sync() error {
	return l.zap.Sync()
}

// 全局日志方法
func Debug(msg string, fields ...interface{}) {
	if globalLogger != nil {
		globalLogger.Debug(msg, fields...)
	}
}

func Info(msg string, fields ...interface{}) {
	if globalLogger != nil {
		globalLogger.Info(msg, fields...)
	}
}

func Warn(msg string, fields ...interface{}) {
	if globalLogger != nil {
		globalLogger.Warn(msg, fields...)
	}
}

func Error(msg string, fields ...interface{}) {
	if globalLogger != nil {
		globalLogger.Error(msg, fields...)
	}
}

func Fatal(msg string, fields ...interface{}) {
	if globalLogger != nil {
		globalLogger.Fatal(msg, fields...)
	}
}

func Debugf(format string, args ...interface{}) {
	if globalLogger != nil {
		globalLogger.Debugf(format, args...)
	}
}

func Infof(format string, args ...interface{}) {
	if globalLogger != nil {
		globalLogger.Infof(format, args...)
	}
}

func Warnf(format string, args ...interface{}) {
	if globalLogger != nil {
		globalLogger.Warnf(format, args...)
	}
}

func Errorf(format string, args ...interface{}) {
	if globalLogger != nil {
		globalLogger.Errorf(format, args...)
	}
}

func Fatalf(format string, args ...interface{}) {
	if globalLogger != nil {
		globalLogger.Fatalf(format, args...)
	}
}

func With(fields ...interface{}) types.LoggerService {
	if globalLogger != nil {
		return globalLogger.With(fields...)
	}
	return nil
}

func WithError(err error) types.LoggerService {
	if globalLogger != nil {
		return globalLogger.WithError(err)
	}
	return nil
}

func Sync() error {
	if globalLogger != nil {
		return globalLogger.Sync()
	}
	return nil
}
