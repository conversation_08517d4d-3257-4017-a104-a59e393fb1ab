package logger

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/bwmarrin/discordgo"
	"go.uber.org/zap/zapcore"
)

// DiscordCore Discord 日志核心
type DiscordCore struct {
	zapcore.LevelEnabler
	encoder zapcore.Encoder
	session *discordgo.Session
	channelID string
	buffer  []zapcore.Entry
	mu      sync.Mutex
	ticker  *time.Ticker
	done    chan struct{}
}

// NewDiscordCore 创建新的 Discord 日志核心
func NewDiscordCore(session *discordgo.Session, channelID string, level zapcore.Level, encoder zapcore.Encoder) *DiscordCore {
	core := &DiscordCore{
		LevelEnabler: level,
		encoder:      encoder,
		session:      session,
		channelID:    channelID,
		buffer:       make([]zapcore.Entry, 0),
		done:         make(chan struct{}),
	}
	
	// 启动批量发送协程
	core.startBatchSender()
	
	return core
}

// With 添加字段
func (c *DiscordCore) With(fields []zapcore.Field) zapcore.Core {
	clone := c.clone()
	clone.encoder = c.encoder.Clone()
	for _, field := range fields {
		field.AddTo(clone.encoder)
	}
	return clone
}

// Check 检查日志级别
func (c *DiscordCore) Check(entry zapcore.Entry, checked *zapcore.CheckedEntry) *zapcore.CheckedEntry {
	if c.Enabled(entry.Level) {
		return checked.AddCore(entry, c)
	}
	return checked
}

// Write 写入日志
func (c *DiscordCore) Write(entry zapcore.Entry, fields []zapcore.Field) error {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	// 添加到缓冲区
	c.buffer = append(c.buffer, entry)
	
	// 如果是错误级别或更高，立即发送
	if entry.Level >= zapcore.ErrorLevel {
		go c.sendImmediately(entry, fields)
	}
	
	return nil
}

// Sync 同步日志
func (c *DiscordCore) Sync() error {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	if len(c.buffer) > 0 {
		c.flushBuffer()
	}
	
	return nil
}

// clone 克隆核心
func (c *DiscordCore) clone() *DiscordCore {
	return &DiscordCore{
		LevelEnabler: c.LevelEnabler,
		encoder:      c.encoder.Clone(),
		session:      c.session,
		channelID:    c.channelID,
		buffer:       make([]zapcore.Entry, 0),
		done:         make(chan struct{}),
	}
}

// startBatchSender 启动批量发送器
func (c *DiscordCore) startBatchSender() {
	c.ticker = time.NewTicker(30 * time.Second) // 每30秒发送一次
	
	go func() {
		for {
			select {
			case <-c.ticker.C:
				c.mu.Lock()
				if len(c.buffer) > 0 {
					c.flushBuffer()
				}
				c.mu.Unlock()
			case <-c.done:
				c.ticker.Stop()
				return
			}
		}
	}()
}

// flushBuffer 刷新缓冲区
func (c *DiscordCore) flushBuffer() {
	if len(c.buffer) == 0 {
		return
	}
	
	// 构建消息内容
	var content string
	for _, entry := range c.buffer {
		level := c.getLevelEmoji(entry.Level)
		timestamp := entry.Time.Format("15:04:05")
		content += fmt.Sprintf("%s `%s` **%s** %s\n", 
			level, timestamp, entry.LoggerName, entry.Message)
	}
	
	// 限制消息长度
	if len(content) > 1900 {
		content = content[:1900] + "...\n*消息过长，已截断*"
	}
	
	// 发送到 Discord
	c.sendToDiscord(content)
	
	// 清空缓冲区
	c.buffer = c.buffer[:0]
}

// sendImmediately 立即发送重要日志
func (c *DiscordCore) sendImmediately(entry zapcore.Entry, fields []zapcore.Field) {
	level := c.getLevelEmoji(entry.Level)
	timestamp := entry.Time.Format("15:04:05")
	
	content := fmt.Sprintf("%s `%s` **%s** %s", 
		level, timestamp, entry.LoggerName, entry.Message)
	
	// 添加字段信息
	if len(fields) > 0 {
		content += "\n**详细信息:**\n"
		for _, field := range fields {
			content += fmt.Sprintf("• %s: %v\n", field.Key, field.Interface)
		}
	}
	
	c.sendToDiscord(content)
}

// sendToDiscord 发送消息到 Discord
func (c *DiscordCore) sendToDiscord(content string) {
	if c.session == nil || c.channelID == "" {
		return
	}
	
	// 创建嵌入消息
	embed := &discordgo.MessageEmbed{
		Description: content,
		Color:       c.getColorForLevel(zapcore.InfoLevel),
		Timestamp:   time.Now().Format(time.RFC3339),
		Footer: &discordgo.MessageEmbedFooter{
			Text: "Zeka Bot 日志系统",
		},
	}
	
	// 发送消息
	_, err := c.session.ChannelMessageSendEmbed(c.channelID, embed)
	if err != nil {
		// 如果发送失败，尝试发送纯文本
		_, err = c.session.ChannelMessageSend(c.channelID, fmt.Sprintf("```\n%s\n```", content))
		if err != nil {
			// 记录到标准错误输出，避免无限循环
			fmt.Printf("Discord 日志发送失败: %v\n", err)
		}
	}
}

// getLevelEmoji 获取日志级别对应的表情符号
func (c *DiscordCore) getLevelEmoji(level zapcore.Level) string {
	switch level {
	case zapcore.DebugLevel:
		return "🔍"
	case zapcore.InfoLevel:
		return "ℹ️"
	case zapcore.WarnLevel:
		return "⚠️"
	case zapcore.ErrorLevel:
		return "❌"
	case zapcore.FatalLevel:
		return "💀"
	default:
		return "📝"
	}
}

// getColorForLevel 获取日志级别对应的颜色
func (c *DiscordCore) getColorForLevel(level zapcore.Level) int {
	switch level {
	case zapcore.DebugLevel:
		return 0x808080 // 灰色
	case zapcore.InfoLevel:
		return 0x3498db // 蓝色
	case zapcore.WarnLevel:
		return 0xf39c12 // 橙色
	case zapcore.ErrorLevel:
		return 0xe74c3c // 红色
	case zapcore.FatalLevel:
		return 0x8b0000 // 深红色
	default:
		return 0x95a5a6 // 默认灰色
	}
}

// Close 关闭 Discord 核心
func (c *DiscordCore) Close() error {
	close(c.done)
	
	// 最后一次刷新缓冲区
	c.mu.Lock()
	defer c.mu.Unlock()
	
	if len(c.buffer) > 0 {
		c.flushBuffer()
	}
	
	return nil
}

// DiscordHook Discord 日志钩子（用于其他日志库）
type DiscordHook struct {
	session   *discordgo.Session
	channelID string
	levels    []zapcore.Level
}

// NewDiscordHook 创建新的 Discord 钩子
func NewDiscordHook(session *discordgo.Session, channelID string, levels []zapcore.Level) *DiscordHook {
	return &DiscordHook{
		session:   session,
		channelID: channelID,
		levels:    levels,
	}
}

// Levels 返回支持的日志级别
func (h *DiscordHook) Levels() []zapcore.Level {
	return h.levels
}

// Fire 触发钩子
func (h *DiscordHook) Fire(ctx context.Context, level zapcore.Level, message string, fields map[string]interface{}) error {
	if !h.shouldLog(level) {
		return nil
	}
	
	// 构建消息
	emoji := h.getLevelEmoji(level)
	timestamp := time.Now().Format("15:04:05")
	content := fmt.Sprintf("%s `%s` %s", emoji, timestamp, message)
	
	// 添加字段
	if len(fields) > 0 {
		content += "\n**详细信息:**\n"
		for key, value := range fields {
			content += fmt.Sprintf("• %s: %v\n", key, value)
		}
	}
	
	// 发送到 Discord
	return h.sendToDiscord(content, level)
}

// shouldLog 检查是否应该记录此级别的日志
func (h *DiscordHook) shouldLog(level zapcore.Level) bool {
	for _, l := range h.levels {
		if l == level {
			return true
		}
	}
	return false
}

// getLevelEmoji 获取级别表情符号
func (h *DiscordHook) getLevelEmoji(level zapcore.Level) string {
	switch level {
	case zapcore.DebugLevel:
		return "🔍"
	case zapcore.InfoLevel:
		return "ℹ️"
	case zapcore.WarnLevel:
		return "⚠️"
	case zapcore.ErrorLevel:
		return "❌"
	case zapcore.FatalLevel:
		return "💀"
	default:
		return "📝"
	}
}

// sendToDiscord 发送消息到 Discord
func (h *DiscordHook) sendToDiscord(content string, level zapcore.Level) error {
	if h.session == nil || h.channelID == "" {
		return fmt.Errorf("Discord 会话或频道 ID 未设置")
	}
	
	embed := &discordgo.MessageEmbed{
		Description: content,
		Color:       h.getColorForLevel(level),
		Timestamp:   time.Now().Format(time.RFC3339),
		Footer: &discordgo.MessageEmbedFooter{
			Text: "Zeka Bot 日志系统",
		},
	}
	
	_, err := h.session.ChannelMessageSendEmbed(h.channelID, embed)
	return err
}

// getColorForLevel 获取级别颜色
func (h *DiscordHook) getColorForLevel(level zapcore.Level) int {
	switch level {
	case zapcore.DebugLevel:
		return 0x808080
	case zapcore.InfoLevel:
		return 0x3498db
	case zapcore.WarnLevel:
		return 0xf39c12
	case zapcore.ErrorLevel:
		return 0xe74c3c
	case zapcore.FatalLevel:
		return 0x8b0000
	default:
		return 0x95a5a6
	}
}
