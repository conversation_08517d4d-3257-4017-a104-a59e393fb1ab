package notification

import (
	"context"
	"fmt"
	"sync"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/services/template"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// EnhancedNotificationService 增强的通知服务实现
type EnhancedNotificationService struct {
	// 依赖服务
	discordClient       *types.Client
	templateService     template.TemplateService
	adapterRegistry     template.AdapterRegistry
	notificationBuilder template.NotificationBuilder

	// 异步通知管理
	asyncResults map[string]*types.ProductNotificationResult
	asyncMu      sync.RWMutex

	// 配置
	defaultTimeout time.Duration
	maxRetries     int

	// 状态
	initialized bool
	mu          sync.RWMutex
}

// NewEnhancedNotificationService 创建新的增强通知服务
func NewEnhancedNotificationService() *EnhancedNotificationService {
	service := &EnhancedNotificationService{
		asyncResults:   make(map[string]*types.ProductNotificationResult),
		defaultTimeout: 30 * time.Second,
		maxRetries:     3,
		initialized:    false,
	}

	// 初始化默认适配器注册表
	service.adapterRegistry = template.NewDefaultAdapterRegistry()

	return service
}

// Initialize 初始化服务
func (s *EnhancedNotificationService) Initialize(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.initialized = true
	logger.Info("增强通知服务已初始化")
	return nil
}

// Shutdown 关闭服务
func (s *EnhancedNotificationService) Shutdown(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.initialized = false
	logger.Info("增强通知服务已关闭")
	return nil
}

// SetTemplateManager 设置模板管理器（向后兼容）
func (s *EnhancedNotificationService) SetTemplateManager(manager interface{}) error {
	if templateService, ok := manager.(template.TemplateService); ok {
		return s.SetTemplateService(templateService)
	}
	return fmt.Errorf("无效的模板管理器类型")
}

// SetTemplateRenderer 设置模板渲染器（向后兼容）
func (s *EnhancedNotificationService) SetTemplateRenderer(renderer interface{}) error {
	// 这个方法在新架构中不需要，因为渲染器是模板服务的一部分
	logger.Warn("SetTemplateRenderer已弃用，请使用SetTemplateService")
	return nil
}

// SetDiscordClient 设置Discord客户端
func (s *EnhancedNotificationService) SetDiscordClient(client *types.Client) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.discordClient = client
	return nil
}

// SetTemplateService 设置模板服务
func (s *EnhancedNotificationService) SetTemplateService(service template.TemplateService) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.templateService = service
	return nil
}

// SetAdapterRegistry 设置适配器注册表
func (s *EnhancedNotificationService) SetAdapterRegistry(registry template.AdapterRegistry) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.adapterRegistry = registry
	return nil
}

// SendNotification 发送通知
func (s *EnhancedNotificationService) SendNotification(ctx context.Context, options *types.ProductNotificationOptions) (*types.ProductNotificationResult, error) {
	startTime := time.Now()

	// 验证输入
	if err := s.validateInput(options); err != nil {
		return nil, fmt.Errorf("输入验证失败: %w", err)
	}

	// 创建结果对象
	result := &types.ProductNotificationResult{
		ID:        generateNotificationID(),
		Status:    "processing",
		Success:   false,
		Platform:  options.Product.Platform,
		Timestamp: startTime,
	}

	// 选择适配器
	adapter, err := s.selectAdapter(options.Product)
	if err != nil {
		result.Status = "failed"
		result.Error = fmt.Errorf("适配器选择失败: %w", err)
		return result, result.Error
	}

	// 验证产品数据
	if err := adapter.ValidateProduct(options.Product); err != nil {
		result.Status = "failed"
		result.Error = fmt.Errorf("产品数据验证失败: %w", err)
		return result, result.Error
	}

	// 获取模板
	templateID := adapter.GetTemplateID()
	if options.TemplateID != "" && options.TemplateOverride {
		templateID = options.TemplateID
	}

	tmpl, err := s.templateService.GetTemplate(templateID, options.GuildID)
	if err != nil {
		result.Status = "failed"
		result.Error = fmt.Errorf("获取模板失败: %w", err)
		return result, result.Error
	}

	result.TemplateID = templateID
	result.TemplateUsed = tmpl.Name

	// 转换变量
	variables := adapter.ToTemplateVariables(options.Product)

	// 渲染模板
	renderStart := time.Now()
	renderCtx := template.RenderContext{
		Template:  tmpl,
		Variables: variables,
		GuildID:   options.GuildID,
	}

	renderedContent, err := s.templateService.Render(renderCtx)
	if err != nil {
		result.Status = "failed"
		result.Error = fmt.Errorf("模板渲染失败: %w", err)
		return result, result.Error
	}
	result.TemplateRenderTime = time.Since(renderStart)

	// 发送消息
	sendStart := time.Now()
	messageID, err := s.sendMessage(ctx, options.ChannelID, renderedContent, options.Timeout)
	if err != nil {
		result.Status = "failed"
		result.Error = fmt.Errorf("消息发送失败: %w", err)
		return result, result.Error
	}
	result.MessageSendTime = time.Since(sendStart)

	// 设置成功结果
	result.MessageID = messageID
	result.Status = "sent"
	result.Success = true
	result.TotalTime = time.Since(startTime)

	logger.Info("通知发送成功",
		"notification_id", result.ID,
		"channel_id", options.ChannelID,
		"platform", options.Product.Platform,
		"template_id", templateID,
		"message_id", messageID,
		"total_time", result.TotalTime)

	return result, nil
}

// SendBulkNotifications 批量发送通知
func (s *EnhancedNotificationService) SendBulkNotifications(ctx context.Context, notifications []*types.ProductNotificationOptions) ([]*types.ProductNotificationResult, error) {
	var results []*types.ProductNotificationResult

	for _, notification := range notifications {
		result, err := s.SendNotification(ctx, notification)
		if err != nil {
			// 记录错误但继续处理其他通知
			logger.Error("批量通知中的单个通知发送失败", "error", err)
		}
		results = append(results, result)
	}

	return results, nil
}

// SendAsyncNotification 异步发送通知
func (s *EnhancedNotificationService) SendAsyncNotification(ctx context.Context, options *types.ProductNotificationOptions) (string, error) {
	notificationID := generateNotificationID()

	// 创建初始结果
	result := &types.ProductNotificationResult{
		ID:        notificationID,
		Status:    "queued",
		Success:   false,
		Platform:  options.Product.Platform,
		Timestamp: time.Now(),
	}

	// 存储到异步结果映射
	s.asyncMu.Lock()
	s.asyncResults[notificationID] = result
	s.asyncMu.Unlock()

	// 启动异步处理
	go func() {
		finalResult, err := s.SendNotification(ctx, options)
		if err != nil {
			result.Status = "failed"
			result.Error = err
		} else {
			*result = *finalResult
		}

		// 更新结果
		s.asyncMu.Lock()
		s.asyncResults[notificationID] = result
		s.asyncMu.Unlock()
	}()

	return notificationID, nil
}

// GetNotificationStatus 获取通知状态
func (s *EnhancedNotificationService) GetNotificationStatus(notificationID string) (*types.ProductNotificationResult, error) {
	s.asyncMu.RLock()
	defer s.asyncMu.RUnlock()

	result, exists := s.asyncResults[notificationID]
	if !exists {
		return nil, fmt.Errorf("未找到通知: %s", notificationID)
	}

	return result, nil
}

// HealthCheck 健康检查
func (s *EnhancedNotificationService) HealthCheck(ctx context.Context) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if !s.initialized {
		return fmt.Errorf("服务未初始化")
	}

	if s.discordClient == nil {
		return fmt.Errorf("Discord客户端未设置")
	}

	if s.templateService == nil {
		return fmt.Errorf("模板服务未设置")
	}

	if s.adapterRegistry == nil {
		return fmt.Errorf("适配器注册表未设置")
	}

	return nil
}

// validateInput 验证输入
func (s *EnhancedNotificationService) validateInput(options *types.ProductNotificationOptions) error {
	if options == nil {
		return fmt.Errorf("通知选项不能为空")
	}

	if options.Product == nil {
		return fmt.Errorf("产品信息不能为空")
	}

	if options.ChannelID == "" {
		return fmt.Errorf("频道ID不能为空")
	}

	return nil
}

// selectAdapter 选择适配器
func (s *EnhancedNotificationService) selectAdapter(product *types.ProductItem) (template.PlatformAdapter, error) {
	if s.adapterRegistry == nil {
		return nil, fmt.Errorf("适配器注册表未设置")
	}

	return s.adapterRegistry.GetAdapterForProduct(product)
}

// sendMessage 发送消息
func (s *EnhancedNotificationService) sendMessage(ctx context.Context, channelID string, content *template.RenderedContent, timeout time.Duration) (string, error) {
	if s.discordClient == nil {
		return "", fmt.Errorf("Discord客户端未设置")
	}

	session := s.discordClient.Session
	if session == nil {
		return "", fmt.Errorf("Discord会话未建立")
	}

	// 设置超时
	if timeout <= 0 {
		timeout = s.defaultTimeout
	}

	// 发送消息
	var message *discordgo.Message
	var err error

	if len(content.Embeds) > 0 {
		// 发送embed消息
		message, err = session.ChannelMessageSendEmbed(channelID, content.Embeds[0])
	} else if content.Content != "" {
		// 发送文本消息
		message, err = session.ChannelMessageSend(channelID, content.Content)
	} else {
		return "", fmt.Errorf("没有可发送的内容")
	}

	if err != nil {
		return "", fmt.Errorf("发送Discord消息失败: %w", err)
	}

	return message.ID, nil
}

// SendProductNotification 发送产品通知（向后兼容）
func (s *EnhancedNotificationService) SendProductNotification(ctx context.Context, product *types.ProductItem, channelID string) (*types.ProductNotificationResult, error) {
	options := &types.ProductNotificationOptions{
		Product:   product,
		ChannelID: channelID,
		Priority:  types.PriorityNormal,
		Timeout:   s.defaultTimeout,
		RetryMax:  s.maxRetries,
	}
	return s.SendNotification(ctx, options)
}

// SendProductNotificationWithOptions 发送带选项的产品通知（向后兼容）
func (s *EnhancedNotificationService) SendProductNotificationWithOptions(ctx context.Context, options types.ProductNotificationOptions) (*types.ProductNotificationResult, error) {
	return s.SendNotification(ctx, &options)
}

// SendBulkProductNotifications 批量发送产品通知（向后兼容）
func (s *EnhancedNotificationService) SendBulkProductNotifications(ctx context.Context, notifications []types.ProductNotificationOptions) ([]*types.ProductNotificationResult, error) {
	// 转换为指针数组
	notificationPtrs := make([]*types.ProductNotificationOptions, len(notifications))
	for i := range notifications {
		notificationPtrs[i] = &notifications[i]
	}
	return s.SendBulkNotifications(ctx, notificationPtrs)
}

// SendProductNotificationAsync 异步发送产品通知（向后兼容）
func (s *EnhancedNotificationService) SendProductNotificationAsync(ctx context.Context, product *types.ProductItem, channelID string) (string, error) {
	options := &types.ProductNotificationOptions{
		Product:   product,
		ChannelID: channelID,
		Priority:  types.PriorityNormal,
		Timeout:   s.defaultTimeout,
		RetryMax:  s.maxRetries,
		Async:     true,
	}
	return s.SendAsyncNotification(ctx, options)
}

// generateNotificationID 生成通知ID
func generateNotificationID() string {
	return fmt.Sprintf("pn_%d", time.Now().UnixNano())
}
