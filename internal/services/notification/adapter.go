package notification

import (
	"context"
	"fmt"
	"time"

	"zeka-go/internal/services"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/services/template"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// CombinedTemplateService 组合模板服务，实现TemplateService接口
type CombinedTemplateService struct {
	manager  *template.Manager
	renderer *template.StandardRenderer
}

// GetTemplate 获取模板
func (c *CombinedTemplateService) GetTemplate(templateID string, guildID ...string) (*types.Template, error) {
	return c.manager.GetTemplate(templateID, guildID...)
}

// AddTemplate 添加模板
func (c *CombinedTemplateService) AddTemplate(template *types.Template) error {
	return c.manager.AddTemplate(template)
}

// RemoveTemplate 移除模板（暂不支持）
func (c *CombinedTemplateService) RemoveTemplate(templateID string, guildID ...string) error {
	return fmt.Errorf("RemoveTemplate方法暂不支持")
}

// ListTemplates 列出模板（暂不支持）
func (c *CombinedTemplateService) ListTemplates(guildID ...string) ([]*types.Template, error) {
	return nil, fmt.Errorf("ListTemplates方法暂不支持")
}

// LoadTemplate 加载模板
func (c *CombinedTemplateService) LoadTemplate(templateID string, guildID ...string) (*types.Template, error) {
	return c.manager.LoadTemplate(templateID, guildID...)
}

// ReloadTemplates 重新加载模板
func (c *CombinedTemplateService) ReloadTemplates() error {
	return c.manager.ReloadTemplates()
}

// GetTemplateForProduct 根据产品获取模板
func (c *CombinedTemplateService) GetTemplateForProduct(product *types.ProductItem, guildID ...string) (*types.Template, error) {
	return c.manager.GetTemplateForProduct(product, guildID...)
}

// GetTemplateByPlatform 根据平台获取模板
func (c *CombinedTemplateService) GetTemplateByPlatform(platform string, guildID ...string) (*types.Template, error) {
	return c.manager.GetTemplateByPlatform(platform, guildID...)
}

// ValidateTemplate 验证模板
func (c *CombinedTemplateService) ValidateTemplate(template *types.Template) error {
	return c.manager.ValidateTemplate(template)
}

// Initialize 初始化
func (c *CombinedTemplateService) Initialize(ctx context.Context) error {
	return c.manager.Initialize(ctx)
}

// Shutdown 关闭（暂不支持）
func (c *CombinedTemplateService) Shutdown(ctx context.Context) error {
	return nil // 模板管理器没有Shutdown方法
}

// Render 渲染模板
func (c *CombinedTemplateService) Render(ctx template.RenderContext) (*template.RenderedContent, error) {
	return c.renderer.Render(ctx)
}

// RenderEmbed 渲染嵌入消息
func (c *CombinedTemplateService) RenderEmbed(embedData interface{}, variables map[string]interface{}) (*discordgo.MessageEmbed, error) {
	return c.renderer.RenderEmbed(embedData, variables)
}

// ReplaceVariables 替换变量
func (c *CombinedTemplateService) ReplaceVariables(text string, variables map[string]interface{}) string {
	return c.renderer.ReplaceVariables(text, variables)
}

// ProductNotificationServiceAdapter 产品通知服务适配器
type ProductNotificationServiceAdapter struct {
	service *EnhancedNotificationService
	name    string
	stype   string
	deps    []string
}

// NewProductNotificationServiceAdapter 创建新的产品通知服务适配器
func NewProductNotificationServiceAdapter() *ProductNotificationServiceAdapter {
	return &ProductNotificationServiceAdapter{
		service: NewEnhancedNotificationService(),
		name:    "product_notification",
		stype:   "notification",
		deps:    []string{"template", "discord"},
	}
}

// GetName 获取服务名称
func (a *ProductNotificationServiceAdapter) GetName() string {
	return a.name
}

// GetType 获取服务类型
func (a *ProductNotificationServiceAdapter) GetType() string {
	return a.stype
}

// GetDependencies 获取依赖列表
func (a *ProductNotificationServiceAdapter) GetDependencies() []string {
	return a.deps
}

// Initialize 初始化服务
func (a *ProductNotificationServiceAdapter) Initialize(ctx context.Context) error {
	logger.Info("初始化产品通知服务适配器")
	return a.service.Initialize(ctx)
}

// Start 启动服务
func (a *ProductNotificationServiceAdapter) Start(ctx context.Context) error {
	logger.Info("启动产品通知服务")
	// 产品通知服务不需要特殊的启动逻辑
	return nil
}

// Stop 停止服务
func (a *ProductNotificationServiceAdapter) Stop(ctx context.Context) error {
	logger.Info("停止产品通知服务")
	return a.service.Shutdown(ctx)
}

// GetStatus 获取服务状态
func (a *ProductNotificationServiceAdapter) GetStatus() string {
	// 执行健康检查
	if err := a.service.HealthCheck(context.Background()); err != nil {
		return "unhealthy"
	}
	return "healthy"
}

// GetService 获取底层服务实例
func (a *ProductNotificationServiceAdapter) GetService() types.ProductNotificationService {
	return a.service
}

// SetDiscordClient 设置 Discord 客户端
func (a *ProductNotificationServiceAdapter) SetDiscordClient(client *types.Client) error {
	return a.service.SetDiscordClient(client)
}

// SetTemplateManager 设置模板管理器
func (a *ProductNotificationServiceAdapter) SetTemplateManager(manager interface{}) error {
	// 如果是模板管理器，创建组合的模板服务
	if templateManager, ok := manager.(*template.Manager); ok {
		// 创建渲染器
		renderer := template.NewRenderer()

		// 创建组合服务
		combinedService := &CombinedTemplateService{
			manager:  templateManager,
			renderer: renderer,
		}

		return a.service.SetTemplateService(combinedService)
	}

	return a.service.SetTemplateManager(manager)
}

// SetTemplateRenderer 设置模板渲染器
func (a *ProductNotificationServiceAdapter) SetTemplateRenderer(renderer interface{}) error {
	return a.service.SetTemplateRenderer(renderer)
}

// InjectDependencies 注入依赖服务
func (a *ProductNotificationServiceAdapter) InjectDependencies(serviceManager interface{}) error {
	// 这里可以从服务管理器获取依赖服务
	// 暂时留空，依赖注入将在 bot.go 中手动处理
	logger.Debug("产品通知服务依赖注入完成")
	return nil
}

// GetMetrics 获取服务指标
func (a *ProductNotificationServiceAdapter) GetMetrics() map[string]interface{} {
	metrics := make(map[string]interface{})

	// 基础指标
	metrics["service_name"] = a.name
	metrics["service_type"] = a.stype
	metrics["dependencies"] = a.deps

	// 健康状态
	status := a.GetStatus()
	metrics["health_status"] = status

	// 健康检查详情
	healthResult := a.HealthCheck(context.Background())
	metrics["health_message"] = healthResult.Message

	// 异步通知统计
	if a.service != nil {
		a.service.asyncMu.RLock()
		asyncCount := len(a.service.asyncResults)
		a.service.asyncMu.RUnlock()

		metrics["async_notifications_count"] = asyncCount
	}

	return metrics
}

// ValidateConfiguration 验证配置
func (a *ProductNotificationServiceAdapter) ValidateConfiguration() error {
	if a.service == nil {
		return fmt.Errorf("产品通知服务实例未创建")
	}

	return nil
}

// Reload 重新加载配置
func (a *ProductNotificationServiceAdapter) Reload(ctx context.Context) error {
	logger.Info("重新加载产品通知服务配置")
	// 产品通知服务暂时不需要重新加载逻辑
	return nil
}

// GetConfigSchema 获取配置模式
func (a *ProductNotificationServiceAdapter) GetConfigSchema() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"default_timeout": map[string]interface{}{
				"type":        "string",
				"description": "默认超时时间",
				"default":     "30s",
			},
			"max_retries": map[string]interface{}{
				"type":        "integer",
				"description": "最大重试次数",
				"default":     3,
			},
			"async_cleanup_interval": map[string]interface{}{
				"type":        "string",
				"description": "异步结果清理间隔",
				"default":     "1h",
			},
		},
	}
}

// ApplyConfiguration 应用配置
func (a *ProductNotificationServiceAdapter) ApplyConfiguration(config map[string]interface{}) error {
	logger.Debug("应用产品通知服务配置", "config", config)

	// 这里可以根据配置更新服务参数
	// 暂时使用默认配置

	return nil
}

// GetVersion 获取服务版本
func (a *ProductNotificationServiceAdapter) GetVersion() string {
	return "1.0.0"
}

// GetDescription 获取服务描述
func (a *ProductNotificationServiceAdapter) GetDescription() string {
	return "产品通知服务 - 支持基于模板的产品信息 Discord 通知发送"
}

// SupportsFeature 检查是否支持特定功能
func (a *ProductNotificationServiceAdapter) SupportsFeature(feature string) bool {
	supportedFeatures := map[string]bool{
		"async_notifications":  true,
		"bulk_notifications":   true,
		"template_integration": true,
		"platform_detection":   true,
		"timeout_control":      true,
		"retry_mechanism":      true,
		"health_check":         true,
		"metrics":              true,
	}

	return supportedFeatures[feature]
}

// GetSupportedFeatures 获取支持的功能列表
func (a *ProductNotificationServiceAdapter) GetSupportedFeatures() []string {
	return []string{
		"async_notifications",
		"bulk_notifications",
		"template_integration",
		"platform_detection",
		"timeout_control",
		"retry_mechanism",
		"health_check",
		"metrics",
	}
}

// HealthCheck 健康检查（实现 services.Service 接口）
func (a *ProductNotificationServiceAdapter) HealthCheck(ctx context.Context) *services.HealthCheckResult {
	start := time.Now()

	result := &services.HealthCheckResult{
		Healthy:   true,
		Message:   "产品通知服务运行正常",
		CheckTime: time.Now(),
	}

	if a.service == nil {
		result.Healthy = false
		result.Message = "产品通知服务实例未创建"
		result.ResponseTime = time.Since(start)
		return result
	}

	if err := a.service.HealthCheck(ctx); err != nil {
		result.Healthy = false
		result.Message = err.Error()
	}

	result.ResponseTime = time.Since(start)
	return result
}
