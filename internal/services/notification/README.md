# 产品通知服务 (Product Notification Service)

产品通知服务是一个专门用于发送基于模板的产品信息 Discord 通知的服务模块。

## 功能特性

- ✅ **智能模板选择**: 根据产品平台自动选择对应的模板
- ✅ **模板服务集成**: 深度集成现有的模板管理和渲染系统
- ✅ **异步发送支持**: 支持异步通知发送和状态跟踪
- ✅ **批量通知**: 支持一次发送多个产品通知
- ✅ **错误处理**: 完整的错误处理和重试机制
- ✅ **性能监控**: 提供详细的性能指标和日志记录
- ✅ **健康检查**: 内置健康检查和服务状态监控

## 快速开始

### 1. 基础使用

```go
// 获取产品通知服务
service := notification.NewProductNotificationService()

// 创建产品信息
product := &types.ProductItem{
    Title:       "MOLLY 盲盒系列",
    URL:         "https://www.popmart.com/products/molly-blind-box",
    ProductID:   "MOLLY-001",
    Price:       "¥59",
    Platform:    "popmart",
    Stock:       10,
    Availability: "有库存",
}

// 发送通知
ctx := context.Background()
channelID := "1234567890123456789"

result, err := service.SendProductNotification(ctx, product, channelID)
if err != nil {
    log.Printf("通知发送失败: %v", err)
    return
}

log.Printf("通知发送成功: %s", result.MessageID)
```

### 2. 高级选项

```go
// 创建高级通知选项
options := types.ProductNotificationOptions{
    Product:   product,
    ChannelID: channelID,
    GuildID:   "9876543210987654321",
    Priority:  types.PriorityHigh,
    Timeout:   15 * time.Second,
    RetryMax:  2,
    
    // 强制使用指定模板
    TemplateID:       "popmart",
    TemplateOverride: true,
    
    Metadata: map[string]interface{}{
        "source": "manual_trigger",
        "user_id": "user123",
    },
}

result, err := service.SendProductNotificationWithOptions(ctx, options)
```

### 3. 异步发送

```go
// 启动异步通知
notificationID, err := service.SendProductNotificationAsync(ctx, product, channelID)
if err != nil {
    log.Printf("异步通知启动失败: %v", err)
    return
}

// 检查状态
status, err := service.GetNotificationStatus(notificationID)
if err != nil {
    log.Printf("获取状态失败: %v", err)
    return
}

log.Printf("通知状态: %s", status.Status)
```

## API 参考

### 核心接口

#### `SendProductNotification(ctx, product, channelID) -> (result, error)`
发送基础产品通知

**参数:**
- `ctx`: 上下文
- `product`: 产品信息 (`*types.ProductItem`)
- `channelID`: Discord 频道 ID

**返回:**
- `result`: 通知结果 (`*types.ProductNotificationResult`)
- `error`: 错误信息

#### `SendProductNotificationWithOptions(ctx, options) -> (result, error)`
发送带高级选项的产品通知

**参数:**
- `ctx`: 上下文
- `options`: 通知选项 (`types.ProductNotificationOptions`)

#### `SendBulkProductNotifications(ctx, notifications) -> (results, error)`
批量发送产品通知

**参数:**
- `ctx`: 上下文
- `notifications`: 通知选项数组 (`[]types.ProductNotificationOptions`)

#### `SendProductNotificationAsync(ctx, product, channelID) -> (notificationID, error)`
异步发送产品通知

**返回:**
- `notificationID`: 通知 ID，用于状态跟踪

#### `GetNotificationStatus(notificationID) -> (result, error)`
获取异步通知状态

## 配置和集成

### 服务依赖

产品通知服务需要以下依赖：

1. **Discord 客户端**: 用于发送消息
2. **模板管理器**: 用于获取和管理模板
3. **模板渲染器**: 用于渲染模板

### 依赖注入

```go
// 设置 Discord 客户端
service.SetDiscordClient(discordClient)

// 设置模板服务
service.SetTemplateManager(templateManager)
service.SetTemplateRenderer(templateRenderer)

// 初始化服务
err := service.Initialize(ctx)
```

### 服务注册

在 `bot.go` 中，产品通知服务会自动注册到服务管理器：

```go
// 注册产品通知服务
productNotificationAdapter := notification.NewProductNotificationServiceAdapter()
serviceManager.RegisterService(productNotificationAdapter)
```

## 模板系统集成

### 智能平台检测

服务会根据产品的 `Platform` 字段自动选择对应的模板：

- `amazon` -> `amazon` 模板
- `popmart` -> `popmart` 模板  
- `aliexpress` -> `aliexpress` 模板
- 其他 -> `default` 模板

### 变量映射

产品信息会通过 `ProductItemAdapter` 转换为模板变量：

```go
variables := map[string]interface{}{
    "title":       product.Title,
    "url":         product.URL,
    "price":       product.Price,
    "platform":    product.Platform,
    "stock":       product.Stock,
    "availability": product.Availability,
    // ... 更多字段
}
```

## 错误处理

### 常见错误

1. **服务未初始化**: 确保调用 `Initialize()` 方法
2. **依赖未设置**: 确保设置了所有必需的依赖服务
3. **产品信息无效**: 检查产品信息的必需字段
4. **模板不存在**: 确保指定的模板存在
5. **Discord 发送失败**: 检查频道 ID 和权限

### 错误示例

```go
result, err := service.SendProductNotification(ctx, product, channelID)
if err != nil {
    switch {
    case strings.Contains(err.Error(), "产品信息验证失败"):
        // 处理产品信息错误
    case strings.Contains(err.Error(), "模板选择失败"):
        // 处理模板错误
    case strings.Contains(err.Error(), "消息发送失败"):
        // 处理 Discord 发送错误
    default:
        // 处理其他错误
    }
}
```

## 性能和监控

### 性能指标

通知结果包含详细的性能指标：

```go
type ProductNotificationResult struct {
    TemplateRenderTime time.Duration // 模板渲染时间
    MessageSendTime    time.Duration // 消息发送时间
    TotalTime          time.Duration // 总时间
    // ... 其他字段
}
```

### 健康检查

```go
// 检查服务健康状态
err := service.HealthCheck(ctx)
if err != nil {
    log.Printf("服务不健康: %v", err)
}
```

## 最佳实践

1. **错误处理**: 始终检查返回的错误
2. **超时设置**: 为长时间运行的操作设置合适的超时
3. **批量操作**: 对于多个通知，使用批量发送以提高性能
4. **异步处理**: 对于非关键通知，使用异步发送
5. **日志记录**: 利用内置的结构化日志记录
6. **健康检查**: 定期执行健康检查确保服务正常

## 示例代码

完整的使用示例请参考 `example.go` 文件。
