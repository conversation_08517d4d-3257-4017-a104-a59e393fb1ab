package notification

import (
	"fmt"
	"time"

	"zeka-go/internal/types"
)

// DefaultNotificationBuilder 默认通知构建器实现
type DefaultNotificationBuilder struct {
	product    *types.ProductItem
	channelID  string
	guildID    string
	templateID string
	priority   types.NotificationPriority
	timeout    time.Duration
	retryMax   int
	metadata   map[string]interface{}
	override   bool
}

// NewNotificationBuilder 创建新的通知构建器
func NewNotificationBuilder() *DefaultNotificationBuilder {
	return &DefaultNotificationBuilder{
		priority: types.PriorityNormal,
		timeout:  30 * time.Second,
		retryMax: 3,
		metadata: make(map[string]interface{}),
		override: false,
	}
}

// SetProduct 设置产品信息
func (b *DefaultNotificationBuilder) SetProduct(product *types.ProductItem) *DefaultNotificationBuilder {
	b.product = product
	return b
}

// SetChannel 设置频道ID
func (b *DefaultNotificationBuilder) SetChannel(channelID string) *DefaultNotificationBuilder {
	b.channelID = channelID
	return b
}

// SetGuild 设置公会ID
func (b *DefaultNotificationBuilder) SetGuild(guildID string) *DefaultNotificationBuilder {
	b.guildID = guildID
	return b
}

// SetTemplate 设置模板ID
func (b *DefaultNotificationBuilder) SetTemplate(templateID string) *DefaultNotificationBuilder {
	b.templateID = templateID
	b.override = true
	return b
}

// SetPriority 设置优先级
func (b *DefaultNotificationBuilder) SetPriority(priority types.NotificationPriority) *DefaultNotificationBuilder {
	b.priority = priority
	return b
}

// SetTimeout 设置超时时间（秒）
func (b *DefaultNotificationBuilder) SetTimeout(timeoutSeconds int) *DefaultNotificationBuilder {
	b.timeout = time.Duration(timeoutSeconds) * time.Second
	return b
}

// SetRetryMax 设置最大重试次数
func (b *DefaultNotificationBuilder) SetRetryMax(retryMax int) *DefaultNotificationBuilder {
	b.retryMax = retryMax
	return b
}

// SetMetadata 设置元数据
func (b *DefaultNotificationBuilder) SetMetadata(metadata map[string]interface{}) *DefaultNotificationBuilder {
	b.metadata = metadata
	return b
}

// AddMetadata 添加元数据项
func (b *DefaultNotificationBuilder) AddMetadata(key string, value interface{}) *DefaultNotificationBuilder {
	if b.metadata == nil {
		b.metadata = make(map[string]interface{})
	}
	b.metadata[key] = value
	return b
}

// Build 构建通知选项
func (b *DefaultNotificationBuilder) Build() (*types.ProductNotificationOptions, error) {
	// 验证必需字段
	if err := b.validate(); err != nil {
		return nil, fmt.Errorf("构建验证失败: %w", err)
	}

	// 构建选项
	options := &types.ProductNotificationOptions{
		Product:          b.product,
		ChannelID:        b.channelID,
		GuildID:          b.guildID,
		TemplateID:       b.templateID,
		TemplateOverride: b.override,
		Priority:         b.priority,
		Timeout:          b.timeout,
		RetryMax:         b.retryMax,
		Metadata:         b.copyMetadata(),
	}

	return options, nil
}

// Reset 重置构建器
func (b *DefaultNotificationBuilder) Reset() *DefaultNotificationBuilder {
	b.product = nil
	b.channelID = ""
	b.guildID = ""
	b.templateID = ""
	b.priority = types.PriorityNormal
	b.timeout = 30 * time.Second
	b.retryMax = 3
	b.metadata = make(map[string]interface{})
	b.override = false
	return b
}

// validate 验证构建器状态
func (b *DefaultNotificationBuilder) validate() error {
	if b.product == nil {
		return fmt.Errorf("产品信息不能为空")
	}

	if b.channelID == "" {
		return fmt.Errorf("频道ID不能为空")
	}

	if b.timeout <= 0 {
		return fmt.Errorf("超时时间必须大于0")
	}

	if b.retryMax < 0 {
		return fmt.Errorf("重试次数不能为负数")
	}

	return nil
}

// copyMetadata 复制元数据
func (b *DefaultNotificationBuilder) copyMetadata() map[string]interface{} {
	if b.metadata == nil {
		return make(map[string]interface{})
	}

	copied := make(map[string]interface{})
	for k, v := range b.metadata {
		copied[k] = v
	}
	return copied
}

// FluentNotificationBuilder 流式通知构建器（链式调用）
type FluentNotificationBuilder struct {
	*DefaultNotificationBuilder
}

// NewFluentNotificationBuilder 创建新的流式通知构建器
func NewFluentNotificationBuilder() *FluentNotificationBuilder {
	return &FluentNotificationBuilder{
		DefaultNotificationBuilder: NewNotificationBuilder(),
	}
}

// Product 设置产品信息（流式接口）
func (b *FluentNotificationBuilder) Product(product *types.ProductItem) *FluentNotificationBuilder {
	b.SetProduct(product)
	return b
}

// Channel 设置频道ID（流式接口）
func (b *FluentNotificationBuilder) Channel(channelID string) *FluentNotificationBuilder {
	b.SetChannel(channelID)
	return b
}

// Guild 设置公会ID（流式接口）
func (b *FluentNotificationBuilder) Guild(guildID string) *FluentNotificationBuilder {
	b.SetGuild(guildID)
	return b
}

// Template 设置模板ID（流式接口）
func (b *FluentNotificationBuilder) Template(templateID string) *FluentNotificationBuilder {
	b.SetTemplate(templateID)
	return b
}

// Priority 设置优先级（流式接口）
func (b *FluentNotificationBuilder) Priority(priority types.NotificationPriority) *FluentNotificationBuilder {
	b.SetPriority(priority)
	return b
}

// Timeout 设置超时时间（流式接口）
func (b *FluentNotificationBuilder) Timeout(timeoutSeconds int) *FluentNotificationBuilder {
	b.SetTimeout(timeoutSeconds)
	return b
}

// Retry 设置重试次数（流式接口）
func (b *FluentNotificationBuilder) Retry(retryMax int) *FluentNotificationBuilder {
	b.SetRetryMax(retryMax)
	return b
}

// Metadata 设置元数据（流式接口）
func (b *FluentNotificationBuilder) Metadata(metadata map[string]interface{}) *FluentNotificationBuilder {
	b.SetMetadata(metadata)
	return b
}

// Meta 添加元数据项（流式接口）
func (b *FluentNotificationBuilder) Meta(key string, value interface{}) *FluentNotificationBuilder {
	b.AddMetadata(key, value)
	return b
}

// 便捷方法

// ForAmazon 为Amazon产品配置构建器
func (b *FluentNotificationBuilder) ForAmazon() *FluentNotificationBuilder {
	return b.Template("amazon").Priority(types.PriorityNormal)
}

// ForPopMart 为PopMart产品配置构建器
func (b *FluentNotificationBuilder) ForPopMart() *FluentNotificationBuilder {
	return b.Template("popmart").Priority(types.PriorityHigh)
}

// ForAliExpress 为AliExpress产品配置构建器
func (b *FluentNotificationBuilder) ForAliExpress() *FluentNotificationBuilder {
	return b.Template("aliexpress").Priority(types.PriorityNormal)
}

// WithHighPriority 设置高优先级
func (b *FluentNotificationBuilder) WithHighPriority() *FluentNotificationBuilder {
	return b.Priority(types.PriorityHigh)
}

// WithLowPriority 设置低优先级
func (b *FluentNotificationBuilder) WithLowPriority() *FluentNotificationBuilder {
	return b.Priority(types.PriorityLow)
}

// WithQuickTimeout 设置快速超时（10秒）
func (b *FluentNotificationBuilder) WithQuickTimeout() *FluentNotificationBuilder {
	return b.Timeout(10)
}

// WithLongTimeout 设置长超时（60秒）
func (b *FluentNotificationBuilder) WithLongTimeout() *FluentNotificationBuilder {
	return b.Timeout(60)
}

// WithNoRetry 设置不重试
func (b *FluentNotificationBuilder) WithNoRetry() *FluentNotificationBuilder {
	return b.Retry(0)
}

// WithMaxRetry 设置最大重试次数（5次）
func (b *FluentNotificationBuilder) WithMaxRetry() *FluentNotificationBuilder {
	return b.Retry(5)
}
