package template

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"gopkg.in/yaml.v3"
)

// Manager 模板管理器
type Manager struct {
	templates     map[string]*types.Template
	templateIndex *types.TemplateIndex
	templatesPath string
	mu            sync.RWMutex
}

// NewManager 创建新的模板管理器
func NewManager(templatesPath string) *Manager {
	return &Manager{
		templates:     make(map[string]*types.Template),
		templatesPath: templatesPath,
	}
}

// Initialize 初始化模板管理器
func (m *Manager) Initialize(ctx context.Context) error {
	logger.Info("📝 初始化模板管理器...")

	// 确保模板目录存在
	if err := m.ensureTemplatesDirectory(); err != nil {
		return fmt.Errorf("创建模板目录失败: %w", err)
	}

	// 加载模板索引
	if err := m.loadTemplateIndex(); err != nil {
		logger.Warn("加载模板索引失败，将创建默认索引", "error", err)
		m.createDefaultIndex()
	}

	// 加载所有模板
	if err := m.loadAllTemplates(); err != nil {
		return fmt.Errorf("加载模板失败: %w", err)
	}

	// 尝试加载YAML embed模板
	yamlPaths := []string{
		"configs/embed_templates.yaml",
		"./configs/embed_templates.yaml",
		"../configs/embed_templates.yaml",
		"../../configs/embed_templates.yaml",
	}

	for _, yamlPath := range yamlPaths {
		if err := m.LoadTemplatesFromYAML(yamlPath); err == nil {
			logger.Info("✅ 成功加载YAML embed模板", "path", yamlPath)
			break
		} else {
			logger.Debug("尝试加载YAML模板失败", "path", yamlPath, "error", err)
		}
	}

	logger.Info("✅ 模板管理器初始化完成", "templates_count", len(m.templates))
	return nil
}

// GetTemplate 获取模板
func (m *Manager) GetTemplate(templateID string, guildID ...string) (*types.Template, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// 如果指定了 guildID，优先查找服务器专用模板
	if len(guildID) > 0 && guildID[0] != "" {
		guildTemplateKey := fmt.Sprintf("%s:%s", guildID[0], templateID)
		if template, exists := m.templates[guildTemplateKey]; exists {
			return template, nil
		}
	}

	// 查找全局模板
	if template, exists := m.templates[templateID]; exists {
		return template, nil
	}

	return nil, fmt.Errorf("模板 %s 不存在", templateID)
}

// AddTemplate 添加模板
func (m *Manager) AddTemplate(template *types.Template) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 验证模板
	if err := m.validateTemplate(template); err != nil {
		return fmt.Errorf("模板验证失败: %w", err)
	}

	// 生成模板键
	key := template.ID
	if template.GuildID != nil && *template.GuildID != "" {
		key = fmt.Sprintf("%s:%s", *template.GuildID, template.ID)
	}

	// 设置时间戳
	now := time.Now()
	template.Metadata.CreatedAt = now
	template.Metadata.UpdatedAt = now

	// 添加到内存
	m.templates[key] = template

	logger.Info("✅ 模板已添加", "template_id", template.ID, "name", template.Name)
	return nil
}

// LoadTemplate 从文件加载模板
func (m *Manager) LoadTemplate(templateID string, guildID ...string) (*types.Template, error) {
	// 先尝试从内存获取
	if template, err := m.GetTemplate(templateID, guildID...); err == nil {
		return template, nil
	}

	// 从文件加载
	return m.loadTemplateFromFile(templateID, guildID...)
}

// ReloadTemplates 重新加载所有模板
func (m *Manager) ReloadTemplates() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 清空当前模板
	m.templates = make(map[string]*types.Template)

	// 重新加载模板索引
	if err := m.loadTemplateIndex(); err != nil {
		return fmt.Errorf("重新加载模板索引失败: %w", err)
	}

	// 重新加载所有模板
	if err := m.loadAllTemplates(); err != nil {
		return fmt.Errorf("重新加载模板失败: %w", err)
	}

	logger.Info("✅ 模板重新加载完成", "templates_count", len(m.templates))
	return nil
}

// ValidateTemplate 验证模板
func (m *Manager) ValidateTemplate(template *types.Template) error {
	return m.validateTemplate(template)
}

// GetTemplateList 获取模板列表
func (m *Manager) GetTemplateList(guildID ...string) []*types.Template {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var templates []*types.Template
	for key, template := range m.templates {
		// 如果指定了 guildID，只返回该服务器的模板和全局模板
		if len(guildID) > 0 && guildID[0] != "" {
			if strings.Contains(key, ":") {
				// 服务器专用模板
				parts := strings.SplitN(key, ":", 2)
				if parts[0] == guildID[0] {
					templates = append(templates, template)
				}
			} else {
				// 全局模板
				templates = append(templates, template)
			}
		} else {
			// 返回所有模板
			templates = append(templates, template)
		}
	}

	return templates
}

// GetTemplateByPlatform 根据平台获取模板
func (m *Manager) GetTemplateByPlatform(platform string, guildID ...string) (*types.Template, error) {
	// 使用适配器进行平台到模板ID的映射
	adapter := NewProductItemAdapter()
	templateID := adapter.GetTemplateIDForPlatform(platform)

	return m.GetTemplate(templateID, guildID...)
}

// GetTemplateForProduct 根据产品信息获取最适合的模板
func (m *Manager) GetTemplateForProduct(product *types.ProductItem, guildID ...string) (*types.Template, error) {
	if product == nil {
		return m.GetTemplate("default", guildID...)
	}

	// 使用适配器进行智能平台检测
	adapter := NewProductItemAdapter()
	templateID := adapter.DetectPlatformTemplate(product)

	return m.GetTemplate(templateID, guildID...)
}

// GetTemplatesByCategory 按分类获取模板
func (m *Manager) GetTemplatesByCategory(category string, guildID ...string) []*types.Template {
	templates := m.GetTemplateList(guildID...)
	var filtered []*types.Template

	for _, template := range templates {
		if template.Category == category {
			filtered = append(filtered, template)
		}
	}

	return filtered
}

// LoadTemplatesFromYAML 从YAML配置文件加载模板
func (m *Manager) LoadTemplatesFromYAML(yamlPath string) error {
	logger.Info("📄 从YAML文件加载模板", "path", yamlPath)

	// 读取YAML文件
	data, err := os.ReadFile(yamlPath)
	if err != nil {
		return fmt.Errorf("读取YAML模板文件失败: %w", err)
	}

	// 解析YAML配置
	var yamlConfig YAMLTemplateConfig
	if err := yaml.Unmarshal(data, &yamlConfig); err != nil {
		return fmt.Errorf("解析YAML模板文件失败: %w", err)
	}

	// 转换并添加模板
	for templateID, yamlTemplate := range yamlConfig.Templates {
		template, err := m.convertYAMLEmbedToTemplate(templateID, yamlTemplate)
		if err != nil {
			logger.Warn("转换YAML模板失败", "template_id", templateID, "error", err)
			continue
		}

		if err := m.AddTemplate(template); err != nil {
			logger.Warn("添加YAML模板失败", "template_id", templateID, "error", err)
			continue
		}

		logger.Debug("✅ YAML模板加载成功", "template_id", templateID, "name", template.Name)
	}

	logger.Info("✅ YAML模板加载完成", "loaded_count", len(yamlConfig.Templates))
	return nil
}

// ensureTemplatesDirectory 确保模板目录存在
func (m *Manager) ensureTemplatesDirectory() error {
	dirs := []string{
		m.templatesPath,
		filepath.Join(m.templatesPath, "global"),
		filepath.Join(m.templatesPath, "global", "system"),
		filepath.Join(m.templatesPath, "global", "shared"),
		filepath.Join(m.templatesPath, "global", "monitors"),
		filepath.Join(m.templatesPath, "guilds"),
		filepath.Join(m.templatesPath, "metadata"),
		filepath.Join(m.templatesPath, "categories"),
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录 %s 失败: %w", dir, err)
		}
	}

	return nil
}

// loadTemplateIndex 加载模板索引
func (m *Manager) loadTemplateIndex() error {
	indexPath := filepath.Join(m.templatesPath, "metadata", "template_index.json")

	data, err := os.ReadFile(indexPath)
	if err != nil {
		return fmt.Errorf("读取模板索引文件失败: %w", err)
	}

	var index types.TemplateIndex
	if err := json.Unmarshal(data, &index); err != nil {
		return fmt.Errorf("解析模板索引失败: %w", err)
	}

	m.templateIndex = &index
	logger.Debug("📁 模板索引加载完成", "templates_count", len(index.Templates))
	return nil
}

// createDefaultIndex 创建默认模板索引
func (m *Manager) createDefaultIndex() {
	m.templateIndex = &types.TemplateIndex{
		Version:     "1.0.0",
		LastUpdated: time.Now().Format(time.RFC3339),
		Templates:   []types.TemplateIndexItem{},
		Categories:  []string{"system", "general", "member_events", "moderation", "announcements", "monitors"},
	}
}

// loadAllTemplates 加载所有模板
func (m *Manager) loadAllTemplates() error {
	if m.templateIndex == nil {
		return fmt.Errorf("模板索引未加载")
	}

	for _, item := range m.templateIndex.Templates {
		if err := m.loadTemplateFromIndex(item); err != nil {
			logger.Warn("加载模板失败", "template_id", item.TemplateID, "error", err)
			continue
		}
	}

	return nil
}

// loadTemplateFromIndex 从索引项加载模板
func (m *Manager) loadTemplateFromIndex(item types.TemplateIndexItem) error {
	templatePath := filepath.Join(m.templatesPath, item.FilePath)

	data, err := os.ReadFile(templatePath)
	if err != nil {
		return fmt.Errorf("读取模板文件失败: %w", err)
	}

	var template types.Template
	if err := json.Unmarshal(data, &template); err != nil {
		return fmt.Errorf("解析模板失败: %w", err)
	}

	// 生成模板键
	key := template.ID
	if template.GuildID != nil && *template.GuildID != "" {
		key = fmt.Sprintf("%s:%s", *template.GuildID, template.ID)
	}

	m.templates[key] = &template
	logger.Debug("✅ 模板加载成功", "template_id", template.ID, "name", template.Name)
	return nil
}

// loadTemplateFromFile 从文件加载单个模板
func (m *Manager) loadTemplateFromFile(templateID string, guildID ...string) (*types.Template, error) {
	// 这里可以实现从文件系统动态加载模板的逻辑
	// 暂时返回错误，表示模板不存在
	return nil, fmt.Errorf("模板 %s 不存在", templateID)
}

// validateTemplate 验证模板
func (m *Manager) validateTemplate(template *types.Template) error {
	if template.ID == "" {
		return fmt.Errorf("模板 ID 不能为空")
	}

	if template.Name == "" {
		return fmt.Errorf("模板名称不能为空")
	}

	if template.Type == "" {
		return fmt.Errorf("模板类型不能为空")
	}

	if template.Content == nil {
		return fmt.Errorf("模板内容不能为空")
	}

	return nil
}

// YAMLTemplateConfig YAML模板配置结构
type YAMLTemplateConfig struct {
	Templates map[string]YAMLTemplateDefinition `yaml:"templates"`
}

// YAMLTemplateDefinition YAML模板定义
type YAMLTemplateDefinition struct {
	Embed YAMLEmbedTemplate `yaml:"embed"`
}

// YAMLEmbedTemplate YAML embed模板结构
type YAMLEmbedTemplate struct {
	Title       string                `yaml:"title"`
	Description string                `yaml:"description"` // 添加缺失的 description 字段
	URL         string                `yaml:"url"`
	Color       int                   `yaml:"color"`
	Thumbnail   YAMLThumbnailTemplate `yaml:"thumbnail"`
	Fields      []YAMLFieldTemplate   `yaml:"fields"`
	Footer      YAMLFooterTemplate    `yaml:"footer"`
	Timestamp   string                `yaml:"timestamp"`
}

// YAMLThumbnailTemplate YAML缩略图模板
type YAMLThumbnailTemplate struct {
	URL string `yaml:"url"`
}

// YAMLFieldTemplate YAML字段模板
type YAMLFieldTemplate struct {
	Name   string `yaml:"name"`
	Value  string `yaml:"value"`
	Inline bool   `yaml:"inline"`
}

// YAMLFooterTemplate YAML页脚模板
type YAMLFooterTemplate struct {
	Text string `yaml:"text"`
}

// convertYAMLEmbedToTemplate 将YAML embed模板转换为types.Template
func (m *Manager) convertYAMLEmbedToTemplate(templateID string, yamlTemplate YAMLTemplateDefinition) (*types.Template, error) {
	// 创建基础模板结构
	template := &types.Template{
		ID:          templateID,
		Name:        fmt.Sprintf("Embed模板 - %s", templateID),
		Description: fmt.Sprintf("从YAML配置加载的%s平台embed模板", templateID),
		Type:        "embed",
		Category:    "message_forward",
		Version:     "1.0.0",
		IsActive:    true,
		AccessLevel: "global",
		Variables:   m.generateEmbedTemplateVariables(),
		Metadata: types.TemplateMetadata{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Author:    "system",
			Version:   "1.0.0",
			IsSystem:  true,
			Tags:      []string{"embed", "message_forward", templateID},
		},
	}

	// 转换embed内容
	embedContent := map[string]interface{}{
		"embed": map[string]interface{}{
			"title":       yamlTemplate.Embed.Title,
			"description": yamlTemplate.Embed.Description, // 添加缺失的 description 字段
			"url":         yamlTemplate.Embed.URL,
			"color":       yamlTemplate.Embed.Color,
			"timestamp":   yamlTemplate.Embed.Timestamp,
		},
	}

	// 处理缩略图
	if yamlTemplate.Embed.Thumbnail.URL != "" {
		embedContent["embed"].(map[string]interface{})["thumbnail"] = map[string]interface{}{
			"url": yamlTemplate.Embed.Thumbnail.URL,
		}
	}

	// 处理字段 - 修复：使用[]interface{}而不是[]map[string]interface{}
	if len(yamlTemplate.Embed.Fields) > 0 {
		fields := make([]interface{}, len(yamlTemplate.Embed.Fields))
		for i, field := range yamlTemplate.Embed.Fields {
			fields[i] = map[string]interface{}{
				"name":   field.Name,
				"value":  field.Value,
				"inline": field.Inline,
			}
		}
		embedContent["embed"].(map[string]interface{})["fields"] = fields
	}

	// 处理页脚
	if yamlTemplate.Embed.Footer.Text != "" {
		embedContent["embed"].(map[string]interface{})["footer"] = map[string]interface{}{
			"text": yamlTemplate.Embed.Footer.Text,
		}
	}

	template.Content = embedContent

	return template, nil
}

// generateEmbedTemplateVariables 生成embed模板的变量定义
func (m *Manager) generateEmbedTemplateVariables() map[string]types.TemplateVar {
	return map[string]types.TemplateVar{
		"title": {
			Type:        "string",
			Required:    true,
			Description: "产品标题",
		},
		"url": {
			Type:        "string",
			Required:    true,
			Description: "产品链接",
		},
		"productId": {
			Type:        "string",
			Required:    true,
			Description: "产品ID",
		},
		"price": {
			Type:         "string",
			Required:     false,
			Description:  "产品价格",
			DefaultValue: "价格未知",
		},
		"availability": {
			Type:         "string",
			Required:     false,
			Description:  "库存状态",
			DefaultValue: "未知",
		},
		"image_url": {
			Type:        "string",
			Required:    false,
			Description: "产品图片URL",
		},
		"thumbnail_url": {
			Type:        "string",
			Required:    false,
			Description: "缩略图URL",
		},
		"author_name": {
			Type:         "string",
			Required:     false,
			Description:  "作者名称",
			DefaultValue: "PopNest",
		},
		"timestamp": {
			Type:        "string",
			Required:    false,
			Description: "时间戳",
		},
		"skuId": {
			Type:        "string",
			Required:    false,
			Description: "SKU ID",
		},
		"atcLink": {
			Type:        "string",
			Required:    false,
			Description: "加购链接",
		},
		"platform": {
			Type:        "string",
			Required:    false,
			Description: "平台名称",
		},
		"stock": {
			Type:        "string",
			Required:    false,
			Description: "库存显示",
		},
		"releaseDate": {
			Type:        "string",
			Required:    false,
			Description: "发布日期",
		},
	}
}
