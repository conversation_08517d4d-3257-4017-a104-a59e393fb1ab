package template

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"zeka-go/internal/services/logger"

	"github.com/bwmarrin/discordgo"
)

// StandardRenderer 标准模板渲染器实现
type StandardRenderer struct {
	// 配置选项
	strictMode bool
}

// NewStandardRenderer 创建新的标准模板渲染器
func NewStandardRenderer() *StandardRenderer {
	return &StandardRenderer{
		strictMode: false,
	}
}

// Render 渲染模板
func (r *StandardRenderer) Render(ctx RenderContext) (*RenderedContent, error) {
	startTime := time.Now()

	// 验证输入
	if err := r.validateInput(ctx); err != nil {
		return nil, fmt.Errorf("输入验证失败: %w", err)
	}

	result := &RenderedContent{}

	// 渲染内容
	if err := r.renderContent(ctx, result); err != nil {
		return nil, fmt.Errorf("渲染失败: %w", err)
	}

	result.RenderingTime = time.Since(startTime)

	logger.Debug("模板渲染完成",
		"template_id", ctx.Template.ID,
		"rendering_time_ns", result.RenderingTime,
		"embeds_count", len(result.Embeds))

	return result, nil
}

// RenderEmbed 渲染嵌入消息
func (r *StandardRenderer) RenderEmbed(embedData interface{}, variables map[string]interface{}) (*discordgo.MessageEmbed, error) {
	embedMap, ok := embedData.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("embed数据格式错误")
	}

	embed := &discordgo.MessageEmbed{}

	// 渲染基础字段
	if title, exists := embedMap["title"]; exists {
		if titleStr, ok := title.(string); ok {
			embed.Title = r.ReplaceVariables(titleStr, variables)
		}
	}

	if description, exists := embedMap["description"]; exists {
		if descStr, ok := description.(string); ok {
			embed.Description = r.ReplaceVariables(descStr, variables)
		}
	}

	if url, exists := embedMap["url"]; exists {
		if urlStr, ok := url.(string); ok {
			embed.URL = r.ReplaceVariables(urlStr, variables)
		}
	}

	// 渲染颜色
	if color, exists := embedMap["color"]; exists {
		embed.Color = r.parseColor(color, variables)
	}

	// 渲染缩略图
	if thumbnail, exists := embedMap["thumbnail"]; exists {
		embed.Thumbnail = r.renderThumbnail(thumbnail, variables)
	}

	// 渲染字段
	if fields, exists := embedMap["fields"]; exists {
		embed.Fields = r.renderFields(fields, variables)
	}

	// 渲染页脚
	if footer, exists := embedMap["footer"]; exists {
		embed.Footer = r.renderFooter(footer, variables)
	}

	// 渲染时间戳
	if timestamp, exists := embedMap["timestamp"]; exists {
		if timestampStr, ok := timestamp.(string); ok {
			embed.Timestamp = r.ReplaceVariables(timestampStr, variables)
		}
	}

	return embed, nil
}

// ReplaceVariables 替换变量
func (r *StandardRenderer) ReplaceVariables(text string, variables map[string]interface{}) string {
	if text == "" {
		return text
	}

	// 处理单花括号 {variable} 格式
	singlePattern := regexp.MustCompile(`\{([^}]+)\}`)
	text = singlePattern.ReplaceAllStringFunc(text, func(match string) string {
		varName := strings.TrimSpace(match[1 : len(match)-1])
		if value, exists := variables[varName]; exists && value != nil {
			return fmt.Sprintf("%v", value)
		}
		if r.strictMode {
			logger.Warn("模板变量未找到", "variable", varName)
		}
		return match // 保留原始占位符
	})

	// 处理双花括号 {{variable}} 格式
	doublePattern := regexp.MustCompile(`\{\{([^}]+)\}\}`)
	text = doublePattern.ReplaceAllStringFunc(text, func(match string) string {
		varName := strings.TrimSpace(match[2 : len(match)-2])
		if value, exists := variables[varName]; exists && value != nil {
			return fmt.Sprintf("%v", value)
		}
		if r.strictMode {
			logger.Warn("模板变量未找到", "variable", varName)
		}
		return match // 保留原始占位符
	})

	return text
}

// validateInput 验证输入
func (r *StandardRenderer) validateInput(ctx RenderContext) error {
	if ctx.Template == nil {
		return fmt.Errorf("模板不能为空")
	}

	if ctx.Variables == nil {
		ctx.Variables = make(map[string]interface{})
	}

	return nil
}

// renderContent 渲染内容
func (r *StandardRenderer) renderContent(ctx RenderContext, result *RenderedContent) error {
	content := ctx.Template.Content

	switch v := content.(type) {
	case string:
		// 简单文本内容
		result.Content = r.ReplaceVariables(v, ctx.Variables)
	case map[string]interface{}:
		// 复杂内容对象
		return r.renderComplexContent(v, ctx.Variables, result)
	default:
		return fmt.Errorf("不支持的内容类型: %T", content)
	}

	return nil
}

// renderComplexContent 渲染复杂内容
func (r *StandardRenderer) renderComplexContent(content map[string]interface{}, variables map[string]interface{}, result *RenderedContent) error {
	// 处理文本内容
	if textContent, exists := content["content"]; exists {
		if text, ok := textContent.(string); ok {
			result.Content = r.ReplaceVariables(text, variables)
		}
	}

	// 处理嵌入消息
	if embedData, exists := content["embed"]; exists {
		embed, err := r.RenderEmbed(embedData, variables)
		if err != nil {
			return fmt.Errorf("渲染embed失败: %w", err)
		}

		// 直接添加embed
		result.Embeds = append(result.Embeds, embed)
	}

	return nil
}

// parseColor 解析颜色
func (r *StandardRenderer) parseColor(color interface{}, variables map[string]interface{}) int {
	switch v := color.(type) {
	case int:
		return v
	case string:
		colorStr := r.ReplaceVariables(v, variables)
		// 移除0x前缀
		colorStr = strings.TrimPrefix(colorStr, "0x")
		if colorInt, err := strconv.ParseInt(colorStr, 16, 32); err == nil {
			return int(colorInt)
		}
	}
	return 0x0099ff // 默认蓝色
}

// renderThumbnail 渲染缩略图
func (r *StandardRenderer) renderThumbnail(thumbnail interface{}, variables map[string]interface{}) *discordgo.MessageEmbedThumbnail {
	thumbnailMap, ok := thumbnail.(map[string]interface{})
	if !ok {
		return nil
	}

	if url, exists := thumbnailMap["url"]; exists {
		if urlStr, ok := url.(string); ok {
			return &discordgo.MessageEmbedThumbnail{
				URL: r.ReplaceVariables(urlStr, variables),
			}
		}
	}

	return nil
}

// renderFields 渲染字段
func (r *StandardRenderer) renderFields(fields interface{}, variables map[string]interface{}) []*discordgo.MessageEmbedField {
	fieldsList, ok := fields.([]interface{})
	if !ok {
		return nil
	}

	var result []*discordgo.MessageEmbedField
	for _, fieldData := range fieldsList {
		if fieldMap, ok := fieldData.(map[string]interface{}); ok {
			field := &discordgo.MessageEmbedField{}

			if name, exists := fieldMap["name"]; exists {
				if nameStr, ok := name.(string); ok {
					field.Name = r.ReplaceVariables(nameStr, variables)
				}
			}

			if value, exists := fieldMap["value"]; exists {
				if valueStr, ok := value.(string); ok {
					field.Value = r.ReplaceVariables(valueStr, variables)
				}
			}

			if inline, exists := fieldMap["inline"]; exists {
				if inlineBool, ok := inline.(bool); ok {
					field.Inline = inlineBool
				}
			}

			result = append(result, field)
		}
	}

	return result
}

// renderFooter 渲染页脚
func (r *StandardRenderer) renderFooter(footer interface{}, variables map[string]interface{}) *discordgo.MessageEmbedFooter {
	footerMap, ok := footer.(map[string]interface{})
	if !ok {
		return nil
	}

	if text, exists := footerMap["text"]; exists {
		if textStr, ok := text.(string); ok {
			return &discordgo.MessageEmbedFooter{
				Text: r.ReplaceVariables(textStr, variables),
			}
		}
	}

	return nil
}
