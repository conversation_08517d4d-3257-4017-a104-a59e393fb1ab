package template

import (
	"context"
	"time"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// TemplateLoader 模板加载器接口
type TemplateLoader interface {
	// LoadTemplate 加载单个模板
	LoadTemplate(templateID string, guildID ...string) (*types.Template, error)

	// LoadAllTemplates 加载所有模板
	LoadAllTemplates() (map[string]*types.Template, error)

	// ReloadTemplate 重新加载指定模板
	ReloadTemplate(templateID string, guildID ...string) (*types.Template, error)

	// ValidateTemplate 验证模板格式
	ValidateTemplate(template *types.Template) error
}

// TemplateRenderer 模板渲染器接口
type TemplateRenderer interface {
	// Render 渲染模板
	Render(ctx RenderContext) (*RenderedContent, error)

	// RenderEmbed 渲染嵌入消息
	RenderEmbed(embedData interface{}, variables map[string]interface{}) (*discordgo.MessageEmbed, error)

	// ReplaceVariables 替换变量
	ReplaceVariables(text string, variables map[string]interface{}) string
}

// PlatformAdapter 平台适配器接口
type PlatformAdapter interface {
	// GetPlatformName 获取平台名称
	GetPlatformName() string

	// SupportsProduct 检查是否支持该产品
	SupportsProduct(product *types.ProductItem) bool

	// ToTemplateVariables 转换为模板变量
	ToTemplateVariables(product *types.ProductItem) map[string]interface{}

	// GetTemplateID 获取模板ID
	GetTemplateID() string

	// ValidateProduct 验证产品数据
	ValidateProduct(product *types.ProductItem) error
}

// AdapterRegistry 适配器注册表接口
type AdapterRegistry interface {
	// RegisterAdapter 注册适配器
	RegisterAdapter(adapter PlatformAdapter) error

	// GetAdapter 获取适配器
	GetAdapter(platform string) (PlatformAdapter, error)

	// GetAdapterForProduct 根据产品获取适配器
	GetAdapterForProduct(product *types.ProductItem) (PlatformAdapter, error)

	// ListAdapters 列出所有适配器
	ListAdapters() []PlatformAdapter
}

// TemplateManager 模板管理器接口
type TemplateManager interface {
	// 初始化和生命周期
	Initialize(ctx context.Context) error
	Shutdown(ctx context.Context) error

	// 模板管理
	GetTemplate(templateID string, guildID ...string) (*types.Template, error)
	AddTemplate(template *types.Template) error
	RemoveTemplate(templateID string, guildID ...string) error
	ListTemplates(guildID ...string) ([]*types.Template, error)

	// 模板加载和重载
	LoadTemplate(templateID string, guildID ...string) (*types.Template, error)
	ReloadTemplates() error

	// 智能模板选择
	GetTemplateForProduct(product *types.ProductItem, guildID ...string) (*types.Template, error)
	GetTemplateByPlatform(platform string, guildID ...string) (*types.Template, error)

	// 验证
	ValidateTemplate(template *types.Template) error
}

// NotificationBuilder 通知构建器接口
type NotificationBuilder interface {
	// 设置基础信息
	SetProduct(product *types.ProductItem) NotificationBuilder
	SetChannel(channelID string) NotificationBuilder
	SetGuild(guildID string) NotificationBuilder

	// 设置选项
	SetTemplate(templateID string) NotificationBuilder
	SetPriority(priority types.NotificationPriority) NotificationBuilder
	SetTimeout(timeout int) NotificationBuilder
	SetRetryMax(retryMax int) NotificationBuilder

	// 设置元数据
	SetMetadata(metadata map[string]interface{}) NotificationBuilder
	AddMetadata(key string, value interface{}) NotificationBuilder

	// 构建
	Build() (*types.ProductNotificationOptions, error)

	// 重置
	Reset() NotificationBuilder
}

// NotificationSender 通知发送器接口
type NotificationSender interface {
	// SendNotification 发送通知
	SendNotification(ctx context.Context, options *types.ProductNotificationOptions) (*types.ProductNotificationResult, error)

	// SendBulkNotifications 批量发送通知
	SendBulkNotifications(ctx context.Context, notifications []*types.ProductNotificationOptions) ([]*types.ProductNotificationResult, error)

	// SendAsyncNotification 异步发送通知
	SendAsyncNotification(ctx context.Context, options *types.ProductNotificationOptions) (string, error)

	// GetNotificationStatus 获取通知状态
	GetNotificationStatus(notificationID string) (*types.ProductNotificationResult, error)
}

// TemplateService 模板服务接口（组合接口）
type TemplateService interface {
	TemplateManager
	TemplateRenderer
}

// NotificationService 通知服务接口（组合接口）
type NotificationService interface {
	NotificationSender

	// 依赖注入
	SetDiscordClient(client *types.Client) error
	SetTemplateService(service TemplateService) error
	SetAdapterRegistry(registry AdapterRegistry) error

	// 健康检查
	HealthCheck(ctx context.Context) error
}

// RenderContext 渲染上下文
type RenderContext struct {
	Template  *types.Template
	Variables map[string]interface{}
	GuildID   string
	Options   map[string]interface{}
}

// RenderedContent 渲染结果
type RenderedContent struct {
	Content       string
	Embeds        []*discordgo.MessageEmbed
	RenderingTime time.Duration
	Variables     map[string]interface{}
}

// 向后兼容的类型别名
type Renderer = StandardRenderer

// 向后兼容的构造函数
func NewRenderer() *StandardRenderer {
	return NewStandardRenderer()
}
