package template

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"zeka-go/internal/types"
)

// ProductItemAdapter 产品信息到模板变量的适配器
// 专注于变量映射，平台检测功能已移至AdapterRegistry
type ProductItemAdapter struct {
	// 可以添加配置选项
}

// NewProductItemAdapter 创建新的产品信息适配器
func NewProductItemAdapter() *ProductItemAdapter {
	return &ProductItemAdapter{}
}

// ToTemplateVariables 将 ProductItem 转换为模板变量 map
func (adapter *ProductItemAdapter) ToTemplateVariables(product *types.ProductItem) map[string]interface{} {
	if product == nil {
		return make(map[string]interface{})
	}

	variables := make(map[string]interface{})

	// 基础字段映射
	variables["title"] = product.Title
	variables["url"] = product.URL
	variables["productId"] = product.ProductID
	variables["price"] = product.Price
	variables["platform"] = product.Platform

	// 处理可选字段（指针类型）
	if product.Description != nil {
		variables["description"] = *product.Description
	} else {
		variables["description"] = ""
	}

	if product.ImageURL != nil {
		variables["image_url"] = *product.ImageURL
		variables["imageUrl"] = *product.ImageURL // 向后兼容
	} else {
		variables["image_url"] = ""
		variables["imageUrl"] = ""
	}

	if product.ThumbnailURL != nil {
		variables["thumbnail_url"] = *product.ThumbnailURL
		variables["thumbnailUrl"] = *product.ThumbnailURL // 向后兼容
	} else {
		variables["thumbnail_url"] = ""
		variables["thumbnailUrl"] = ""
	}

	if product.SkuID != nil {
		variables["skuId"] = *product.SkuID
		variables["sku_id"] = *product.SkuID // 下划线格式
	} else {
		variables["skuId"] = ""
		variables["sku_id"] = ""
	}

	if product.AtcLink != nil {
		variables["atcLink"] = *product.AtcLink
		variables["atc_link"] = *product.AtcLink // 下划线格式
	} else {
		variables["atcLink"] = ""
		variables["atc_link"] = ""
	}

	if product.ReleaseDate != nil {
		variables["releaseDate"] = *product.ReleaseDate
		variables["release_date"] = *product.ReleaseDate // 下划线格式
	} else {
		variables["releaseDate"] = ""
		variables["release_date"] = ""
	}

	if product.AuthorName != nil {
		variables["author_name"] = *product.AuthorName
		variables["authorName"] = *product.AuthorName // 驼峰格式
	} else {
		variables["author_name"] = "PopNest"
		variables["authorName"] = "PopNest"
	}

	// 库存和可用性（将 stock 转换为字符串以兼容模板验证）
	variables["stock"] = strconv.Itoa(product.Stock)
	variables["availability"] = product.Availability

	// 动态库存显示
	variables["stock_display"] = adapter.getStockDisplay(product)

	// 时间戳处理
	if product.Timestamp != nil {
		variables["timestamp"] = *product.Timestamp
	} else {
		variables["timestamp"] = time.Now().Format(time.RFC3339)
	}

	// 动态颜色（用于支持基于库存状态的颜色变化）
	variables["dynamic_color"] = adapter.getDynamicColor(product)

	// 添加元数据
	if product.Metadata != nil {
		for key, value := range product.Metadata {
			// 避免覆盖已有的关键字段
			if _, exists := variables[key]; !exists {
				variables[key] = value
			}
		}
	}

	return variables
}

// getStockDisplay 获取库存显示文本
func (adapter *ProductItemAdapter) getStockDisplay(product *types.ProductItem) string {
	// 优先使用 availability 字段
	if product.Availability != "" && product.Availability != "unknown" {
		return product.Availability
	}

	// 根据库存数量显示状态
	if product.Stock > 0 {
		return fmt.Sprintf("有库存 (%d)", product.Stock)
	} else if product.Stock == 0 {
		return "缺货"
	}

	return "未知"
}

// getDynamicColor 获取动态颜色（十六进制字符串格式）
func (adapter *ProductItemAdapter) getDynamicColor(product *types.ProductItem) string {
	// 对于Amazon，根据库存状态动态设置颜色
	if strings.ToLower(product.Platform) == "amazon" {
		if product.Availability == "In Stock" || product.Availability == "有库存" {
			return "0x00dd00" // 绿色
		} else if product.Availability == "Out of Stock" || product.Availability == "无库存" {
			return "0xff4444" // 红色
		}
	}

	// 根据平台返回默认颜色
	switch strings.ToLower(product.Platform) {
	case "popmart":
		return "0xff69b4" // POP MART 粉色
	case "aliexpress":
		return "0xff4500" // AliExpress 橙红色
	case "amazon", "amazon-wishlist":
		return "0x0099ff" // Amazon 蓝色
	default:
		return "0x0099ff" // 默认蓝色
	}
}

// GetColorInt 将颜色字符串转换为整数（用于 Discord embed）
func (adapter *ProductItemAdapter) GetColorInt(colorStr string) int {
	// 移除 0x 前缀
	colorStr = strings.TrimPrefix(colorStr, "0x")

	// 转换为整数
	if colorInt, err := strconv.ParseInt(colorStr, 16, 32); err == nil {
		return int(colorInt)
	}

	// 默认蓝色
	return 0x0099ff
}

// ValidateProductItem 验证 ProductItem 数据完整性
func (adapter *ProductItemAdapter) ValidateProductItem(product *types.ProductItem) error {
	if product == nil {
		return fmt.Errorf("产品信息不能为空")
	}

	if product.Title == "" {
		return fmt.Errorf("产品标题不能为空")
	}

	if product.ProductID == "" {
		return fmt.Errorf("产品ID不能为空")
	}

	return nil
}

// DetectPlatformTemplate 智能检测产品对应的模板ID
// 已弃用：请使用AdapterRegistry.GetAdapterForProduct()
// 保留此方法仅为向后兼容
func (adapter *ProductItemAdapter) DetectPlatformTemplate(product *types.ProductItem) string {
	if product == nil {
		return "default"
	}

	// 简化的平台检测逻辑
	platform := strings.ToLower(product.Platform)
	switch platform {
	case "amazon":
		return "amazon"
	case "popmart", "pop mart":
		return "popmart"
	case "aliexpress":
		return "aliexpress"
	case "amazon-wishlist":
		return "amazon-wishlist"
	default:
		return "default"
	}
}

// GetTemplateIDForPlatform 根据平台名称获取模板ID
// 已弃用：请使用AdapterRegistry.GetAdapter()
// 保留此方法仅为向后兼容
func (adapter *ProductItemAdapter) GetTemplateIDForPlatform(platform string) string {
	switch strings.ToLower(platform) {
	case "amazon":
		return "amazon"
	case "popmart", "pop mart":
		return "popmart"
	case "aliexpress":
		return "aliexpress"
	case "amazon-wishlist":
		return "amazon-wishlist"
	default:
		return "default"
	}
}
