package template

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"gopkg.in/yaml.v3"
)

// YAMLTemplateLoader YAML模板加载器实现
type YAMLTemplateLoader struct {
	templatesPath string
	templateIndex *types.TemplateIndex
}

// NewYAMLTemplateLoader 创建新的YAML模板加载器
func NewYAMLTemplateLoader(templatesPath string) *YAMLTemplateLoader {
	return &YAMLTemplateLoader{
		templatesPath: templatesPath,
	}
}

// LoadTemplate 加载单个模板
func (l *YAMLTemplateLoader) LoadTemplate(templateID string, guildID ...string) (*types.Template, error) {
	// 构建文件路径
	filePath := l.getTemplateFilePath(templateID, guildID...)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("模板文件不存在: %s", filePath)
	}

	// 读取文件内容
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取模板文件失败: %w", err)
	}

	// 根据文件扩展名选择解析方式
	if strings.HasSuffix(filePath, ".yaml") || strings.HasSuffix(filePath, ".yml") {
		return l.parseYAMLTemplate(templateID, data, guildID...)
	} else if strings.HasSuffix(filePath, ".json") {
		return l.parseJSONTemplate(templateID, data, guildID...)
	}

	return nil, fmt.Errorf("不支持的模板文件格式: %s", filePath)
}

// LoadAllTemplates 加载所有模板
func (l *YAMLTemplateLoader) LoadAllTemplates() (map[string]*types.Template, error) {
	templates := make(map[string]*types.Template)

	// 加载模板索引
	if err := l.loadTemplateIndex(); err != nil {
		logger.Warn("加载模板索引失败，将扫描目录", "error", err)
		return l.scanAndLoadTemplates()
	}

	// 根据索引加载模板
	for _, item := range l.templateIndex.Templates {
		var guildID string
		if item.GuildID != nil {
			guildID = *item.GuildID
		}
		template, err := l.LoadTemplate(item.TemplateID, guildID)
		if err != nil {
			logger.Error("加载模板失败", "template_id", item.TemplateID, "error", err)
			continue
		}

		key := item.TemplateID
		if item.GuildID != nil && *item.GuildID != "" {
			key = fmt.Sprintf("%s:%s", *item.GuildID, item.TemplateID)
		}

		templates[key] = template
	}

	logger.Info("模板加载完成", "count", len(templates))
	return templates, nil
}

// ReloadTemplate 重新加载指定模板
func (l *YAMLTemplateLoader) ReloadTemplate(templateID string, guildID ...string) (*types.Template, error) {
	return l.LoadTemplate(templateID, guildID...)
}

// ValidateTemplate 验证模板格式
func (l *YAMLTemplateLoader) ValidateTemplate(template *types.Template) error {
	if template == nil {
		return fmt.Errorf("模板不能为空")
	}

	if template.ID == "" {
		return fmt.Errorf("模板ID不能为空")
	}

	if template.Name == "" {
		return fmt.Errorf("模板名称不能为空")
	}

	if template.Type == "" {
		return fmt.Errorf("模板类型不能为空")
	}

	if template.Content == nil {
		return fmt.Errorf("模板内容不能为空")
	}

	// 验证embed模板的特定结构
	if template.Type == "embed" {
		return l.validateEmbedTemplate(template)
	}

	return nil
}

// parseYAMLTemplate 解析YAML模板
func (l *YAMLTemplateLoader) parseYAMLTemplate(templateID string, data []byte, guildID ...string) (*types.Template, error) {
	var yamlTemplate YAMLTemplateDefinition
	if err := yaml.Unmarshal(data, &yamlTemplate); err != nil {
		return nil, fmt.Errorf("解析YAML模板失败: %w", err)
	}

	return l.convertYAMLToTemplate(templateID, yamlTemplate, guildID...)
}

// parseJSONTemplate 解析JSON模板
func (l *YAMLTemplateLoader) parseJSONTemplate(templateID string, data []byte, guildID ...string) (*types.Template, error) {
	var template types.Template
	if err := json.Unmarshal(data, &template); err != nil {
		return nil, fmt.Errorf("解析JSON模板失败: %w", err)
	}

	// 设置基础信息
	template.ID = templateID
	if len(guildID) > 0 && guildID[0] != "" {
		template.GuildID = &guildID[0]
	}

	return &template, nil
}

// convertYAMLToTemplate 将YAML模板转换为标准模板
func (l *YAMLTemplateLoader) convertYAMLToTemplate(templateID string, yamlTemplate YAMLTemplateDefinition, guildID ...string) (*types.Template, error) {
	// 创建基础模板结构
	template := &types.Template{
		ID:          templateID,
		Name:        fmt.Sprintf("Embed模板 - %s", templateID),
		Description: fmt.Sprintf("从YAML配置加载的%s平台embed模板", templateID),
		Type:        "embed",
		Category:    "message_forward",
		Version:     "1.0.0",
		IsActive:    true,
		AccessLevel: "global",
		Variables:   l.generateEmbedTemplateVariables(),
		Metadata: types.TemplateMetadata{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Author:    "system",
			Version:   "1.0.0",
			IsSystem:  true,
			Tags:      []string{"embed", "message_forward", templateID},
		},
	}

	// 设置公会ID
	if len(guildID) > 0 && guildID[0] != "" {
		template.GuildID = &guildID[0]
	}

	// 转换embed内容
	embedContent := map[string]interface{}{
		"embed": l.convertYAMLEmbedToMap(yamlTemplate.Embed),
	}

	template.Content = embedContent
	return template, nil
}

// convertYAMLEmbedToMap 将YAML embed转换为map
func (l *YAMLTemplateLoader) convertYAMLEmbedToMap(embed YAMLEmbedTemplate) map[string]interface{} {
	embedMap := make(map[string]interface{})

	// 基础字段
	if embed.Title != "" {
		embedMap["title"] = embed.Title
	}
	if embed.Description != "" {
		embedMap["description"] = embed.Description
	}
	if embed.URL != "" {
		embedMap["url"] = embed.URL
	}
	if embed.Color != 0 {
		embedMap["color"] = embed.Color
	}

	// 缩略图
	if embed.Thumbnail.URL != "" {
		embedMap["thumbnail"] = map[string]interface{}{
			"url": embed.Thumbnail.URL,
		}
	}

	// 字段 - 修复：使用[]interface{}而不是[]map[string]interface{}
	if len(embed.Fields) > 0 {
		fields := make([]interface{}, len(embed.Fields))
		for i, field := range embed.Fields {
			fields[i] = map[string]interface{}{
				"name":   field.Name,
				"value":  field.Value,
				"inline": field.Inline,
			}
		}
		embedMap["fields"] = fields
	}

	// 页脚
	if embed.Footer.Text != "" {
		embedMap["footer"] = map[string]interface{}{
			"text": embed.Footer.Text,
		}
	}

	// 时间戳
	if embed.Timestamp != "" {
		embedMap["timestamp"] = embed.Timestamp
	}

	return embedMap
}

// generateEmbedTemplateVariables 生成embed模板变量定义
func (l *YAMLTemplateLoader) generateEmbedTemplateVariables() map[string]types.TemplateVar {
	return map[string]types.TemplateVar{
		"title":         {Type: "string", Required: true, Description: "产品标题"},
		"url":           {Type: "string", Required: false, Description: "产品链接"},
		"price":         {Type: "string", Required: false, Description: "产品价格"},
		"productId":     {Type: "string", Required: false, Description: "产品ID"},
		"skuId":         {Type: "string", Required: false, Description: "SKU ID"},
		"stock":         {Type: "string", Required: false, Description: "库存数量"},
		"atcLink":       {Type: "string", Required: false, Description: "购买链接"},
		"author_name":   {Type: "string", Required: false, Description: "作者名称"},
		"timestamp":     {Type: "string", Required: false, Description: "时间戳"},
		"thumbnail_url": {Type: "string", Required: false, Description: "缩略图URL"},
		"dynamic_color": {Type: "string", Required: false, Description: "动态颜色"},
	}
}

// validateEmbedTemplate 验证embed模板
func (l *YAMLTemplateLoader) validateEmbedTemplate(template *types.Template) error {
	content, ok := template.Content.(map[string]interface{})
	if !ok {
		return fmt.Errorf("embed模板内容格式错误")
	}

	embed, exists := content["embed"]
	if !exists {
		return fmt.Errorf("embed模板缺少embed字段")
	}

	embedMap, ok := embed.(map[string]interface{})
	if !ok {
		return fmt.Errorf("embed字段格式错误")
	}

	// 验证必需字段
	if _, exists := embedMap["title"]; !exists {
		return fmt.Errorf("embed模板缺少title字段")
	}

	return nil
}

// getTemplateFilePath 获取模板文件路径
func (l *YAMLTemplateLoader) getTemplateFilePath(templateID string, guildID ...string) string {
	// 优先查找公会特定模板
	if len(guildID) > 0 && guildID[0] != "" {
		guildPath := filepath.Join(l.templatesPath, "guilds", guildID[0], templateID+".yaml")
		if _, err := os.Stat(guildPath); err == nil {
			return guildPath
		}
	}

	// 查找全局模板
	return filepath.Join(l.templatesPath, templateID+".yaml")
}

// loadTemplateIndex 加载模板索引
func (l *YAMLTemplateLoader) loadTemplateIndex() error {
	indexPath := filepath.Join(l.templatesPath, "index.json")

	data, err := os.ReadFile(indexPath)
	if err != nil {
		return fmt.Errorf("读取模板索引失败: %w", err)
	}

	var index types.TemplateIndex
	if err := json.Unmarshal(data, &index); err != nil {
		return fmt.Errorf("解析模板索引失败: %w", err)
	}

	l.templateIndex = &index
	return nil
}

// scanAndLoadTemplates 扫描目录并加载模板
func (l *YAMLTemplateLoader) scanAndLoadTemplates() (map[string]*types.Template, error) {
	templates := make(map[string]*types.Template)

	// 扫描全局模板目录
	err := filepath.Walk(l.templatesPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		// 只处理YAML和JSON文件
		if !strings.HasSuffix(path, ".yaml") && !strings.HasSuffix(path, ".yml") && !strings.HasSuffix(path, ".json") {
			return nil
		}

		// 提取模板ID
		templateID := strings.TrimSuffix(info.Name(), filepath.Ext(info.Name()))

		template, err := l.LoadTemplate(templateID)
		if err != nil {
			logger.Error("扫描加载模板失败", "template_id", templateID, "path", path, "error", err)
			return nil
		}

		templates[templateID] = template
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("扫描模板目录失败: %w", err)
	}

	return templates, nil
}
