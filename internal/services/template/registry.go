package template

import (
	"fmt"
	"strings"
	"sync"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"
)

// DefaultAdapterRegistry 默认适配器注册表实现
type DefaultAdapterRegistry struct {
	adapters map[string]PlatformAdapter
	mu       sync.RWMutex
}

// NewDefaultAdapterRegistry 创建新的默认适配器注册表
func NewDefaultAdapterRegistry() *DefaultAdapterRegistry {
	registry := &DefaultAdapterRegistry{
		adapters: make(map[string]PlatformAdapter),
	}

	// 注册默认适配器
	registry.registerDefaultAdapters()

	return registry
}

// RegisterAdapter 注册适配器
func (r *DefaultAdapterRegistry) RegisterAdapter(adapter PlatformAdapter) error {
	if adapter == nil {
		return fmt.Errorf("适配器不能为空")
	}

	platform := strings.ToLower(adapter.GetPlatformName())
	if platform == "" {
		return fmt.Errorf("平台名称不能为空")
	}

	r.mu.Lock()
	defer r.mu.Unlock()

	r.adapters[platform] = adapter

	logger.Info("平台适配器已注册", "platform", platform, "template_id", adapter.GetTemplateID())
	return nil
}

// GetAdapter 获取适配器
func (r *DefaultAdapterRegistry) GetAdapter(platform string) (PlatformAdapter, error) {
	platform = strings.ToLower(platform)

	r.mu.RLock()
	defer r.mu.RUnlock()

	adapter, exists := r.adapters[platform]
	if !exists {
		return nil, fmt.Errorf("未找到平台适配器: %s", platform)
	}

	return adapter, nil
}

// GetAdapterForProduct 根据产品获取适配器
func (r *DefaultAdapterRegistry) GetAdapterForProduct(product *types.ProductItem) (PlatformAdapter, error) {
	if product == nil {
		return nil, fmt.Errorf("产品信息不能为空")
	}

	r.mu.RLock()
	defer r.mu.RUnlock()

	// 优先使用明确的平台字段
	if product.Platform != "" {
		platform := strings.ToLower(product.Platform)
		if adapter, exists := r.adapters[platform]; exists {
			if adapter.SupportsProduct(product) {
				return adapter, nil
			}
		}
	}

	// 遍历所有适配器，找到支持该产品的适配器
	for _, adapter := range r.adapters {
		if adapter.SupportsProduct(product) {
			return adapter, nil
		}
	}

	// 返回默认适配器
	if defaultAdapter, exists := r.adapters["default"]; exists {
		return defaultAdapter, nil
	}

	return nil, fmt.Errorf("未找到支持该产品的适配器")
}

// ListAdapters 列出所有适配器
func (r *DefaultAdapterRegistry) ListAdapters() []PlatformAdapter {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var adapters []PlatformAdapter
	for _, adapter := range r.adapters {
		adapters = append(adapters, adapter)
	}

	return adapters
}

// registerDefaultAdapters 注册默认适配器
func (r *DefaultAdapterRegistry) registerDefaultAdapters() {
	// 注册通用适配器
	r.adapters["default"] = NewUniversalAdapter()

	// 注册平台特定适配器
	r.adapters["amazon"] = NewAmazonAdapter()
	r.adapters["popmart"] = NewPopMartAdapter()
	r.adapters["aliexpress"] = NewAliExpressAdapter()
	r.adapters["amazon-wishlist"] = NewAmazonWishlistAdapter()

	logger.Info("默认平台适配器已注册", "count", len(r.adapters))
}

// UniversalAdapter 通用适配器（默认适配器）
type UniversalAdapter struct{}

func NewUniversalAdapter() *UniversalAdapter {
	return &UniversalAdapter{}
}

func (a *UniversalAdapter) GetPlatformName() string {
	return "default"
}

func (a *UniversalAdapter) SupportsProduct(product *types.ProductItem) bool {
	return true // 通用适配器支持所有产品
}

func (a *UniversalAdapter) ToTemplateVariables(product *types.ProductItem) map[string]interface{} {
	// 使用现有的ProductItemAdapter逻辑
	adapter := NewProductItemAdapter()
	return adapter.ToTemplateVariables(product)
}

func (a *UniversalAdapter) GetTemplateID() string {
	return "default"
}

func (a *UniversalAdapter) ValidateProduct(product *types.ProductItem) error {
	if product == nil {
		return fmt.Errorf("产品信息不能为空")
	}
	if product.Title == "" {
		return fmt.Errorf("产品标题不能为空")
	}
	return nil
}

// AmazonAdapter Amazon适配器
type AmazonAdapter struct{}

func NewAmazonAdapter() *AmazonAdapter {
	return &AmazonAdapter{}
}

func (a *AmazonAdapter) GetPlatformName() string {
	return "amazon"
}

func (a *AmazonAdapter) SupportsProduct(product *types.ProductItem) bool {
	if product == nil {
		return false
	}

	// 检查平台字段
	if strings.ToLower(product.Platform) == "amazon" {
		return true
	}

	// 检查URL
	if strings.Contains(strings.ToLower(product.URL), "amazon.") {
		return !strings.Contains(strings.ToLower(product.URL), "wishlist")
	}

	return false
}

func (a *AmazonAdapter) ToTemplateVariables(product *types.ProductItem) map[string]interface{} {
	adapter := NewProductItemAdapter()
	return adapter.ToTemplateVariables(product)
}

func (a *AmazonAdapter) GetTemplateID() string {
	return "amazon"
}

func (a *AmazonAdapter) ValidateProduct(product *types.ProductItem) error {
	if product == nil {
		return fmt.Errorf("产品信息不能为空")
	}
	if product.Title == "" {
		return fmt.Errorf("产品标题不能为空")
	}
	if product.ProductID == "" {
		return fmt.Errorf("Amazon产品必须有ProductID")
	}
	return nil
}

// PopMartAdapter PopMart适配器
type PopMartAdapter struct{}

func NewPopMartAdapter() *PopMartAdapter {
	return &PopMartAdapter{}
}

func (a *PopMartAdapter) GetPlatformName() string {
	return "popmart"
}

func (a *PopMartAdapter) SupportsProduct(product *types.ProductItem) bool {
	if product == nil {
		return false
	}

	platform := strings.ToLower(product.Platform)
	return platform == "popmart" || platform == "pop mart" ||
		strings.Contains(strings.ToLower(product.URL), "popmart.")
}

func (a *PopMartAdapter) ToTemplateVariables(product *types.ProductItem) map[string]interface{} {
	adapter := NewProductItemAdapter()
	return adapter.ToTemplateVariables(product)
}

func (a *PopMartAdapter) GetTemplateID() string {
	return "popmart"
}

func (a *PopMartAdapter) ValidateProduct(product *types.ProductItem) error {
	if product == nil {
		return fmt.Errorf("产品信息不能为空")
	}
	if product.Title == "" {
		return fmt.Errorf("产品标题不能为空")
	}
	return nil
}

// AliExpressAdapter AliExpress适配器
type AliExpressAdapter struct{}

func NewAliExpressAdapter() *AliExpressAdapter {
	return &AliExpressAdapter{}
}

func (a *AliExpressAdapter) GetPlatformName() string {
	return "aliexpress"
}

func (a *AliExpressAdapter) SupportsProduct(product *types.ProductItem) bool {
	if product == nil {
		return false
	}

	return strings.ToLower(product.Platform) == "aliexpress" ||
		strings.Contains(strings.ToLower(product.URL), "aliexpress.")
}

func (a *AliExpressAdapter) ToTemplateVariables(product *types.ProductItem) map[string]interface{} {
	adapter := NewProductItemAdapter()
	return adapter.ToTemplateVariables(product)
}

func (a *AliExpressAdapter) GetTemplateID() string {
	return "aliexpress"
}

func (a *AliExpressAdapter) ValidateProduct(product *types.ProductItem) error {
	if product == nil {
		return fmt.Errorf("产品信息不能为空")
	}
	if product.Title == "" {
		return fmt.Errorf("产品标题不能为空")
	}
	if product.ProductID == "" {
		return fmt.Errorf("AliExpress产品必须有ProductID")
	}
	return nil
}

// AmazonWishlistAdapter Amazon Wishlist适配器
type AmazonWishlistAdapter struct{}

func NewAmazonWishlistAdapter() *AmazonWishlistAdapter {
	return &AmazonWishlistAdapter{}
}

func (a *AmazonWishlistAdapter) GetPlatformName() string {
	return "amazon-wishlist"
}

func (a *AmazonWishlistAdapter) SupportsProduct(product *types.ProductItem) bool {
	if product == nil {
		return false
	}

	return strings.ToLower(product.Platform) == "amazon-wishlist" ||
		(strings.Contains(strings.ToLower(product.URL), "amazon.") &&
			(strings.Contains(strings.ToLower(product.URL), "wishlist") ||
				strings.Contains(strings.ToLower(product.URL), "registry")))
}

func (a *AmazonWishlistAdapter) ToTemplateVariables(product *types.ProductItem) map[string]interface{} {
	adapter := NewProductItemAdapter()
	return adapter.ToTemplateVariables(product)
}

func (a *AmazonWishlistAdapter) GetTemplateID() string {
	return "amazon-wishlist"
}

func (a *AmazonWishlistAdapter) ValidateProduct(product *types.ProductItem) error {
	if product == nil {
		return fmt.Errorf("产品信息不能为空")
	}
	if product.Title == "" {
		return fmt.Errorf("产品标题不能为空")
	}
	return nil
}
