package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"runtime"
	"strings"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/redis/go-redis/v9"
)

// RedisCache Redis 缓存服务实现
type RedisCache struct {
	client *redis.Client
	config types.RedisConfig
	prefix string
}

// NewRedisCache 创建新的 Redis 缓存服务
func NewRedisCache(config types.RedisConfig) (*RedisCache, error) {
	if !config.IsEnabled() {
		return nil, fmt.Errorf("Redis 配置未启用")
	}

	// 创建优化的 Redis 客户端配置
	options := &redis.Options{
		Addr:     config.GetRedisAddr(),
		Password: config.Password,
		DB:       config.DB,

		// 优化的连接池配置
		PoolSize:     getOptimalPoolSize(),
		MinIdleConns: getOptimalMinIdleConns(),
		MaxRetries:   3,

		// 优化的超时配置
		DialTimeout:  10 * time.Second, // 增加连接超时
		ReadTimeout:  5 * time.Second,  // 增加读取超时
		WriteTimeout: 5 * time.Second,  // 增加写入超时

		// 优化的连接管理
		PoolTimeout:     30 * time.Second, // 增加池超时
		ConnMaxIdleTime: 10 * time.Minute, // 增加空闲时间
		ConnMaxLifetime: 30 * time.Minute, // 添加连接最大生命周期

		// 启用连接监控
		OnConnect: func(ctx context.Context, cn *redis.Conn) error {
			logger.Debug("Redis连接建立", "db", config.DB)
			return nil
		},
	}

	rdb := redis.NewClient(options)

	cache := &RedisCache{
		client: rdb,
		config: config,
		prefix: config.Prefix,
	}

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := cache.Ping(ctx); err != nil {
		return nil, fmt.Errorf("Redis 连接测试失败: %w", err)
	}

	logger.Info("Redis 缓存服务初始化成功",
		"addr", config.GetRedisAddr(),
		"db", config.DB,
		"prefix", config.Prefix)

	return cache, nil
}

// addPrefix 添加键前缀
func (r *RedisCache) addPrefix(key string) string {
	if r.prefix == "" {
		return key
	}
	return r.prefix + key
}

// Get 获取值
func (r *RedisCache) Get(ctx context.Context, key string) (string, error) {
	result, err := r.client.Get(ctx, r.addPrefix(key)).Result()
	if err == redis.Nil {
		return "", nil // 键不存在
	}
	return result, err
}

// Set 设置值
func (r *RedisCache) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	var data string

	switch v := value.(type) {
	case string:
		data = v
	case []byte:
		data = string(v)
	default:
		// 序列化为 JSON
		bytes, err := json.Marshal(value)
		if err != nil {
			return fmt.Errorf("序列化值失败: %w", err)
		}
		data = string(bytes)
	}

	return r.client.Set(ctx, r.addPrefix(key), data, expiration).Err()
}

// Del 删除键
func (r *RedisCache) Del(ctx context.Context, keys ...string) error {
	if len(keys) == 0 {
		return nil
	}

	prefixedKeys := make([]string, len(keys))
	for i, key := range keys {
		prefixedKeys[i] = r.addPrefix(key)
	}

	return r.client.Del(ctx, prefixedKeys...).Err()
}

// Exists 检查键是否存在
func (r *RedisCache) Exists(ctx context.Context, keys ...string) (int64, error) {
	if len(keys) == 0 {
		return 0, nil
	}

	prefixedKeys := make([]string, len(keys))
	for i, key := range keys {
		prefixedKeys[i] = r.addPrefix(key)
	}

	return r.client.Exists(ctx, prefixedKeys...).Result()
}

// HGet 获取哈希字段值
func (r *RedisCache) HGet(ctx context.Context, key, field string) (string, error) {
	result, err := r.client.HGet(ctx, r.addPrefix(key), field).Result()
	if err == redis.Nil {
		return "", nil
	}
	return result, err
}

// HSet 设置哈希字段值
func (r *RedisCache) HSet(ctx context.Context, key string, values ...interface{}) error {
	return r.client.HSet(ctx, r.addPrefix(key), values...).Err()
}

// HGetAll 获取哈希所有字段
func (r *RedisCache) HGetAll(ctx context.Context, key string) (map[string]string, error) {
	return r.client.HGetAll(ctx, r.addPrefix(key)).Result()
}

// HDel 删除哈希字段
func (r *RedisCache) HDel(ctx context.Context, key string, fields ...string) error {
	if len(fields) == 0 {
		return nil
	}
	return r.client.HDel(ctx, r.addPrefix(key), fields...).Err()
}

// LPush 从左侧推入列表
func (r *RedisCache) LPush(ctx context.Context, key string, values ...interface{}) error {
	return r.client.LPush(ctx, r.addPrefix(key), values...).Err()
}

// RPush 从右侧推入列表
func (r *RedisCache) RPush(ctx context.Context, key string, values ...interface{}) error {
	return r.client.RPush(ctx, r.addPrefix(key), values...).Err()
}

// LPop 从左侧弹出列表元素
func (r *RedisCache) LPop(ctx context.Context, key string) (string, error) {
	result, err := r.client.LPop(ctx, r.addPrefix(key)).Result()
	if err == redis.Nil {
		return "", nil
	}
	return result, err
}

// RPop 从右侧弹出列表元素
func (r *RedisCache) RPop(ctx context.Context, key string) (string, error) {
	result, err := r.client.RPop(ctx, r.addPrefix(key)).Result()
	if err == redis.Nil {
		return "", nil
	}
	return result, err
}

// LLen 获取列表长度
func (r *RedisCache) LLen(ctx context.Context, key string) (int64, error) {
	return r.client.LLen(ctx, r.addPrefix(key)).Result()
}

// SAdd 添加集合成员
func (r *RedisCache) SAdd(ctx context.Context, key string, members ...interface{}) error {
	return r.client.SAdd(ctx, r.addPrefix(key), members...).Err()
}

// SRem 删除集合成员
func (r *RedisCache) SRem(ctx context.Context, key string, members ...interface{}) error {
	return r.client.SRem(ctx, r.addPrefix(key), members...).Err()
}

// SMembers 获取集合所有成员
func (r *RedisCache) SMembers(ctx context.Context, key string) ([]string, error) {
	return r.client.SMembers(ctx, r.addPrefix(key)).Result()
}

// SIsMember 检查是否为集合成员
func (r *RedisCache) SIsMember(ctx context.Context, key string, member interface{}) (bool, error) {
	return r.client.SIsMember(ctx, r.addPrefix(key), member).Result()
}

// ZAdd 添加有序集合成员
func (r *RedisCache) ZAdd(ctx context.Context, key string, members ...interface{}) error {
	// 转换为 Redis Z 结构
	var zMembers []redis.Z
	for i := 0; i < len(members); i += 2 {
		if i+1 < len(members) {
			score, ok := members[i].(float64)
			if !ok {
				return fmt.Errorf("分数必须是 float64 类型")
			}
			zMembers = append(zMembers, redis.Z{
				Score:  score,
				Member: members[i+1],
			})
		}
	}

	return r.client.ZAdd(ctx, r.addPrefix(key), zMembers...).Err()
}

// ZRem 删除有序集合成员
func (r *RedisCache) ZRem(ctx context.Context, key string, members ...interface{}) error {
	return r.client.ZRem(ctx, r.addPrefix(key), members...).Err()
}

// ZRange 获取有序集合范围
func (r *RedisCache) ZRange(ctx context.Context, key string, start, stop int64) ([]string, error) {
	return r.client.ZRange(ctx, r.addPrefix(key), start, stop).Result()
}

// ZScore 获取有序集合成员分数
func (r *RedisCache) ZScore(ctx context.Context, key, member string) (float64, error) {
	result, err := r.client.ZScore(ctx, r.addPrefix(key), member).Result()
	if err == redis.Nil {
		return 0, nil
	}
	return result, err
}

// Expire 设置键过期时间
func (r *RedisCache) Expire(ctx context.Context, key string, expiration time.Duration) error {
	return r.client.Expire(ctx, r.addPrefix(key), expiration).Err()
}

// TTL 获取键剩余生存时间
func (r *RedisCache) TTL(ctx context.Context, key string) (time.Duration, error) {
	return r.client.TTL(ctx, r.addPrefix(key)).Result()
}

// Ping 测试连接
func (r *RedisCache) Ping(ctx context.Context) error {
	return r.client.Ping(ctx).Err()
}

// Close 关闭连接
func (r *RedisCache) Close() error {
	logger.Info("正在关闭 Redis 连接...")
	return r.client.Close()
}

// GetJSON 获取并反序列化 JSON 值
func (r *RedisCache) GetJSON(ctx context.Context, key string, dest interface{}) error {
	data, err := r.Get(ctx, key)
	if err != nil {
		return err
	}

	if data == "" {
		return nil // 键不存在
	}

	return json.Unmarshal([]byte(data), dest)
}

// SetJSON 序列化并设置 JSON 值
func (r *RedisCache) SetJSON(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("序列化 JSON 失败: %w", err)
	}

	return r.Set(ctx, key, string(data), expiration)
}

// Incr 递增
func (r *RedisCache) Incr(ctx context.Context, key string) (int64, error) {
	return r.client.Incr(ctx, r.addPrefix(key)).Result()
}

// Decr 递减
func (r *RedisCache) Decr(ctx context.Context, key string) (int64, error) {
	return r.client.Decr(ctx, r.addPrefix(key)).Result()
}

// IncrBy 按指定值递增
func (r *RedisCache) IncrBy(ctx context.Context, key string, value int64) (int64, error) {
	return r.client.IncrBy(ctx, r.addPrefix(key), value).Result()
}

// DecrBy 按指定值递减
func (r *RedisCache) DecrBy(ctx context.Context, key string, value int64) (int64, error) {
	return r.client.DecrBy(ctx, r.addPrefix(key), value).Result()
}

// GetStats 获取 Redis 统计信息
func (r *RedisCache) GetStats(ctx context.Context) (map[string]string, error) {
	info, err := r.client.Info(ctx).Result()
	if err != nil {
		return nil, err
	}

	// 解析 INFO 命令输出
	stats := make(map[string]string)
	lines := strings.Split(info, "\r\n")

	for _, line := range lines {
		if strings.Contains(line, ":") && !strings.HasPrefix(line, "#") {
			parts := strings.SplitN(line, ":", 2)
			if len(parts) == 2 {
				stats[parts[0]] = parts[1]
			}
		}
	}

	return stats, nil
}

// getOptimalPoolSize 获取最优连接池大小
func getOptimalPoolSize() int {
	// 基于CPU核心数动态计算连接池大小
	// 一般建议是 CPU核心数 * 2 到 CPU核心数 * 4
	cpuCount := runtime.NumCPU()
	poolSize := cpuCount * 3

	// 设置合理的范围
	if poolSize < 10 {
		poolSize = 10
	}
	if poolSize > 50 {
		poolSize = 50
	}

	return poolSize
}

// getOptimalMinIdleConns 获取最优最小空闲连接数
func getOptimalMinIdleConns() int {
	// 最小空闲连接数通常是连接池大小的 1/3 到 1/2
	poolSize := getOptimalPoolSize()
	minIdle := poolSize / 3

	// 设置合理的范围
	if minIdle < 5 {
		minIdle = 5
	}
	if minIdle > 20 {
		minIdle = 20
	}

	return minIdle
}

// GetPoolStats 获取连接池统计信息
func (r *RedisCache) GetPoolStats() *redis.PoolStats {
	if r.client == nil {
		return nil
	}
	return r.client.PoolStats()
}

// MonitorPool 监控连接池状态
func (r *RedisCache) MonitorPool(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			stats := r.GetPoolStats()
			if stats != nil {
				logger.Debug("Redis连接池状态",
					"hits", stats.Hits,
					"misses", stats.Misses,
					"timeouts", stats.Timeouts,
					"total_conns", stats.TotalConns,
					"idle_conns", stats.IdleConns,
					"stale_conns", stats.StaleConns)

				// 检查连接池健康状态
				if stats.Timeouts > 0 {
					logger.Warn("Redis连接池出现超时", "timeouts", stats.Timeouts)
				}

				// 检查连接池利用率
				if stats.TotalConns > 0 {
					utilization := float64(stats.TotalConns-stats.IdleConns) / float64(stats.TotalConns) * 100
					if utilization > 80 {
						logger.Warn("Redis连接池利用率过高", "utilization", utilization)
					}
				}
			}
		}
	}
}
