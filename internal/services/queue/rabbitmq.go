package queue

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	amqp "github.com/rabbitmq/amqp091-go"
)

// ConnectionPool 连接池
type ConnectionPool struct {
	connections []*amqp.Connection
	channels    []*amqp.Channel
	mu          sync.RWMutex
	size        int
	current     int
}

// HealthChecker 健康检查器
type HealthChecker struct {
	service  *RabbitMQService
	interval time.Duration
	stopCh   chan struct{}
}

// RabbitMQService RabbitMQ 队列服务实现
type RabbitMQService struct {
	config     types.QueueConfig
	connection *amqp.Connection
	channel    *amqp.Channel

	// 连接池
	pool *ConnectionPool

	// 健康检查
	healthChecker *HealthChecker

	// 任务处理器
	taskHandlers map[string]types.TaskHandler
	handlerMu    sync.RWMutex

	// 统计信息
	stats   *types.QueueStats
	statsMu sync.RWMutex

	// 状态管理
	isConnected bool
	connMu      sync.RWMutex

	// 重连管理
	reconnectAttempts    int
	maxReconnectAttempts int

	// 上下文
	ctx    context.Context
	cancel context.CancelFunc
}

// NewRabbitMQService 创建新的 RabbitMQ 服务
func NewRabbitMQService(config types.QueueConfig) (*RabbitMQService, error) {
	if !config.IsEnabled() {
		return nil, fmt.Errorf("队列配置未启用")
	}

	service := &RabbitMQService{
		config:               config,
		taskHandlers:         make(map[string]types.TaskHandler),
		stats:                &types.QueueStats{},
		maxReconnectAttempts: 10,
	}

	return service, nil
}

// Connect 连接到 RabbitMQ
func (r *RabbitMQService) Connect(ctx context.Context) error {
	r.ctx, r.cancel = context.WithCancel(ctx)

	logger.Info("正在连接到 RabbitMQ...", "url", r.config.URL)

	// 强制IPv4连接：替换localhost为127.0.0.1
	connectionURL := r.config.URL
	if strings.Contains(connectionURL, "localhost") {
		connectionURL = strings.Replace(connectionURL, "localhost", "127.0.0.1", 1)
		logger.Debug("强制IPv4连接", "original_url", r.config.URL, "modified_url", connectionURL)
	}

	// 建立连接
	conn, err := amqp.Dial(connectionURL)
	if err != nil {
		return fmt.Errorf("连接 RabbitMQ 失败: %w", err)
	}

	// 创建通道
	ch, err := conn.Channel()
	if err != nil {
		conn.Close()
		return fmt.Errorf("创建通道失败: %w", err)
	}

	// 设置 QoS
	if err := ch.Qos(r.config.PrefetchCount, 0, false); err != nil {
		ch.Close()
		conn.Close()
		return fmt.Errorf("设置 QoS 失败: %w", err)
	}

	r.connection = conn
	r.channel = ch

	r.connMu.Lock()
	r.isConnected = true
	r.reconnectAttempts = 0
	r.connMu.Unlock()

	// 声明默认交换机
	if err := r.declareDefaultExchange(); err != nil {
		return fmt.Errorf("声明默认交换机失败: %w", err)
	}

	// 监听连接关闭
	go r.watchConnection()

	logger.Info("RabbitMQ 连接建立成功")
	return nil
}

// declareDefaultExchange 声明默认交换机
func (r *RabbitMQService) declareDefaultExchange() error {
	exchange := r.config.DefaultExchange

	return r.channel.ExchangeDeclare(
		exchange.Name,
		exchange.Type,
		exchange.Options.Durable,
		exchange.Options.AutoDelete,
		false, // internal
		false, // no-wait
		nil,   // arguments
	)
}

// watchConnection 监听连接状态
func (r *RabbitMQService) watchConnection() {
	closeChan := make(chan *amqp.Error)
	r.connection.NotifyClose(closeChan)

	select {
	case err := <-closeChan:
		if err != nil {
			logger.Warn("RabbitMQ 连接断开", "error", err)

			r.connMu.Lock()
			r.isConnected = false
			r.connMu.Unlock()

			// 尝试重连
			go r.attemptReconnect()
		}
	case <-r.ctx.Done():
		return
	}
}

// attemptReconnect 尝试重连
func (r *RabbitMQService) attemptReconnect() {
	r.connMu.Lock()
	if r.reconnectAttempts >= r.maxReconnectAttempts {
		r.connMu.Unlock()
		logger.Error("达到最大重连次数，停止重连", "attempts", r.reconnectAttempts)
		return
	}

	r.reconnectAttempts++
	attempts := r.reconnectAttempts
	r.connMu.Unlock()

	logger.Info("尝试重连 RabbitMQ", "attempt", attempts, "max", r.maxReconnectAttempts)

	// 指数退避延迟
	delay := r.config.ReconnectDelay * time.Duration(attempts)
	if delay > 60*time.Second {
		delay = 60 * time.Second // 最大延迟60秒
	}

	logger.Debug("等待重连延迟", "delay", delay, "attempt", attempts)
	select {
	case <-time.After(delay):
	case <-r.ctx.Done():
		logger.Info("重连被取消")
		return
	}

	// 尝试重新连接
	if err := r.Connect(r.ctx); err != nil {
		logger.Error("重连失败", "error", err, "attempt", attempts)
		// 递归尝试下一次重连
		go r.attemptReconnect()
	} else {
		logger.Info("✅ RabbitMQ 重连成功", "attempts", attempts)
	}
}

// Close 关闭连接
func (r *RabbitMQService) Close() error {
	logger.Info("正在关闭 RabbitMQ 连接...")

	if r.cancel != nil {
		r.cancel()
	}

	r.connMu.Lock()
	r.isConnected = false
	r.connMu.Unlock()

	var err error
	if r.channel != nil {
		if closeErr := r.channel.Close(); closeErr != nil {
			err = closeErr
		}
	}

	if r.connection != nil {
		if closeErr := r.connection.Close(); closeErr != nil {
			err = closeErr
		}
	}

	logger.Info("RabbitMQ 连接已关闭")
	return err
}

// IsConnected 检查是否已连接
func (r *RabbitMQService) IsConnected() bool {
	r.connMu.RLock()
	defer r.connMu.RUnlock()
	return r.isConnected
}

// DeclareQueue 声明队列
func (r *RabbitMQService) DeclareQueue(ctx context.Context, name string, options types.QueueOptions) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	_, err := r.channel.QueueDeclare(
		name,
		options.Durable,
		options.AutoDelete,
		options.Exclusive,
		options.NoWait,
		amqp.Table(options.Arguments),
	)

	if err == nil {
		logger.Debug("队列声明成功", "name", name)
	}

	return err
}

// DeleteQueue 删除队列
func (r *RabbitMQService) DeleteQueue(ctx context.Context, name string, options types.DeleteQueueOptions) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	_, err := r.channel.QueueDelete(
		name,
		options.IfUnused,
		options.IfEmpty,
		options.NoWait,
	)

	if err == nil {
		logger.Debug("队列删除成功", "name", name)
	}

	return err
}

// DeclareExchange 声明交换机
func (r *RabbitMQService) DeclareExchange(ctx context.Context, name, kind string, options types.ExchangeOptions) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	err := r.channel.ExchangeDeclare(
		name,
		kind,
		options.Durable,
		options.AutoDelete,
		false, // internal
		false, // no-wait
		nil,   // arguments
	)

	if err == nil {
		logger.Debug("交换机声明成功", "name", name, "type", kind)
	}

	return err
}

// DeclareDelayExchange 声明延迟交换机（带参数）
func (r *RabbitMQService) DeclareDelayExchange(ctx context.Context, name string, arguments amqp.Table) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	err := r.channel.ExchangeDeclare(
		name,
		"x-delayed-message",
		true,  // durable
		false, // auto-delete
		false, // internal
		false, // no-wait
		arguments,
	)

	if err == nil {
		logger.Debug("延迟交换机声明成功", "name", name)
	}

	return err
}

// DeleteExchange 删除交换机
func (r *RabbitMQService) DeleteExchange(ctx context.Context, name string, options types.DeleteExchangeOptions) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	err := r.channel.ExchangeDelete(
		name,
		options.IfUnused,
		options.NoWait,
	)

	if err == nil {
		logger.Debug("交换机删除成功", "name", name)
	}

	return err
}

// BindQueue 绑定队列到交换机
func (r *RabbitMQService) BindQueue(ctx context.Context, queueName, exchangeName, routingKey string) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	err := r.channel.QueueBind(
		queueName,
		routingKey,
		exchangeName,
		false, // no-wait
		nil,   // arguments
	)

	if err == nil {
		logger.Debug("队列绑定成功", "queue", queueName, "exchange", exchangeName, "routing_key", routingKey)
	}

	return err
}

// UnbindQueue 解绑队列
func (r *RabbitMQService) UnbindQueue(ctx context.Context, queueName, exchangeName, routingKey string) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	err := r.channel.QueueUnbind(
		queueName,
		routingKey,
		exchangeName,
		nil, // arguments
	)

	if err == nil {
		logger.Debug("队列解绑成功", "queue", queueName, "exchange", exchangeName, "routing_key", routingKey)
	}

	return err
}

// Publish 发布消息
func (r *RabbitMQService) Publish(ctx context.Context, exchange, routingKey string, message []byte, options types.PublishOptions) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	// 构建消息属性
	headers := amqp.Table{}
	if options.Headers != nil {
		for k, v := range options.Headers {
			headers[k] = v
		}
	}

	publishing := amqp.Publishing{
		Headers:     headers,
		ContentType: "application/octet-stream",
		Body:        message,
		Priority:    options.Priority,
		Timestamp:   time.Now(),
	}

	// 设置 TTL
	if options.TTL > 0 {
		publishing.Expiration = fmt.Sprintf("%d", options.TTL.Milliseconds())
	}

	err := r.channel.PublishWithContext(
		ctx,
		exchange,
		routingKey,
		options.Mandatory,
		options.Immediate,
		publishing,
	)

	if err == nil {
		r.statsMu.Lock()
		r.stats.Published++
		r.stats.LastUpdated = time.Now()
		r.statsMu.Unlock()

		logger.Debug("消息发布成功", "exchange", exchange, "routing_key", routingKey, "size", len(message))
	}

	return err
}

// PublishJSON 发布 JSON 消息
func (r *RabbitMQService) PublishJSON(ctx context.Context, exchange, routingKey string, message interface{}, options types.PublishOptions) error {
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化 JSON 失败: %w", err)
	}

	// 设置内容类型
	if options.Headers == nil {
		options.Headers = make(map[string]interface{})
	}
	options.Headers["content-type"] = "application/json"

	return r.Publish(ctx, exchange, routingKey, data, options)
}

// Consume 消费消息
func (r *RabbitMQService) Consume(ctx context.Context, queueName string, handler types.MessageHandler, options types.ConsumeOptions) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	msgs, err := r.channel.Consume(
		queueName,
		options.Consumer,
		options.AutoAck,
		options.Exclusive,
		options.NoLocal,
		options.NoWait,
		amqp.Table(options.Arguments),
	)
	if err != nil {
		return fmt.Errorf("开始消费失败: %w", err)
	}

	logger.Info("开始消费队列", "queue", queueName, "consumer", options.Consumer)

	// 处理消息
	go func() {
		for {
			select {
			case msg, ok := <-msgs:
				if !ok {
					logger.Warn("消息通道已关闭", "queue", queueName)
					return
				}

				// 处理消息
				r.handleMessage(ctx, msg, handler, options.AutoAck)

			case <-ctx.Done():
				logger.Info("停止消费队列", "queue", queueName)
				return
			}
		}
	}()

	return nil
}

// handleMessage 处理单个消息
func (r *RabbitMQService) handleMessage(ctx context.Context, delivery amqp.Delivery, handler types.MessageHandler, autoAck bool) {
	defer func() {
		if rec := recover(); rec != nil {
			logger.Error("消息处理 panic", "panic", rec)
			if !autoAck {
				delivery.Nack(false, true) // 重新入队
			}
		}
	}()

	// 构建消息对象
	message := &types.Message{
		ID:          delivery.MessageId,
		Body:        delivery.Body,
		Headers:     make(map[string]interface{}),
		ContentType: delivery.ContentType,
		Timestamp:   delivery.Timestamp,
		ReplyTo:     delivery.ReplyTo,
		Priority:    delivery.Priority,
	}

	// 转换头部
	for k, v := range delivery.Headers {
		message.Headers[k] = v
	}

	// 处理消息
	err := handler(ctx, message)

	if !autoAck {
		if err != nil {
			logger.Error("消息处理失败", "error", err, "message_id", delivery.MessageId)

			r.statsMu.Lock()
			r.stats.Failed++
			r.statsMu.Unlock()

			// 检查重试次数
			retryCount := r.getRetryCount(delivery.Headers)
			if retryCount < r.config.MaxRetries {
				// 更新重试计数并重新发布消息
				newRetryCount := retryCount + 1
				logger.Info("消息重新入队", "retry_count", newRetryCount, "message_id", delivery.MessageId)

				// 重新发布消息到队列，带有更新的重试计数
				if err := r.republishWithRetryCount(delivery, newRetryCount); err != nil {
					logger.Error("重新发布消息失败", "error", err, "message_id", delivery.MessageId)
					delivery.Nack(false, false) // 丢弃消息
				} else {
					delivery.Ack(false) // 确认原消息
					r.statsMu.Lock()
					r.stats.Requeued++
					r.statsMu.Unlock()
				}
			} else {
				logger.Error("消息达到最大重试次数，丢弃", "message_id", delivery.MessageId, "retry_count", retryCount)
				delivery.Nack(false, false) // 丢弃消息
			}
		} else {
			delivery.Ack(false) // 确认消息

			r.statsMu.Lock()
			r.stats.Consumed++
			r.stats.LastUpdated = time.Now()
			r.statsMu.Unlock()

			logger.Debug("消息处理成功", "message_id", delivery.MessageId)
		}
	}
}

// getRetryCount 获取重试次数
func (r *RabbitMQService) getRetryCount(headers amqp.Table) int {
	if headers == nil {
		return 0
	}

	if count, ok := headers["x-retry-count"]; ok {
		if retryCount, ok := count.(int); ok {
			return retryCount
		}
	}

	return 0
}

// republishWithRetryCount 重新发布消息并更新重试计数
func (r *RabbitMQService) republishWithRetryCount(delivery amqp.Delivery, retryCount int) error {
	// 创建新的headers，包含更新的重试计数
	headers := amqp.Table{}
	if delivery.Headers != nil {
		for k, v := range delivery.Headers {
			headers[k] = v
		}
	}
	headers["x-retry-count"] = retryCount

	// 重新发布消息
	publishing := amqp.Publishing{
		Headers:         headers,
		ContentType:     delivery.ContentType,
		ContentEncoding: delivery.ContentEncoding,
		Body:            delivery.Body,
		DeliveryMode:    delivery.DeliveryMode,
		Priority:        delivery.Priority,
		CorrelationId:   delivery.CorrelationId,
		ReplyTo:         delivery.ReplyTo,
		Expiration:      delivery.Expiration,
		MessageId:       delivery.MessageId,
		Timestamp:       delivery.Timestamp,
		Type:            delivery.Type,
		UserId:          delivery.UserId,
		AppId:           delivery.AppId,
	}

	return r.channel.PublishWithContext(
		context.Background(),
		delivery.Exchange,
		delivery.RoutingKey,
		false, // mandatory
		false, // immediate
		publishing,
	)
}

// PublishTask 发布任务
func (r *RabbitMQService) PublishTask(ctx context.Context, queueName, taskType string, data interface{}, options types.PublishOptions) (string, error) {
	// 首先确保队列存在
	queueOptions := types.QueueOptions{
		Durable:    true,  // 持久化队列
		AutoDelete: false, // 不自动删除
		Exclusive:  false, // 非独占
		NoWait:     false, // 等待确认
		Arguments:  nil,   // 无额外参数
	}

	if err := r.DeclareQueue(ctx, queueName, queueOptions); err != nil {
		logger.Error("声明队列失败", "queue", queueName, "error", err)
		return "", fmt.Errorf("声明队列失败: %w", err)
	}

	// 绑定队列到默认交换机
	exchangeName := r.config.DefaultExchange.Name
	routingKey := queueName // 使用队列名作为路由键

	if err := r.BindQueue(ctx, queueName, exchangeName, routingKey); err != nil {
		logger.Error("绑定队列到交换机失败", "queue", queueName, "exchange", exchangeName, "error", err)
		return "", fmt.Errorf("绑定队列到交换机失败: %w", err)
	}

	// 构建任务消息
	task := map[string]interface{}{
		"id":        fmt.Sprintf("task_%d", time.Now().UnixNano()),
		"type":      taskType,
		"data":      data,
		"timestamp": time.Now().Unix(),
	}

	// 发布任务到交换机（重用之前声明的变量）
	err := r.PublishJSON(ctx, exchangeName, routingKey, task, options)
	if err != nil {
		return "", err
	}

	logger.Debug("任务发布成功", "queue", queueName, "task_type", taskType, "task_id", task["id"])
	return task["id"].(string), nil
}

// PublishDelayedTask 发布延迟任务
func (r *RabbitMQService) PublishDelayedTask(ctx context.Context, queueName, taskType string, data interface{}, delay time.Duration, options types.PublishOptions) (string, error) {
	// 首先确保延迟交换机存在
	delayExchangeName := "zeka.delay"
	arguments := amqp.Table{
		"x-delayed-type": "direct",
	}

	if err := r.DeclareDelayExchange(ctx, delayExchangeName, arguments); err != nil {
		logger.Error("声明延迟交换机失败", "exchange", delayExchangeName, "error", err)
		return "", fmt.Errorf("声明延迟交换机失败: %w", err)
	}

	// 确保目标队列存在
	queueOptions := types.QueueOptions{
		Durable:    true,
		AutoDelete: false,
		Exclusive:  false,
		NoWait:     false,
		Arguments:  nil,
	}

	if err := r.DeclareQueue(ctx, queueName, queueOptions); err != nil {
		logger.Error("声明队列失败", "queue", queueName, "error", err)
		return "", fmt.Errorf("声明队列失败: %w", err)
	}

	// 绑定队列到延迟交换机
	routingKey := queueName
	if err := r.BindQueue(ctx, queueName, delayExchangeName, routingKey); err != nil {
		logger.Error("绑定队列到延迟交换机失败", "queue", queueName, "exchange", delayExchangeName, "error", err)
		return "", fmt.Errorf("绑定队列到延迟交换机失败: %w", err)
	}

	// 构建任务消息
	task := map[string]interface{}{
		"id":        fmt.Sprintf("delayed_task_%d", time.Now().UnixNano()),
		"type":      taskType,
		"data":      data,
		"timestamp": time.Now().Unix(),
		"delay":     delay.String(),
	}

	// 设置延迟头部
	if options.Headers == nil {
		options.Headers = make(map[string]interface{})
	}
	options.Headers["x-delay"] = delay.Milliseconds()

	// 发布延迟任务到延迟交换机
	err := r.PublishJSON(ctx, delayExchangeName, routingKey, task, options)
	if err != nil {
		return "", fmt.Errorf("发布延迟任务失败: %w", err)
	}

	logger.Debug("延迟任务发布成功", "queue", queueName, "task_type", taskType, "task_id", task["id"], "delay", delay)
	return task["id"].(string), nil
}

// RegisterTaskHandler 注册任务处理器
func (r *RabbitMQService) RegisterTaskHandler(taskType string, handler types.TaskHandler) {
	r.handlerMu.Lock()
	defer r.handlerMu.Unlock()

	r.taskHandlers[taskType] = handler
	logger.Debug("注册任务处理器", "type", taskType)
}

// StartTaskConsumer 启动任务消费者
func (r *RabbitMQService) StartTaskConsumer(ctx context.Context, queueName string, options types.ConsumeOptions) error {
	// 创建任务消息处理器
	taskHandler := func(ctx context.Context, message *types.Message) error {
		// 解析任务
		var task map[string]interface{}
		if err := json.Unmarshal(message.Body, &task); err != nil {
			return fmt.Errorf("解析任务失败: %w", err)
		}

		taskType, ok := task["type"].(string)
		if !ok {
			return fmt.Errorf("任务类型无效")
		}

		// 获取处理器
		r.handlerMu.RLock()
		handler, exists := r.taskHandlers[taskType]
		r.handlerMu.RUnlock()

		if !exists {
			return fmt.Errorf("未找到任务处理器: %s", taskType)
		}

		// 执行任务
		return handler(ctx, message.Body)
	}

	return r.Consume(ctx, queueName, taskHandler, options)
}

// GetQueueInfo 获取队列信息
func (r *RabbitMQService) GetQueueInfo(ctx context.Context, queueName string) (*types.QueueInfo, error) {
	if !r.IsConnected() {
		return nil, types.ErrQueueNotAvailable
	}

	queue, err := r.channel.QueueInspect(queueName)
	if err != nil {
		return nil, err
	}

	return &types.QueueInfo{
		Name:       queue.Name,
		Messages:   queue.Messages,
		Consumers:  queue.Consumers,
		Durable:    true, // 假设为持久化队列
		AutoDelete: false,
		Exclusive:  false,
		Arguments:  nil,
	}, nil
}

// GetStats 获取统计信息
func (r *RabbitMQService) GetStats() *types.QueueStats {
	r.statsMu.RLock()
	defer r.statsMu.RUnlock()

	// 返回副本
	return &types.QueueStats{
		Published:   r.stats.Published,
		Consumed:    r.stats.Consumed,
		Failed:      r.stats.Failed,
		Requeued:    r.stats.Requeued,
		LastUpdated: r.stats.LastUpdated,
	}
}

// GetHealthStatus 获取队列服务健康状态
func (r *RabbitMQService) GetHealthStatus() *types.QueueHealthStatus {
	r.connMu.RLock()
	connected := r.isConnected
	attempts := r.reconnectAttempts
	r.connMu.RUnlock()

	r.statsMu.RLock()
	stats := *r.stats
	r.statsMu.RUnlock()

	status := &types.QueueHealthStatus{
		Connected:         connected,
		URL:               r.config.URL,
		ReconnectAttempts: attempts,
		MaxRetries:        r.maxReconnectAttempts,
		Stats:             stats,
		LastCheck:         time.Now(),
	}

	if connected {
		status.Status = "healthy"
		status.Message = "RabbitMQ 连接正常"
	} else {
		status.Status = "unhealthy"
		if attempts >= r.maxReconnectAttempts {
			status.Message = "RabbitMQ 连接失败，已达到最大重连次数"
		} else {
			status.Message = fmt.Sprintf("RabbitMQ 连接失败，正在重连 (尝试 %d/%d)", attempts, r.maxReconnectAttempts)
		}
	}

	return status
}

// PublishBatch 批量发布消息
func (r *RabbitMQService) PublishBatch(ctx context.Context, exchange string, messages []types.BatchMessage) error {
	r.connMu.RLock()
	connected := r.isConnected
	r.connMu.RUnlock()

	if !connected {
		return fmt.Errorf("RabbitMQ 连接未建立")
	}

	ch := r.channel
	if ch == nil {
		return fmt.Errorf("RabbitMQ 频道未建立")
	}

	// 批量发布消息（不使用事务，简化实现）
	for _, msg := range messages {
		publishing := amqp.Publishing{
			ContentType:  "application/json",
			Body:         msg.Body,
			Priority:     msg.Options.Priority,
			DeliveryMode: amqp.Persistent,
			Timestamp:    time.Now(),
		}

		if msg.Options.TTL > 0 {
			publishing.Expiration = fmt.Sprintf("%d", msg.Options.TTL.Milliseconds())
		}

		if err := ch.PublishWithContext(ctx, exchange, msg.RoutingKey, false, false, publishing); err != nil {
			return fmt.Errorf("批量发布消息失败: %w", err)
		}
	}

	// 更新统计
	r.statsMu.Lock()
	r.stats.Published += int64(len(messages))
	r.statsMu.Unlock()

	logger.Info("批量发布消息成功", "count", len(messages), "exchange", exchange)
	return nil
}

// SetupDeadLetterQueue 设置死信队列
func (r *RabbitMQService) SetupDeadLetterQueue(queueName string) error {
	r.connMu.RLock()
	connected := r.isConnected
	r.connMu.RUnlock()

	if !connected {
		return fmt.Errorf("RabbitMQ 连接未建立")
	}

	ch := r.channel
	if ch == nil {
		return fmt.Errorf("RabbitMQ 频道未建立")
	}

	// 创建死信交换机
	dlxName := queueName + ".dlx"
	if err := ch.ExchangeDeclare(dlxName, "direct", true, false, false, false, nil); err != nil {
		return fmt.Errorf("创建死信交换机失败: %w", err)
	}

	// 创建死信队列
	dlqName := queueName + ".dlq"
	_, err := ch.QueueDeclare(dlqName, true, false, false, false, nil)
	if err != nil {
		return fmt.Errorf("创建死信队列失败: %w", err)
	}

	// 绑定死信队列到死信交换机
	if err := ch.QueueBind(dlqName, queueName, dlxName, false, nil); err != nil {
		return fmt.Errorf("绑定死信队列失败: %w", err)
	}

	logger.Info("死信队列设置完成", "queue", queueName, "dlq", dlqName)
	return nil
}

// PurgeQueue 清空队列
func (r *RabbitMQService) PurgeQueue(queueName string) (int, error) {
	r.connMu.RLock()
	connected := r.isConnected
	r.connMu.RUnlock()

	if !connected {
		return 0, fmt.Errorf("RabbitMQ 连接未建立")
	}

	ch := r.channel
	if ch == nil {
		return 0, fmt.Errorf("RabbitMQ 频道未建立")
	}

	count, err := ch.QueuePurge(queueName, false)
	if err != nil {
		return 0, fmt.Errorf("清空队列失败: %w", err)
	}

	logger.Info("队列已清空", "queue", queueName, "count", count)
	return count, nil
}

// 简化的处理器映射（用于兼容现有代码）
var simpleHandlers = make(map[string]func(data interface{}) error)
var simpleHandlersMu sync.RWMutex

// RegisterHandler 注册任务处理器（简化版本，兼容现有代码）
func (r *RabbitMQService) RegisterHandler(taskType string, handler func(data interface{}) error) {
	simpleHandlersMu.Lock()
	defer simpleHandlersMu.Unlock()

	simpleHandlers[taskType] = handler
	logger.Debug("注册简化任务处理器", "type", taskType)
}

// StartConsumer 启动队列消费者（简化版本，兼容现有代码）
func (r *RabbitMQService) StartConsumer(queueName string, options interface{}) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	logger.Info("启动简化队列消费者", "queue", queueName)

	// 首先声明队列（确保队列存在）
	queueOptions := types.QueueOptions{
		Durable:    true,  // 持久化队列
		AutoDelete: false, // 不自动删除
		Exclusive:  false, // 非独占
		NoWait:     false, // 等待确认
		Arguments:  nil,   // 无额外参数
	}

	if err := r.DeclareQueue(context.Background(), queueName, queueOptions); err != nil {
		logger.Error("声明队列失败", "queue", queueName, "error", err)
		return fmt.Errorf("声明队列失败: %w", err)
	}

	logger.Debug("队列声明成功", "queue", queueName)

	// 绑定队列到默认交换机
	exchangeName := r.config.DefaultExchange.Name
	routingKey := queueName // 使用队列名作为路由键

	if err := r.BindQueue(context.Background(), queueName, exchangeName, routingKey); err != nil {
		logger.Error("绑定队列到交换机失败", "queue", queueName, "exchange", exchangeName, "error", err)
		return fmt.Errorf("绑定队列到交换机失败: %w", err)
	}

	logger.Debug("队列绑定成功", "queue", queueName, "exchange", exchangeName, "routing_key", routingKey)

	// 使用默认的消费选项
	consumeOptions := types.ConsumeOptions{
		AutoAck:   false,
		Exclusive: false,
		NoLocal:   false,
		NoWait:    false,
	}

	// 创建消费者处理函数
	handler := func(ctx context.Context, message *types.Message) error {
		// 尝试解析消息
		var messageData map[string]interface{}
		if err := json.Unmarshal(message.Body, &messageData); err != nil {
			logger.Error("解析消息失败", "error", err)
			return err
		}

		// 获取任务类型
		taskType, ok := messageData["type"].(string)
		if !ok {
			taskType = queueName // 使用队列名作为默认任务类型
		}

		// 查找处理器
		simpleHandlersMu.RLock()
		taskHandler, exists := simpleHandlers[taskType]
		simpleHandlersMu.RUnlock()

		if !exists {
			logger.Warn("未找到简化任务处理器", "type", taskType)
			return fmt.Errorf("未找到任务处理器: %s", taskType)
		}

		// 执行处理器
		return taskHandler(messageData)
	}

	return r.Consume(context.Background(), queueName, handler, consumeOptions)
}

// NewConnectionPool 创建连接池
func NewConnectionPool(config types.QueueConfig, size int) (*ConnectionPool, error) {
	pool := &ConnectionPool{
		connections: make([]*amqp.Connection, 0, size),
		channels:    make([]*amqp.Channel, 0, size),
		size:        size,
		current:     0,
	}

	// 创建连接池
	for i := 0; i < size; i++ {
		conn, err := amqp.Dial(config.URL)
		if err != nil {
			// 清理已创建的连接
			pool.Close()
			return nil, fmt.Errorf("创建连接池连接失败: %w", err)
		}

		ch, err := conn.Channel()
		if err != nil {
			conn.Close()
			pool.Close()
			return nil, fmt.Errorf("创建连接池通道失败: %w", err)
		}

		// 设置 QoS
		if err := ch.Qos(config.PrefetchCount, 0, false); err != nil {
			ch.Close()
			conn.Close()
			pool.Close()
			return nil, fmt.Errorf("设置连接池 QoS 失败: %w", err)
		}

		pool.connections = append(pool.connections, conn)
		pool.channels = append(pool.channels, ch)
	}

	return pool, nil
}

// GetChannel 从连接池获取通道
func (p *ConnectionPool) GetChannel() *amqp.Channel {
	p.mu.Lock()
	defer p.mu.Unlock()

	if len(p.channels) == 0 {
		return nil
	}

	// 轮询获取通道
	ch := p.channels[p.current]
	p.current = (p.current + 1) % len(p.channels)

	return ch
}

// GetConnection 从连接池获取连接
func (p *ConnectionPool) GetConnection() *amqp.Connection {
	p.mu.RLock()
	defer p.mu.RUnlock()

	if len(p.connections) == 0 {
		return nil
	}

	// 轮询获取连接
	conn := p.connections[p.current%len(p.connections)]
	return conn
}

// Close 关闭连接池
func (p *ConnectionPool) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	var errors []error

	// 关闭所有通道
	for _, ch := range p.channels {
		if ch != nil && !ch.IsClosed() {
			if err := ch.Close(); err != nil {
				errors = append(errors, err)
			}
		}
	}

	// 关闭所有连接
	for _, conn := range p.connections {
		if conn != nil && !conn.IsClosed() {
			if err := conn.Close(); err != nil {
				errors = append(errors, err)
			}
		}
	}

	p.connections = nil
	p.channels = nil

	if len(errors) > 0 {
		return fmt.Errorf("关闭连接池时出现错误: %v", errors)
	}

	return nil
}

// IsHealthy 检查连接池健康状态
func (p *ConnectionPool) IsHealthy() bool {
	p.mu.RLock()
	defer p.mu.RUnlock()

	for _, conn := range p.connections {
		if conn == nil || conn.IsClosed() {
			return false
		}
	}

	for _, ch := range p.channels {
		if ch == nil || ch.IsClosed() {
			return false
		}
	}

	return true
}

// NewHealthChecker 创建健康检查器
func NewHealthChecker(service *RabbitMQService, interval time.Duration) *HealthChecker {
	return &HealthChecker{
		service:  service,
		interval: interval,
		stopCh:   make(chan struct{}),
	}
}

// Start 启动健康检查
func (hc *HealthChecker) Start(ctx context.Context) {
	ticker := time.NewTicker(hc.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-hc.stopCh:
			return
		case <-ticker.C:
			hc.checkHealth()
		}
	}
}

// Stop 停止健康检查
func (hc *HealthChecker) Stop() {
	close(hc.stopCh)
}

// checkHealth 执行健康检查
func (hc *HealthChecker) checkHealth() {
	// 检查主连接
	if hc.service.connection == nil || hc.service.connection.IsClosed() {
		logger.Warn("RabbitMQ主连接不健康，尝试重连")
		hc.service.attemptReconnect()
		return
	}

	// 检查主通道
	if hc.service.channel == nil || hc.service.channel.IsClosed() {
		logger.Warn("RabbitMQ主通道不健康，尝试重建")
		if err := hc.service.recreateChannel(); err != nil {
			logger.Error("RabbitMQ重建通道失败", "error", err)
		}
		return
	}

	// 检查连接池（如果存在）
	if hc.service.pool != nil && !hc.service.pool.IsHealthy() {
		logger.Warn("RabbitMQ连接池不健康")
		// 这里可以添加连接池重建逻辑
	}

	// 执行简单的健康检查操作
	if err := hc.service.channel.ExchangeDeclarePassive(
		"amq.direct", // 使用默认交换机进行测试
		"direct",
		true,
		false,
		false,
		false,
		nil,
	); err != nil {
		logger.Warn("RabbitMQ健康检查失败", "error", err)
	}
}

// recreateChannel 重建通道
func (r *RabbitMQService) recreateChannel() error {
	r.connMu.Lock()
	defer r.connMu.Unlock()

	if r.connection == nil || r.connection.IsClosed() {
		return fmt.Errorf("连接不可用，无法重建通道")
	}

	// 关闭旧通道
	if r.channel != nil && !r.channel.IsClosed() {
		r.channel.Close()
	}

	// 创建新通道
	ch, err := r.connection.Channel()
	if err != nil {
		return fmt.Errorf("重建通道失败: %w", err)
	}

	// 设置 QoS
	if err := ch.Qos(r.config.PrefetchCount, 0, false); err != nil {
		ch.Close()
		return fmt.Errorf("重建通道设置 QoS 失败: %w", err)
	}

	r.channel = ch
	logger.Info("RabbitMQ通道重建成功")

	return nil
}

// InitializePool 初始化连接池
func (r *RabbitMQService) InitializePool(poolSize int) error {
	if poolSize <= 0 {
		poolSize = 5 // 默认连接池大小
	}

	pool, err := NewConnectionPool(r.config, poolSize)
	if err != nil {
		return fmt.Errorf("初始化连接池失败: %w", err)
	}

	r.pool = pool
	logger.Info("RabbitMQ连接池初始化成功", "size", poolSize)

	return nil
}

// StartHealthChecker 启动健康检查
func (r *RabbitMQService) StartHealthChecker(ctx context.Context) {
	if r.healthChecker == nil {
		r.healthChecker = NewHealthChecker(r, 30*time.Second)
	}

	go r.healthChecker.Start(ctx)
	logger.Info("RabbitMQ健康检查器已启动")
}

// StopHealthChecker 停止健康检查
func (r *RabbitMQService) StopHealthChecker() {
	if r.healthChecker != nil {
		r.healthChecker.Stop()
		logger.Info("RabbitMQ健康检查器已停止")
	}
}
