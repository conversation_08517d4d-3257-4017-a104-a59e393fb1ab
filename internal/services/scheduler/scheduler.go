package scheduler

import (
	"context"
	"fmt"
	"sync"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"
)

// SchedulerService 调度服务
type SchedulerService struct {
	config     *types.Config
	cache      types.CacheService
	queue      types.QueueService
	jobs       map[string]*Job
	crons      map[string]*CronJob
	workers    []*Worker
	jobQueue   chan *Job
	stopChan   chan struct{}
	mu         sync.RWMutex
	workerMu   sync.RWMutex
	stats      *SchedulerStats
	statsMu    sync.RWMutex
	isRunning  bool
}

// Job 任务定义
type Job struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Type        JobType                `json:"type"`
	Handler     JobHandler             `json:"-"`
	Data        map[string]interface{} `json:"data"`
	Priority    int                    `json:"priority"`
	MaxRetries  int                    `json:"max_retries"`
	Retries     int                    `json:"retries"`
	Timeout     time.Duration          `json:"timeout"`
	ScheduledAt time.Time              `json:"scheduled_at"`
	StartedAt   time.Time              `json:"started_at"`
	CompletedAt time.Time              `json:"completed_at"`
	Status      JobStatus              `json:"status"`
	Error       string                 `json:"error,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// CronJob 定时任务
type CronJob struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Schedule    string                 `json:"schedule"` // Cron 表达式
	JobTemplate *Job                   `json:"job_template"`
	Enabled     bool                   `json:"enabled"`
	NextRun     time.Time              `json:"next_run"`
	LastRun     time.Time              `json:"last_run"`
	RunCount    int64                  `json:"run_count"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// Worker 工作者
type Worker struct {
	ID        int
	JobQueue  chan *Job
	QuitChan  chan bool
	IsWorking bool
	mu        sync.RWMutex
}

// JobType 任务类型
type JobType string

const (
	JobTypeImmediate JobType = "immediate"
	JobTypeDelayed   JobType = "delayed"
	JobTypeRecurring JobType = "recurring"
	JobTypeScheduled JobType = "scheduled"
)

// JobStatus 任务状态
type JobStatus string

const (
	JobStatusPending   JobStatus = "pending"
	JobStatusRunning   JobStatus = "running"
	JobStatusCompleted JobStatus = "completed"
	JobStatusFailed    JobStatus = "failed"
	JobStatusCancelled JobStatus = "cancelled"
)

// JobHandler 任务处理器接口
type JobHandler interface {
	Execute(ctx context.Context, job *Job) error
	GetName() string
	GetTimeout() time.Duration
}

// SchedulerStats 调度器统计
type SchedulerStats struct {
	TotalJobs      int64     `json:"total_jobs"`
	CompletedJobs  int64     `json:"completed_jobs"`
	FailedJobs     int64     `json:"failed_jobs"`
	PendingJobs    int64     `json:"pending_jobs"`
	RunningJobs    int64     `json:"running_jobs"`
	ActiveWorkers  int       `json:"active_workers"`
	TotalWorkers   int       `json:"total_workers"`
	CronJobs       int       `json:"cron_jobs"`
	LastJobTime    time.Time `json:"last_job_time"`
	LastUpdated    time.Time `json:"last_updated"`
}

// NewSchedulerService 创建调度服务
func NewSchedulerService(config *types.Config, cache types.CacheService, queue types.QueueService) *SchedulerService {
	return &SchedulerService{
		config:   config,
		cache:    cache,
		queue:    queue,
		jobs:     make(map[string]*Job),
		crons:    make(map[string]*CronJob),
		workers:  make([]*Worker, 0),
		jobQueue: make(chan *Job, 1000), // 缓冲队列
		stopChan: make(chan struct{}),
		stats: &SchedulerStats{
			LastUpdated: time.Now(),
		},
	}
}

// Start 启动调度服务
func (s *SchedulerService) Start(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if s.isRunning {
		return fmt.Errorf("调度服务已在运行")
	}
	
	logger.Info("启动调度服务")
	
	// 启动工作者
	workerCount := s.getWorkerCount()
	for i := 0; i < workerCount; i++ {
		worker := s.createWorker(i)
		s.workers = append(s.workers, worker)
		go worker.Start(ctx)
	}
	
	// 启动定时任务检查器
	go s.cronScheduler(ctx)
	
	// 启动延迟任务检查器
	go s.delayedJobScheduler(ctx)
	
	// 启动统计更新器
	go s.statsUpdater(ctx)
	
	s.isRunning = true
	s.updateStats()
	
	logger.Info("调度服务启动完成", "workers", workerCount)
	return nil
}

// Stop 停止调度服务
func (s *SchedulerService) Stop(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if !s.isRunning {
		return nil
	}
	
	logger.Info("停止调度服务")
	
	// 发送停止信号
	close(s.stopChan)
	
	// 停止所有工作者
	s.workerMu.Lock()
	for _, worker := range s.workers {
		worker.Stop()
	}
	s.workers = s.workers[:0]
	s.workerMu.Unlock()
	
	s.isRunning = false
	s.updateStats()
	
	logger.Info("调度服务已停止")
	return nil
}

// ScheduleJob 调度任务
func (s *SchedulerService) ScheduleJob(job *Job) error {
	if job.ID == "" {
		job.ID = s.generateJobID()
	}
	
	job.CreatedAt = time.Now()
	job.UpdatedAt = time.Now()
	job.Status = JobStatusPending
	
	s.mu.Lock()
	s.jobs[job.ID] = job
	s.mu.Unlock()
	
	switch job.Type {
	case JobTypeImmediate:
		s.jobQueue <- job
	case JobTypeDelayed, JobTypeScheduled:
		// 延迟任务由 delayedJobScheduler 处理
	default:
		return fmt.Errorf("不支持的任务类型: %s", job.Type)
	}
	
	s.updateStats()
	
	logger.Info("任务已调度",
		"job_id", job.ID,
		"job_name", job.Name,
		"job_type", job.Type)
	
	return nil
}

// ScheduleCronJob 调度定时任务
func (s *SchedulerService) ScheduleCronJob(cronJob *CronJob) error {
	if cronJob.ID == "" {
		cronJob.ID = s.generateJobID()
	}
	
	cronJob.CreatedAt = time.Now()
	cronJob.UpdatedAt = time.Now()
	
	// 计算下次运行时间
	nextRun, err := s.parseNextRun(cronJob.Schedule)
	if err != nil {
		return fmt.Errorf("解析 Cron 表达式失败: %w", err)
	}
	cronJob.NextRun = nextRun
	
	s.mu.Lock()
	s.crons[cronJob.ID] = cronJob
	s.mu.Unlock()
	
	s.updateStats()
	
	logger.Info("定时任务已调度",
		"cron_id", cronJob.ID,
		"cron_name", cronJob.Name,
		"schedule", cronJob.Schedule,
		"next_run", cronJob.NextRun)
	
	return nil
}

// GetJob 获取任务
func (s *SchedulerService) GetJob(jobID string) (*Job, bool) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	job, exists := s.jobs[jobID]
	return job, exists
}

// GetCronJob 获取定时任务
func (s *SchedulerService) GetCronJob(cronID string) (*CronJob, bool) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	cron, exists := s.crons[cronID]
	return cron, exists
}

// CancelJob 取消任务
func (s *SchedulerService) CancelJob(jobID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	job, exists := s.jobs[jobID]
	if !exists {
		return fmt.Errorf("任务 %s 不存在", jobID)
	}
	
	if job.Status == JobStatusRunning {
		return fmt.Errorf("任务 %s 正在运行，无法取消", jobID)
	}
	
	job.Status = JobStatusCancelled
	job.UpdatedAt = time.Now()
	
	logger.Info("任务已取消", "job_id", jobID)
	return nil
}

// GetStats 获取统计信息
func (s *SchedulerService) GetStats() *SchedulerStats {
	s.statsMu.RLock()
	defer s.statsMu.RUnlock()
	
	// 返回副本
	stats := *s.stats
	return &stats
}

// createWorker 创建工作者
func (s *SchedulerService) createWorker(id int) *Worker {
	return &Worker{
		ID:       id,
		JobQueue: s.jobQueue,
		QuitChan: make(chan bool),
	}
}

// getWorkerCount 获取工作者数量
func (s *SchedulerService) getWorkerCount() int {
	// 从配置获取，默认为 5
	if s.config.Scheduler.Workers > 0 {
		return s.config.Scheduler.Workers
	}
	return 5
}

// generateJobID 生成任务ID
func (s *SchedulerService) generateJobID() string {
	return fmt.Sprintf("job_%d", time.Now().UnixNano())
}

// parseNextRun 解析下次运行时间
func (s *SchedulerService) parseNextRun(schedule string) (time.Time, error) {
	// 简单的 Cron 解析实现
	// 在实际项目中，应该使用专门的 Cron 库
	
	// 支持一些基本格式
	switch schedule {
	case "@hourly":
		return time.Now().Add(time.Hour), nil
	case "@daily":
		return time.Now().Add(24 * time.Hour), nil
	case "@weekly":
		return time.Now().Add(7 * 24 * time.Hour), nil
	default:
		// 默认每小时执行
		return time.Now().Add(time.Hour), nil
	}
}

// cronScheduler 定时任务调度器
func (s *SchedulerService) cronScheduler(ctx context.Context) {
	ticker := time.NewTicker(time.Minute) // 每分钟检查一次
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-s.stopChan:
			return
		case <-ticker.C:
			s.checkCronJobs()
		}
	}
}

// checkCronJobs 检查定时任务
func (s *SchedulerService) checkCronJobs() {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	now := time.Now()
	
	for _, cronJob := range s.crons {
		if !cronJob.Enabled {
			continue
		}
		
		if now.After(cronJob.NextRun) {
			// 创建任务实例
			job := &Job{
				ID:          s.generateJobID(),
				Name:        cronJob.JobTemplate.Name,
				Type:        JobTypeImmediate,
				Handler:     cronJob.JobTemplate.Handler,
				Data:        cronJob.JobTemplate.Data,
				Priority:    cronJob.JobTemplate.Priority,
				MaxRetries:  cronJob.JobTemplate.MaxRetries,
				Timeout:     cronJob.JobTemplate.Timeout,
				ScheduledAt: now,
			}
			
			// 调度任务
			s.ScheduleJob(job)
			
			// 更新定时任务
			cronJob.LastRun = now
			cronJob.RunCount++
			
			// 计算下次运行时间
			nextRun, err := s.parseNextRun(cronJob.Schedule)
			if err != nil {
				logger.Error("计算下次运行时间失败",
					"cron_id", cronJob.ID,
					"error", err)
				continue
			}
			cronJob.NextRun = nextRun
			cronJob.UpdatedAt = now
			
			logger.Debug("定时任务已触发",
				"cron_id", cronJob.ID,
				"job_id", job.ID,
				"next_run", cronJob.NextRun)
		}
	}
}

// delayedJobScheduler 延迟任务调度器
func (s *SchedulerService) delayedJobScheduler(ctx context.Context) {
	ticker := time.NewTicker(10 * time.Second) // 每10秒检查一次
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-s.stopChan:
			return
		case <-ticker.C:
			s.checkDelayedJobs()
		}
	}
}

// checkDelayedJobs 检查延迟任务
func (s *SchedulerService) checkDelayedJobs() {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	now := time.Now()
	
	for _, job := range s.jobs {
		if job.Status != JobStatusPending {
			continue
		}
		
		if (job.Type == JobTypeDelayed || job.Type == JobTypeScheduled) && now.After(job.ScheduledAt) {
			// 将任务加入执行队列
			select {
			case s.jobQueue <- job:
				logger.Debug("延迟任务已加入队列", "job_id", job.ID)
			default:
				logger.Warn("任务队列已满", "job_id", job.ID)
			}
		}
	}
}

// statsUpdater 统计更新器
func (s *SchedulerService) statsUpdater(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second) // 每30秒更新一次
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-s.stopChan:
			return
		case <-ticker.C:
			s.updateStats()
		}
	}
}

// updateStats 更新统计信息
func (s *SchedulerService) updateStats() {
	s.statsMu.Lock()
	defer s.statsMu.Unlock()
	
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	var totalJobs, completedJobs, failedJobs, pendingJobs, runningJobs int64
	
	for _, job := range s.jobs {
		totalJobs++
		switch job.Status {
		case JobStatusCompleted:
			completedJobs++
		case JobStatusFailed:
			failedJobs++
		case JobStatusPending:
			pendingJobs++
		case JobStatusRunning:
			runningJobs++
		}
	}
	
	s.workerMu.RLock()
	activeWorkers := 0
	for _, worker := range s.workers {
		if worker.IsWorking {
			activeWorkers++
		}
	}
	totalWorkers := len(s.workers)
	s.workerMu.RUnlock()
	
	s.stats.TotalJobs = totalJobs
	s.stats.CompletedJobs = completedJobs
	s.stats.FailedJobs = failedJobs
	s.stats.PendingJobs = pendingJobs
	s.stats.RunningJobs = runningJobs
	s.stats.ActiveWorkers = activeWorkers
	s.stats.TotalWorkers = totalWorkers
	s.stats.CronJobs = int(len(s.crons))
	s.stats.LastUpdated = time.Now()
}

// Start 启动工作者
func (w *Worker) Start(ctx context.Context) {
	logger.Debug("工作者启动", "worker_id", w.ID)
	
	for {
		select {
		case job := <-w.JobQueue:
			w.processJob(ctx, job)
		case <-w.QuitChan:
			logger.Debug("工作者停止", "worker_id", w.ID)
			return
		case <-ctx.Done():
			return
		}
	}
}

// Stop 停止工作者
func (w *Worker) Stop() {
	w.QuitChan <- true
}

// processJob 处理任务
func (w *Worker) processJob(ctx context.Context, job *Job) {
	w.mu.Lock()
	w.IsWorking = true
	w.mu.Unlock()
	
	defer func() {
		w.mu.Lock()
		w.IsWorking = false
		w.mu.Unlock()
	}()
	
	logger.Info("开始处理任务",
		"worker_id", w.ID,
		"job_id", job.ID,
		"job_name", job.Name)
	
	job.Status = JobStatusRunning
	job.StartedAt = time.Now()
	job.UpdatedAt = time.Now()
	
	// 设置超时
	jobCtx := ctx
	if job.Timeout > 0 {
		var cancel context.CancelFunc
		jobCtx, cancel = context.WithTimeout(ctx, job.Timeout)
		defer cancel()
	}
	
	// 执行任务
	err := job.Handler.Execute(jobCtx, job)
	
	job.CompletedAt = time.Now()
	job.UpdatedAt = time.Now()
	
	if err != nil {
		job.Status = JobStatusFailed
		job.Error = err.Error()
		job.Retries++
		
		logger.Error("任务执行失败",
			"worker_id", w.ID,
			"job_id", job.ID,
			"error", err,
			"retries", job.Retries)
		
		// 检查是否需要重试
		if job.Retries < job.MaxRetries {
			// 重新调度任务
			job.Status = JobStatusPending
			job.ScheduledAt = time.Now().Add(time.Minute * time.Duration(job.Retries)) // 递增延迟
			logger.Info("任务将重试", "job_id", job.ID, "retry_at", job.ScheduledAt)
		}
	} else {
		job.Status = JobStatusCompleted
		logger.Info("任务执行成功",
			"worker_id", w.ID,
			"job_id", job.ID,
			"duration", job.CompletedAt.Sub(job.StartedAt))
	}
}
