package tasks

import (
	"context"
	"fmt"
	"sync"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"
)

// ModuleStatus 模块状态
type ModuleStatus string

const (
	StatusUninitialized ModuleStatus = "uninitialized"
	StatusInitializing  ModuleStatus = "initializing"
	StatusInitialized   ModuleStatus = "initialized"
	StatusRunning       ModuleStatus = "running"
	StatusStopping      ModuleStatus = "stopping"
	StatusStopped       ModuleStatus = "stopped"
	StatusError         ModuleStatus = "error"
)

// HealthStatus 健康状态
type HealthStatus struct {
	Enabled          bool                   `json:"enabled"`
	ConfigValid      bool                   `json:"config_valid"`
	QueueConnected   bool                   `json:"queue_connected"`
	DiscordConnected bool                   `json:"discord_connected"`
	LastHealthCheck  time.Time              `json:"last_health_check"`
	ActiveMappings   int                    `json:"active_mappings,omitempty"`
	Stats            map[string]interface{} `json:"stats,omitempty"`
	LastError        string                 `json:"last_error,omitempty"`
	LastErrorAt      time.Time              `json:"last_error_at,omitempty"`
}

// ModuleConfig 模块配置接口
type ModuleConfig interface {
	IsEnabled() bool
	Validate() error
}

// ModuleInitializer 模块初始化器接口
type ModuleInitializer interface {
	// 基础生命周期
	Initialize(client *types.Client, queueService types.QueueService) error
	Shutdown() error

	// 健康检查
	PerformHealthCheck(ctx context.Context, client *types.Client) error
	GetHealthStatus() HealthStatus

	// 配置验证
	ValidateConfiguration(config *types.Config) error

	// 统计信息
	GetStatistics() map[string]interface{}
	ResetStatistics()

	// 模块信息
	GetModuleInfo() map[string]interface{}
}

// StandardModuleFramework 标准模块框架
type StandardModuleFramework struct {
	*BaseTaskModule

	// 状态管理
	status      ModuleStatus
	healthStats HealthStatus
	mu          sync.RWMutex

	// 配置
	configValidator func(*types.Config) error

	// 生命周期钩子
	onInitialize func(*types.Client, types.QueueService) error
	onShutdown   func() error

	// 健康检查钩子
	onHealthCheck func(context.Context, *types.Client) error

	// 统计信息
	statistics map[string]interface{}
	statsMu    sync.RWMutex
}

// NewStandardModuleFramework 创建标准模块框架
func NewStandardModuleFramework(name, description string, queues []string) *StandardModuleFramework {
	return &StandardModuleFramework{
		BaseTaskModule: NewBaseTaskModule(name, description, queues),
		status:         StatusUninitialized,
		healthStats: HealthStatus{
			Enabled:          false,
			ConfigValid:      false,
			QueueConnected:   false,
			DiscordConnected: false,
			LastHealthCheck:  time.Now(),
			Stats:            make(map[string]interface{}),
		},
		statistics: make(map[string]interface{}),
	}
}

// SetConfigValidator 设置配置验证器
func (smf *StandardModuleFramework) SetConfigValidator(validator func(*types.Config) error) {
	smf.configValidator = validator
}

// SetInitializeHook 设置初始化钩子
func (smf *StandardModuleFramework) SetInitializeHook(hook func(*types.Client, types.QueueService) error) {
	smf.onInitialize = hook
}

// SetShutdownHook 设置关闭钩子
func (smf *StandardModuleFramework) SetShutdownHook(hook func() error) {
	smf.onShutdown = hook
}

// SetHealthCheckHook 设置健康检查钩子
func (smf *StandardModuleFramework) SetHealthCheckHook(hook func(context.Context, *types.Client) error) {
	smf.onHealthCheck = hook
}

// Initialize 标准初始化流程
func (smf *StandardModuleFramework) Initialize(client *types.Client, queueService types.QueueService) error {
	smf.mu.Lock()
	defer smf.mu.Unlock()

	if smf.status != StatusUninitialized {
		return fmt.Errorf("模块 %s 已初始化，当前状态: %s", smf.GetName(), smf.status)
	}

	smf.status = StatusInitializing
	logger.Info("正在初始化模块", "name", smf.GetName())

	// 1. 配置验证
	if err := smf.validateConfig(client.Context.Config); err != nil {
		smf.status = StatusError
		smf.healthStats.ConfigValid = false
		smf.setLastError(err)
		return fmt.Errorf("配置验证失败: %w", err)
	}
	smf.healthStats.ConfigValid = true

	// 2. 检查模块是否启用
	if !smf.isModuleEnabled(client.Context.Config) {
		logger.Info("模块已禁用，跳过初始化", "name", smf.GetName())
		smf.healthStats.Enabled = false
		smf.status = StatusStopped
		return nil
	}
	smf.healthStats.Enabled = true

	// 3. 执行自定义初始化逻辑
	if smf.onInitialize != nil {
		if err := smf.onInitialize(client, queueService); err != nil {
			smf.status = StatusError
			smf.setLastError(err)
			return fmt.Errorf("自定义初始化失败: %w", err)
		}
	}

	// 4. 基础模块初始化
	if err := smf.BaseTaskModule.Initialize(client, queueService); err != nil {
		smf.status = StatusError
		smf.healthStats.QueueConnected = false
		smf.setLastError(err)
		return fmt.Errorf("基础模块初始化失败: %w", err)
	}
	smf.healthStats.QueueConnected = true

	// 5. 检查Discord连接
	smf.checkDiscordConnection(client)

	// 6. 更新状态
	smf.status = StatusInitialized
	smf.healthStats.LastHealthCheck = time.Now()

	logger.Info("模块初始化完成",
		"name", smf.GetName(),
		"enabled", smf.healthStats.Enabled,
		"config_valid", smf.healthStats.ConfigValid,
		"queue_connected", smf.healthStats.QueueConnected,
		"discord_connected", smf.healthStats.DiscordConnected)

	return nil
}

// Shutdown 标准关闭流程
func (smf *StandardModuleFramework) Shutdown() error {
	smf.mu.Lock()
	defer smf.mu.Unlock()

	if smf.status == StatusStopped || smf.status == StatusUninitialized {
		return nil
	}

	smf.status = StatusStopping
	logger.Info("正在关闭模块", "name", smf.GetName())

	// 1. 执行自定义关闭逻辑
	if smf.onShutdown != nil {
		if err := smf.onShutdown(); err != nil {
			logger.Error("自定义关闭逻辑失败", "name", smf.GetName(), "error", err)
		}
	}

	// 2. 基础模块关闭
	if err := smf.BaseTaskModule.Shutdown(); err != nil {
		logger.Error("基础模块关闭失败", "name", smf.GetName(), "error", err)
	}

	// 3. 更新状态
	smf.status = StatusStopped
	smf.healthStats.Enabled = false
	smf.healthStats.QueueConnected = false
	smf.healthStats.DiscordConnected = false
	smf.healthStats.LastHealthCheck = time.Now()

	logger.Info("模块已关闭", "name", smf.GetName())
	return nil
}

// PerformHealthCheck 执行健康检查
func (smf *StandardModuleFramework) PerformHealthCheck(ctx context.Context, client *types.Client) error {
	smf.mu.RLock()
	defer smf.mu.RUnlock()

	logger.Debug("执行模块健康检查", "name", smf.GetName())

	// 1. 检查模块状态
	if smf.status != StatusInitialized && smf.status != StatusRunning {
		return fmt.Errorf("模块状态异常: %s", smf.status)
	}

	// 2. 检查基础健康状态
	if !smf.healthStats.Enabled {
		return fmt.Errorf("模块未启用")
	}

	if !smf.healthStats.ConfigValid {
		return fmt.Errorf("配置无效")
	}

	if !smf.healthStats.QueueConnected {
		return fmt.Errorf("队列服务连接异常")
	}

	// 3. 执行自定义健康检查
	if smf.onHealthCheck != nil {
		if err := smf.onHealthCheck(ctx, client); err != nil {
			smf.setLastError(err)
			return fmt.Errorf("自定义健康检查失败: %w", err)
		}
	}

	// 4. 更新健康检查时间
	smf.healthStats.LastHealthCheck = time.Now()

	logger.Debug("模块健康检查通过", "name", smf.GetName())
	return nil
}

// GetHealthStatus 获取健康状态
func (smf *StandardModuleFramework) GetHealthStatus() HealthStatus {
	smf.mu.RLock()
	defer smf.mu.RUnlock()

	// 返回副本
	status := smf.healthStats
	status.LastHealthCheck = time.Now()
	return status
}

// ValidateConfiguration 验证配置
func (smf *StandardModuleFramework) ValidateConfiguration(config *types.Config) error {
	return smf.validateConfig(config)
}

// GetStatistics 获取统计信息
func (smf *StandardModuleFramework) GetStatistics() map[string]interface{} {
	smf.statsMu.RLock()
	defer smf.statsMu.RUnlock()

	stats := make(map[string]interface{})
	for k, v := range smf.statistics {
		stats[k] = v
	}

	// 添加基础统计信息
	stats["module_name"] = smf.GetName()
	stats["status"] = smf.status
	stats["enabled"] = smf.healthStats.Enabled
	stats["last_health_check"] = smf.healthStats.LastHealthCheck

	return stats
}

// ResetStatistics 重置统计信息
func (smf *StandardModuleFramework) ResetStatistics() {
	smf.statsMu.Lock()
	defer smf.statsMu.Unlock()

	smf.statistics = make(map[string]interface{})
	logger.Info("模块统计信息已重置", "name", smf.GetName())
}

// GetModuleInfo 获取模块信息
func (smf *StandardModuleFramework) GetModuleInfo() map[string]interface{} {
	smf.mu.RLock()
	defer smf.mu.RUnlock()

	return map[string]interface{}{
		"name":        smf.GetName(),
		"description": smf.GetDescription(),
		"queues":      smf.GetQueues(),
		"handlers":    len(smf.GetHandlers()),
		"status":      smf.status,
		"health":      smf.GetHealthStatus(),
		"statistics":  smf.GetStatistics(),
	}
}

// UpdateStatistic 更新统计信息
func (smf *StandardModuleFramework) UpdateStatistic(key string, value interface{}) {
	smf.statsMu.Lock()
	defer smf.statsMu.Unlock()

	smf.statistics[key] = value
}

// GetStatus 获取模块状态
func (smf *StandardModuleFramework) GetStatus() ModuleStatus {
	smf.mu.RLock()
	defer smf.mu.RUnlock()

	return smf.status
}

// validateConfig 验证配置
func (smf *StandardModuleFramework) validateConfig(config *types.Config) error {
	if config == nil {
		return fmt.Errorf("配置对象为空")
	}

	if smf.configValidator != nil {
		return smf.configValidator(config)
	}

	return nil
}

// isModuleEnabled 检查模块是否启用
func (smf *StandardModuleFramework) isModuleEnabled(config *types.Config) bool {
	// 默认实现，子类可以重写
	return true
}

// checkDiscordConnection 检查Discord连接
func (smf *StandardModuleFramework) checkDiscordConnection(client *types.Client) {
	if client.Session != nil && client.Session.State != nil && client.Session.State.User != nil {
		smf.healthStats.DiscordConnected = true
		logger.Debug("Discord连接状态正常", "bot_user", client.Session.State.User.Username)
	} else {
		smf.healthStats.DiscordConnected = false
		logger.Warn("Discord连接状态异常")
	}
}

// setLastError 设置最后错误
func (smf *StandardModuleFramework) setLastError(err error) {
	if err != nil {
		smf.healthStats.LastError = err.Error()
		smf.healthStats.LastErrorAt = time.Now()
	}
}
