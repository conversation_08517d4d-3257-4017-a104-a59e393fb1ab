package instock

import (
	"zeka-go/internal/services/logger"
	"zeka-go/internal/tasks"
	"zeka-go/internal/types"
)

// InStockTaskModule 库存通知任务模块
type InStockTaskModule struct {
	*tasks.BaseTaskModule
}

// NewInStockTaskModule 创建新的库存通知任务模块
func NewInStockTaskModule() *InStockTaskModule {
	// 定义模块处理的队列
	queues := []string{"instock", "instock.notification", "instock.monitor"}

	module := &InStockTaskModule{
		BaseTaskModule: tasks.NewBaseTaskModule(
			"instock",
			"库存通知任务模块，处理商品库存变化通知",
			queues,
		),
	}

	return module
}

// Initialize 初始化模块
func (m *InStockTaskModule) Initialize(client *types.Client, queueService types.QueueService) error {
	logger.Info("📦 正在初始化库存通知任务模块...")

	// 注册任务处理器
	instockHandler := NewInStockTaskHandler()
	m.BaseTaskModule.RegisterHandler(instockHandler)

	logger.Debug("📝 库存通知任务处理器已注册", "type", instockHandler.GetType())

	// 调用基础初始化
	if err := m.BaseTaskModule.Initialize(client, queueService); err != nil {
		return err
	}

	logger.Info("✅ 库存通知任务模块初始化完成")
	return nil
}

// Shutdown 关闭模块
func (m *InStockTaskModule) Shutdown() error {
	logger.Info("正在关闭库存通知任务模块...")

	// 调用基础关闭
	if err := m.BaseTaskModule.Shutdown(); err != nil {
		return err
	}

	logger.Info("✅ 库存通知任务模块已关闭")
	return nil
}
