package instock

import (
	"context"
	"fmt"
	"net/url"
	"strings"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/tasks"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// InStockTaskHandler 库存通知任务处理器
type InStockTaskHandler struct {
	*tasks.BaseTaskHandler
}

// NewInStockTaskHandler 创建新的库存通知任务处理器
func NewInStockTaskHandler() *InStockTaskHandler {
	return &InStockTaskHandler{
		BaseTaskHandler: tasks.NewBaseTaskHandler("instock", "处理商品库存通知"),
	}
}

// ProductData 产品数据结构
type ProductData struct {
	Platform  string `json:"platform"`
	SiteURL   string `json:"siteUrl"`
	ProductID string `json:"productId"`
	OfferID   string `json:"offerId"`
	SkuID     string `json:"skuId"`
	Title     string `json:"title"`
	Price     string `json:"price"`
	ImageURL  string `json:"imageUrl"`
	StockInfo string `json:"stockInfo"`
	ChannelID string `json:"channelId"`
}

// Handle 处理库存通知任务
func (h *InStockTaskHandler) Handle(ctx context.Context, client *types.Client, data interface{}) error {
	logger.Info("处理库存通知任务", "handler", h.GetType())

	// 解析任务数据
	productData, err := h.parseProductData(data)
	if err != nil {
		return fmt.Errorf("解析产品数据失败: %w", err)
	}

	// 生成 Discord 消息
	embed, err := h.createDiscordEmbed(productData)
	if err != nil {
		return fmt.Errorf("创建 Discord 嵌入消息失败: %w", err)
	}

	// 发送消息到指定频道（如果有 Discord session）
	if client.Session != nil {
		if err := h.sendToChannel(client.Session, productData.ChannelID, embed); err != nil {
			return fmt.Errorf("发送消息到频道失败: %w", err)
		}
	} else {
		logger.Debug("跳过 Discord 消息发送（无 session）", "channel", productData.ChannelID)
	}

	logger.Info("库存通知发送成功",
		"platform", productData.Platform,
		"product", productData.Title,
		"channel", productData.ChannelID)

	return nil
}

// parseProductData 解析产品数据
func (h *InStockTaskHandler) parseProductData(data interface{}) (*ProductData, error) {
	// 尝试从 map[string]interface{} 解析
	if dataMap, ok := data.(map[string]interface{}); ok {
		productData := &ProductData{}

		if platform, ok := dataMap["platform"].(string); ok {
			productData.Platform = platform
		}
		if siteURL, ok := dataMap["siteUrl"].(string); ok {
			productData.SiteURL = siteURL
		}
		if productID, ok := dataMap["productId"].(string); ok {
			productData.ProductID = productID
		}
		if offerID, ok := dataMap["offerId"].(string); ok {
			productData.OfferID = offerID
		}
		if skuID, ok := dataMap["skuId"].(string); ok {
			productData.SkuID = skuID
		}
		if title, ok := dataMap["title"].(string); ok {
			productData.Title = title
		}
		if price, ok := dataMap["price"].(string); ok {
			productData.Price = price
		}
		if imageURL, ok := dataMap["imageUrl"].(string); ok {
			productData.ImageURL = imageURL
		}
		if stockInfo, ok := dataMap["stockInfo"].(string); ok {
			productData.StockInfo = stockInfo
		}
		if channelID, ok := dataMap["channelId"].(string); ok {
			productData.ChannelID = channelID
		}

		return productData, nil
	}

	return nil, fmt.Errorf("无效的产品数据格式")
}

// createDiscordEmbed 创建 Discord 嵌入消息
func (h *InStockTaskHandler) createDiscordEmbed(data *ProductData) (*discordgo.MessageEmbed, error) {
	// 生成 ATC 链接
	atcLinks := h.generateATCLinks(data)

	// 确定颜色
	color := h.getPlatformColor(data.Platform)

	embed := &discordgo.MessageEmbed{
		Title:       fmt.Sprintf("🛒 %s - 库存通知", data.Platform),
		Description: fmt.Sprintf("**%s**", data.Title),
		Color:       color,
		Fields: []*discordgo.MessageEmbedField{
			{
				Name:   "💰 价格",
				Value:  data.Price,
				Inline: true,
			},
			{
				Name:   "📦 库存状态",
				Value:  data.StockInfo,
				Inline: true,
			},
			{
				Name:   "🛍️ 快速购买",
				Value:  atcLinks,
				Inline: false,
			},
		},
		Timestamp: "2025-01-01T00:00:00Z", // 当前时间戳
	}

	// 添加图片
	if data.ImageURL != "" {
		embed.Thumbnail = &discordgo.MessageEmbedThumbnail{
			URL: data.ImageURL,
		}
	}

	return embed, nil
}

// generateATCLinks 生成平台特定的 ATC 链接
func (h *InStockTaskHandler) generateATCLinks(data *ProductData) string {
	switch strings.ToLower(data.Platform) {
	case "amazon":
		return h.generateAmazonATCLinks(data)
	case "aliexpress":
		return h.generateAliExpressATCLinks(data)
	case "popmart":
		return h.generatePopMartATCLinks(data)
	default:
		return "链接不可用"
	}
}

// generateAmazonATCLinks 生成 Amazon ATC 链接
func (h *InStockTaskHandler) generateAmazonATCLinks(data *ProductData) string {
	if data.OfferID == "" || data.ProductID == "" {
		return "链接不可用"
	}

	baseURL := fmt.Sprintf("%s/gp/product/handle-buy-box/ref=dp_start-bbf_1_glance", data.SiteURL)

	link1 := fmt.Sprintf("%s?offerListingID=%s&ASIN=%s&quantity.1=1&quantity=1&submit.buy-now=1",
		baseURL, data.OfferID, data.ProductID)
	link2 := fmt.Sprintf("%s?offerListingID=%s&ASIN=%s&quantity.1=2&quantity=1&submit.buy-now=1",
		baseURL, data.OfferID, data.ProductID)

	return fmt.Sprintf("[x1](%s) | [x2](%s)", link1, link2)
}

// generateAliExpressATCLinks 生成 AliExpress ATC 链接
func (h *InStockTaskHandler) generateAliExpressATCLinks(data *ProductData) string {
	if data.ProductID == "" || data.SkuID == "" {
		return "链接不可用"
	}

	baseURL := fmt.Sprintf("%s/p/trade/confirm.html", data.SiteURL)

	link1 := fmt.Sprintf("%s?productId=%s&skuId=%s&quantity=1", baseURL, data.ProductID, data.SkuID)
	link2 := fmt.Sprintf("%s?productId=%s&skuId=%s&quantity=2", baseURL, data.ProductID, data.SkuID)

	return fmt.Sprintf("[x1](%s) | [x2](%s)", link1, link2)
}

// generatePopMartATCLinks 生成 PopMart ATC 链接
func (h *InStockTaskHandler) generatePopMartATCLinks(data *ProductData) string {
	if data.ProductID == "" || data.Title == "" {
		return "链接不可用"
	}

	encodedTitle := url.QueryEscape(data.Title)
	baseURL := fmt.Sprintf("%s/order-confirmation", data.SiteURL)

	link1 := fmt.Sprintf("%s?spuId=%s&skuId=%s&spuTitle=%s&count=1",
		baseURL, data.ProductID, data.SkuID, encodedTitle)
	link2 := fmt.Sprintf("%s?spuId=%s&skuId=%s&spuTitle=%s&count=2",
		baseURL, data.ProductID, data.SkuID, encodedTitle)

	return fmt.Sprintf("[x1](%s) | [x2](%s)", link1, link2)
}

// getPlatformColor 获取平台对应的颜色
func (h *InStockTaskHandler) getPlatformColor(platform string) int {
	switch strings.ToLower(platform) {
	case "amazon":
		return 0xFF9900 // Amazon 橙色
	case "aliexpress":
		return 0xFF6A00 // AliExpress 橙色
	case "popmart":
		return 0xFF1744 // PopMart 红色
	default:
		return 0x00FF00 // 默认绿色
	}
}

// sendToChannel 发送消息到指定频道
func (h *InStockTaskHandler) sendToChannel(session *discordgo.Session, channelID string, embed *discordgo.MessageEmbed) error {
	if channelID == "" {
		return fmt.Errorf("频道 ID 不能为空")
	}

	_, err := session.ChannelMessageSendEmbed(channelID, embed)
	if err != nil {
		return fmt.Errorf("发送嵌入消息失败: %w", err)
	}

	return nil
}

// Validate 验证任务数据
func (h *InStockTaskHandler) Validate(data interface{}) error {
	productData, err := h.parseProductData(data)
	if err != nil {
		return err
	}

	if productData.Platform == "" {
		return fmt.Errorf("平台信息不能为空")
	}
	if productData.Title == "" {
		return fmt.Errorf("产品标题不能为空")
	}
	if productData.ChannelID == "" {
		return fmt.Errorf("频道 ID 不能为空")
	}

	return nil
}
