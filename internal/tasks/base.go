package tasks

import (
	"context"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"
)

// TaskHandler 任务处理器接口
type TaskHandler interface {
	// GetType 获取任务类型
	GetType() string

	// GetDescription 获取任务描述
	GetDescription() string

	// Handle 处理任务
	Handle(ctx context.Context, client *types.Client, data interface{}) error

	// Validate 验证任务数据
	Validate(data interface{}) error
}

// BaseTaskHandler 基础任务处理器
type BaseTaskHandler struct {
	taskType    string
	description string
}

// NewBaseTaskHandler 创建基础任务处理器
func NewBaseTaskHandler(taskType, description string) *BaseTaskHandler {
	return &BaseTaskHandler{
		taskType:    taskType,
		description: description,
	}
}

// GetType 获取任务类型
func (b *BaseTaskHandler) GetType() string {
	return b.taskType
}

// GetDescription 获取任务描述
func (b *BaseTaskHandler) GetDescription() string {
	return b.description
}

// Validate 默认验证实现
func (b *BaseTaskHandler) Validate(data interface{}) error {
	if data == nil {
		return types.ErrInvalidTaskData
	}
	return nil
}

// TaskModule 任务模块接口
type TaskModule interface {
	// GetName 获取模块名称
	GetName() string

	// GetDescription 获取模块描述
	GetDescription() string

	// GetQueues 获取模块处理的队列列表
	GetQueues() []string

	// GetHandlers 获取任务处理器映射
	GetHandlers() map[string]TaskHandler

	// Initialize 初始化模块
	Initialize(client *types.Client, queueService types.QueueService) error

	// Shutdown 关闭模块
	Shutdown() error
}

// BaseTaskModule 基础任务模块
type BaseTaskModule struct {
	name         string
	description  string
	queues       []string
	handlers     map[string]TaskHandler
	client       *types.Client
	queueService types.QueueService
}

// NewBaseTaskModule 创建基础任务模块
func NewBaseTaskModule(name, description string, queues []string) *BaseTaskModule {
	return &BaseTaskModule{
		name:        name,
		description: description,
		queues:      queues,
		handlers:    make(map[string]TaskHandler),
	}
}

// GetName 获取模块名称
func (b *BaseTaskModule) GetName() string {
	return b.name
}

// GetDescription 获取模块描述
func (b *BaseTaskModule) GetDescription() string {
	return b.description
}

// GetQueues 获取模块处理的队列列表
func (b *BaseTaskModule) GetQueues() []string {
	return b.queues
}

// GetHandlers 获取任务处理器映射
func (b *BaseTaskModule) GetHandlers() map[string]TaskHandler {
	return b.handlers
}

// RegisterHandler 注册任务处理器
func (b *BaseTaskModule) RegisterHandler(handler TaskHandler) {
	b.handlers[handler.GetType()] = handler
}

// Initialize 初始化模块
func (b *BaseTaskModule) Initialize(client *types.Client, queueService types.QueueService) error {
	b.client = client
	b.queueService = queueService

	// 注册任务处理器到队列服务
	if queueService != nil {
		for taskType, handler := range b.handlers {
			queueService.RegisterHandler(taskType, func(data interface{}) error {
				return handler.Handle(context.Background(), client, data)
			})
		}
	} else {
		logger.Debug("⚠️ 队列服务不可用，跳过任务处理器注册", "module", b.name)
	}

	return nil
}

// Shutdown 关闭模块
func (b *BaseTaskModule) Shutdown() error {
	// 默认实现，子类可以重写
	return nil
}
