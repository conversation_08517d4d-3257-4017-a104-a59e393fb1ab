package tasks

import (
	"context"
	"fmt"
	"sync"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"
)

// TaskLoader 任务加载器
type TaskLoader struct {
	client         *types.Client
	queueService   types.QueueService
	modules        map[string]TaskModule
	builtinModules []TaskModule
	mu             sync.RWMutex
	ctx            context.Context
	cancel         context.CancelFunc
}

// NewTaskLoader 创建新的任务加载器
func NewTaskLoader(client *types.Client, queueService types.QueueService) *TaskLoader {
	ctx, cancel := context.WithCancel(context.Background())

	return &TaskLoader{
		client:         client,
		queueService:   queueService,
		modules:        make(map[string]TaskModule),
		builtinModules: make([]TaskModule, 0),
		ctx:            ctx,
		cancel:         cancel,
	}
}

// SetBuiltinModules 设置内置模块列表
func (tl *TaskLoader) SetBuiltinModules(modules []TaskModule) {
	tl.mu.Lock()
	defer tl.mu.Unlock()
	tl.builtinModules = modules
}

// LoadModules 加载所有任务模块
func (tl *TaskLoader) LoadModules() error {
	tl.mu.Lock()
	defer tl.mu.Unlock()

	logger.Info("🔍 正在加载任务模块...")

	// 手动注册任务模块（避免复杂的动态扫描）
	if err := tl.registerBuiltinModules(); err != nil {
		return fmt.Errorf("注册内置模块失败: %w", err)
	}

	logger.Info("✅ 任务模块加载完成", "count", len(tl.modules))
	return nil
}

// registerBuiltinModules 注册内置任务模块
func (tl *TaskLoader) registerBuiltinModules() error {
	// 注册逻辑已移动到 builtin 包中，避免导入循环
	// 这个方法现在是一个占位符，实际注册由外部调用完成
	logger.Debug("内置模块注册将由外部调用完成")
	return nil
}

// RegisterModule 注册任务模块
func (tl *TaskLoader) RegisterModule(module TaskModule) error {
	tl.mu.Lock()
	defer tl.mu.Unlock()

	name := module.GetName()
	if _, exists := tl.modules[name]; exists {
		return fmt.Errorf("任务模块 %s 已存在", name)
	}

	logger.Info("📦 正在注册任务模块", "name", name, "description", module.GetDescription())

	// 初始化模块
	if err := module.Initialize(tl.client, tl.queueService); err != nil {
		return fmt.Errorf("初始化任务模块 %s 失败: %w", name, err)
	}

	// 启动队列消费者
	for _, queueName := range module.GetQueues() {
		if tl.queueService != nil {
			if err := tl.queueService.StartConsumer(queueName, nil); err != nil {
				logger.Error("启动队列消费者失败", "queue", queueName, "module", name, "error", err)
				// 不返回错误，继续处理其他队列
			} else {
				logger.Info("🎯 任务队列消费者已启动", "queue", queueName, "module", name)
			}
		} else {
			logger.Warn("⚠️ 队列服务不可用，队列消费者未启动", "queue", queueName, "module", name)
		}
	}

	// 注册任务处理器
	handlers := module.GetHandlers()
	for taskType, handler := range handlers {
		if tl.queueService != nil {
			// 创建闭包来捕获handler变量，避免循环变量问题
			currentHandler := handler
			tl.queueService.RegisterHandler(taskType, func(data any) error {
				return currentHandler.Handle(context.Background(), tl.client, data)
			})
			logger.Debug("📝 已注册任务处理器", "type", taskType, "module", name)
		} else {
			logger.Debug("⚠️ 队列服务不可用，跳过任务处理器注册", "type", taskType, "module", name)
		}
	}

	tl.modules[name] = module
	logger.Info("✅ 任务模块注册成功",
		"name", name,
		"queues", len(module.GetQueues()),
		"handlers", len(handlers))

	return nil
}

// GetModules 获取已加载的模块列表
func (tl *TaskLoader) GetModules() []TaskModule {
	tl.mu.RLock()
	defer tl.mu.RUnlock()

	modules := make([]TaskModule, 0, len(tl.modules))
	for _, module := range tl.modules {
		modules = append(modules, module)
	}
	return modules
}

// GetModule 获取特定模块
func (tl *TaskLoader) GetModule(name string) (TaskModule, bool) {
	tl.mu.RLock()
	defer tl.mu.RUnlock()

	module, exists := tl.modules[name]
	return module, exists
}

// GetStats 获取任务处理统计信息
func (tl *TaskLoader) GetStats() map[string]any {
	tl.mu.RLock()
	defer tl.mu.RUnlock()

	stats := make(map[string]any)

	// 添加模块统计
	moduleStats := make(map[string]any)
	for name, module := range tl.modules {
		moduleStats[name] = map[string]any{
			"description": module.GetDescription(),
			"queues":      module.GetQueues(),
			"handlers":    len(module.GetHandlers()),
		}
	}
	stats["modules"] = moduleStats

	// 添加队列服务统计
	if tl.queueService != nil {
		stats["queue"] = tl.queueService.GetStats()
	}

	return stats
}

// GetModuleInfo 获取模块信息
func (tl *TaskLoader) GetModuleInfo() []map[string]any {
	tl.mu.RLock()
	defer tl.mu.RUnlock()

	info := make([]map[string]any, 0, len(tl.modules))
	for _, module := range tl.modules {
		moduleInfo := map[string]any{
			"name":        module.GetName(),
			"description": module.GetDescription(),
			"queues":      module.GetQueues(),
			"handlers":    getHandlerTypes(module.GetHandlers()),
			"type":        fmt.Sprintf("%T", module),
		}
		info = append(info, moduleInfo)
	}
	return info
}

// getHandlerTypes 获取处理器类型列表
func getHandlerTypes(handlers map[string]TaskHandler) []string {
	types := make([]string, 0, len(handlers))
	for taskType := range handlers {
		types = append(types, taskType)
	}
	return types
}

// ShutdownModules 关闭所有模块
func (tl *TaskLoader) ShutdownModules() error {
	tl.mu.Lock()
	defer tl.mu.Unlock()

	logger.Info("🛑 正在关闭任务模块...")

	var errors []error
	for name, module := range tl.modules {
		if err := module.Shutdown(); err != nil {
			logger.Error("关闭任务模块失败", "name", name, "error", err)
			errors = append(errors, fmt.Errorf("模块 %s: %w", name, err))
		} else {
			logger.Debug("✅ 已关闭任务模块", "name", name)
		}
	}

	if len(errors) > 0 {
		logger.Warn("部分任务模块关闭时出现错误", "errors", len(errors))
	}

	logger.Info("✅ 所有任务模块已关闭")
	return nil
}

// Shutdown 关闭任务加载器
func (tl *TaskLoader) Shutdown() error {
	tl.mu.Lock()
	defer tl.mu.Unlock()

	logger.Info("🛑 正在关闭任务加载器...")

	// 取消上下文
	if tl.cancel != nil {
		tl.cancel()
	}

	// 关闭所有模块
	for name, module := range tl.modules {
		if err := module.Shutdown(); err != nil {
			logger.Error("关闭任务模块失败", "name", name, "error", err)
		} else {
			logger.Debug("✅ 任务模块已关闭", "name", name)
		}
	}

	logger.Info("✅ 任务加载器已关闭")
	return nil
}
