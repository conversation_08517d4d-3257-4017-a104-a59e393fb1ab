package commands

import (
	"context"
	"fmt"
	"strings"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// FilterCommand Filter命令处理器（严格遵循设计文档）
// 实现/filter命令系列：add、remove、list、clear
type FilterCommand struct {
	name        string
	description string

	// 服务依赖
	filterService  types.FilterEngine
	forwardService types.ForwardRuleManager
}

// NewFilterCommand 创建Filter命令处理器
func NewFilterCommand(filterService types.FilterEngine, forwardService types.ForwardRuleManager) *FilterCommand {
	return &FilterCommand{
		name:           "filter",
		description:    "管理过滤规则：添加、删除、列出、清除过滤规则",
		filterService:  filterService,
		forwardService: forwardService,
	}
}

// GetName 获取命令名称
func (fc *FilterCommand) GetName() string {
	return fc.name
}

// GetDescription 获取命令描述
func (fc *FilterCommand) GetDescription() string {
	return fc.description
}

// GetCategory 获取命令分类
func (fc *FilterCommand) GetCategory() string {
	return "管理"
}

// GetCooldown 获取冷却时间
func (fc *FilterCommand) GetCooldown() time.Duration {
	return 5 * time.Second
}

// GetPermissions 获取所需权限
func (fc *FilterCommand) GetPermissions() []string {
	return []string{"MANAGE_CHANNELS"} // 需要管理频道权限
}

// Validate 验证命令参数
func (fc *FilterCommand) Validate(interaction *discordgo.InteractionCreate) error {
	// 基本验证逻辑
	return nil
}

// Execute 执行命令
func (fc *FilterCommand) Execute(ctx context.Context, client *types.Client, i *discordgo.InteractionCreate) error {
	s := client.Session
	// 权限检查由中间件处理，这里不需要额外检查

	// 获取子命令
	options := i.ApplicationCommandData().Options
	if len(options) == 0 {
		return fc.respondError(s, i, "请指定子命令：add、remove、list、clear")
	}

	subCommand := options[0].Name
	subOptions := options[0].Options

	switch subCommand {
	case "add":
		return fc.handleAdd(s, i, subOptions)
	case "remove":
		return fc.handleRemove(s, i, subOptions)
	case "list":
		return fc.handleList(s, i, subOptions)
	case "clear":
		return fc.handleClear(s, i, subOptions)
	default:
		return fc.respondError(s, i, fmt.Sprintf("未知的子命令: %s", subCommand))
	}
}

// handleAdd 处理添加过滤规则（遵循设计文档的+/-前缀格式）
func (fc *FilterCommand) handleAdd(s *discordgo.Session, i *discordgo.InteractionCreate, options []*discordgo.ApplicationCommandInteractionDataOption) error {
	// 解析参数
	var channelID, keywordWithPrefix string

	for _, option := range options {
		switch option.Name {
		case "channel":
			channelID = option.ChannelValue(nil).ID
		case "keyword": // 改为单个keyword，支持+/-前缀
			keywordWithPrefix = option.StringValue()
		}
	}

	// 验证必需参数
	if channelID == "" {
		return fc.respondError(s, i, "频道是必需的")
	}

	if keywordWithPrefix == "" {
		return fc.respondError(s, i, "关键词是必需的")
	}

	// 解析关键词和模式（从+/-前缀）
	keyword, mode := types.ParseKeywordWithMode(keywordWithPrefix)

	// 创建过滤规则（极简结构）
	rule := &types.FilterRule{
		Channel: channelID,
		Keyword: keyword,
		Mode:    mode,
	}

	// 添加规则
	if err := fc.filterService.AddRule(rule); err != nil {
		logger.Error("添加过滤规则失败", "error", err, "user", fc.getUserID(i))
		return fc.respondError(s, i, fmt.Sprintf("添加过滤规则失败: %v", err))
	}

	// 记录操作日志
	fc.logOperation("add_filter_rule", fc.getUserID(i), map[string]interface{}{
		"channel": channelID,
		"keyword": keyword,
		"mode":    mode,
	})

	// 响应成功
	embed := &discordgo.MessageEmbed{
		Title:       "✅ 过滤规则已添加",
		Description: fmt.Sprintf("频道: <#%s>\n关键词: `%s`\n模式: `%s`", channelID, rule.FormatKeywordWithMode(), mode),
		Color:       0x00ff00,
	}

	return s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Embeds: []*discordgo.MessageEmbed{embed},
		},
	})
}

// handleRemove 处理删除过滤规则
func (fc *FilterCommand) handleRemove(s *discordgo.Session, i *discordgo.InteractionCreate, options []*discordgo.ApplicationCommandInteractionDataOption) error {
	// 解析参数
	var channelID, keywordWithPrefix string
	for _, option := range options {
		switch option.Name {
		case "channel":
			channelID = option.ChannelValue(nil).ID
		case "keyword":
			keywordWithPrefix = option.StringValue()
		}
	}

	if channelID == "" || keywordWithPrefix == "" {
		return fc.respondError(s, i, "频道和关键词都是必需的")
	}

	// 解析关键词和模式
	keyword, mode := types.ParseKeywordWithMode(keywordWithPrefix)

	// 构造唯一ID
	tempRule := &types.FilterRule{
		Channel: channelID,
		Keyword: keyword,
		Mode:    mode,
	}
	uniqueID := tempRule.GetUniqueID()

	// 删除规则
	if err := fc.filterService.RemoveRule(uniqueID); err != nil {
		logger.Error("删除过滤规则失败", "error", err, "user", fc.getUserID(i))
		return fc.respondError(s, i, fmt.Sprintf("删除过滤规则失败: %v", err))
	}

	// 记录操作日志
	fc.logOperation("remove_filter_rule", fc.getUserID(i), map[string]interface{}{
		"channel":   channelID,
		"keyword":   keyword,
		"mode":      mode,
		"unique_id": uniqueID,
	})

	// 响应成功
	embed := &discordgo.MessageEmbed{
		Title:       "✅ 过滤规则已删除",
		Description: fmt.Sprintf("规则 `%s` 已成功删除", keywordWithPrefix),
		Color:       0x00ff00,
	}

	return s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Embeds: []*discordgo.MessageEmbed{embed},
		},
	})
}

// handleList 处理列出过滤规则
func (fc *FilterCommand) handleList(s *discordgo.Session, i *discordgo.InteractionCreate, options []*discordgo.ApplicationCommandInteractionDataOption) error {
	// 解析参数
	var channelID string
	for _, option := range options {
		if option.Name == "channel" {
			channelID = option.ChannelValue(nil).ID
			break
		}
	}

	if channelID == "" {
		return fc.respondError(s, i, "频道是必需的")
	}

	// 获取频道的过滤规则
	rules := fc.filterService.ListRules(channelID)

	if len(rules) == 0 {
		embed := &discordgo.MessageEmbed{
			Title:       "📋 过滤规则列表",
			Description: fmt.Sprintf("频道 <#%s> 没有配置任何过滤规则", channelID),
			Color:       0xffff00,
		}

		return s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
			Type: discordgo.InteractionResponseChannelMessageWithSource,
			Data: &discordgo.InteractionResponseData{
				Embeds: []*discordgo.MessageEmbed{embed},
			},
		})
	}

	// 构建规则列表
	var description strings.Builder
	description.WriteString(fmt.Sprintf("频道 <#%s> 共有 %d 个过滤规则:\n\n", channelID, len(rules)))

	for i, rule := range rules {
		if i >= 10 { // 限制显示数量
			description.WriteString(fmt.Sprintf("... 还有 %d 个规则\n", len(rules)-10))
			break
		}

		modeIcon := "❌"
		if rule.Mode == "whitelist" {
			modeIcon = "✅"
		}

		description.WriteString(fmt.Sprintf("**%d. %s** %s\n", i+1, rule.FormatKeywordWithMode(), modeIcon))
		description.WriteString("\n")
	}

	embed := &discordgo.MessageEmbed{
		Title:       "📋 过滤规则列表",
		Description: description.String(),
		Color:       0x0099ff,
	}

	return s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Embeds: []*discordgo.MessageEmbed{embed},
		},
	})
}

// handleClear 处理清除过滤规则
func (fc *FilterCommand) handleClear(s *discordgo.Session, i *discordgo.InteractionCreate, options []*discordgo.ApplicationCommandInteractionDataOption) error {
	// 解析参数
	var channelID string
	for _, option := range options {
		if option.Name == "channel" {
			channelID = option.ChannelValue(nil).ID
			break
		}
	}

	if channelID == "" {
		return fc.respondError(s, i, "频道是必需的")
	}

	// 获取要清除的规则
	rules := fc.filterService.ListRules(channelID)

	if len(rules) == 0 {
		embed := &discordgo.MessageEmbed{
			Title:       "📋 清除过滤规则",
			Description: fmt.Sprintf("频道 <#%s> 没有需要清除的过滤规则", channelID),
			Color:       0xffff00,
		}

		return s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
			Type: discordgo.InteractionResponseChannelMessageWithSource,
			Data: &discordgo.InteractionResponseData{
				Embeds: []*discordgo.MessageEmbed{embed},
			},
		})
	}

	// 删除规则
	var removedCount int
	var errors []string

	for _, rule := range rules {
		uniqueID := rule.GetUniqueID()
		if err := fc.filterService.RemoveRule(uniqueID); err != nil {
			errors = append(errors, fmt.Sprintf("删除规则 %s 失败: %v", rule.FormatKeywordWithMode(), err))
		} else {
			removedCount++
		}
	}

	// 记录操作日志
	fc.logOperation("clear_filter_rules", fc.getUserID(i), map[string]interface{}{
		"channel":       channelID,
		"removed_count": removedCount,
		"total_rules":   len(rules),
		"errors":        len(errors),
	})

	// 响应结果
	var description strings.Builder
	description.WriteString(fmt.Sprintf("成功删除 %d 个过滤规则", removedCount))

	if len(errors) > 0 {
		description.WriteString(fmt.Sprintf("\n\n❌ %d 个规则删除失败:\n", len(errors)))
		for _, err := range errors {
			description.WriteString(fmt.Sprintf("• %s\n", err))
		}
	}

	color := 0x00ff00
	if len(errors) > 0 {
		color = 0xffaa00
	}

	embed := &discordgo.MessageEmbed{
		Title:       "🧹 清除过滤规则完成",
		Description: description.String(),
		Color:       color,
	}

	return s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Embeds: []*discordgo.MessageEmbed{embed},
		},
	})
}

// 辅助方法

// hasPermission 检查用户权限 - 已废弃，权限检查由中间件处理
// func (fc *FilterCommand) hasPermission(i *discordgo.InteractionCreate) bool {
//     // 权限检查现在由PermissionManager和中间件处理
//     return true
// }

// getUserID 获取用户ID
func (fc *FilterCommand) getUserID(i *discordgo.InteractionCreate) string {
	if i.Member != nil {
		return i.Member.User.ID
	}
	if i.User != nil {
		return i.User.ID
	}
	return "unknown"
}

// respondError 响应错误信息
func (fc *FilterCommand) respondError(s *discordgo.Session, i *discordgo.InteractionCreate, message string) error {
	embed := &discordgo.MessageEmbed{
		Title:       "❌ 错误",
		Description: message,
		Color:       0xff0000,
	}

	return s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Embeds: []*discordgo.MessageEmbed{embed},
			Flags:  discordgo.MessageFlagsEphemeral,
		},
	})
}

// logOperation 记录操作日志
func (fc *FilterCommand) logOperation(operation, userID string, data map[string]interface{}) {
	logger.Info("过滤规则操作",
		"operation", operation,
		"user_id", userID,
		"data", data,
	)
}

// GetApplicationCommand 获取应用命令定义（遵循设计文档）
func (fc *FilterCommand) GetApplicationCommand() *discordgo.ApplicationCommand {
	return &discordgo.ApplicationCommand{
		Name:        fc.name,
		Description: fc.description,
		Options: []*discordgo.ApplicationCommandOption{
			{
				Type:        discordgo.ApplicationCommandOptionSubCommand,
				Name:        "add",
				Description: "添加过滤规则（使用+/-前缀指定白名单/黑名单）",
				Options: []*discordgo.ApplicationCommandOption{
					{
						Type:        discordgo.ApplicationCommandOptionChannel,
						Name:        "channel",
						Description: "目标频道",
						Required:    true,
					},
					{
						Type:        discordgo.ApplicationCommandOptionString,
						Name:        "keyword",
						Description: "关键词（+前缀=白名单，-前缀=黑名单，如：+ABC123 或 -Funko）",
						Required:    true,
					},
				},
			},
			{
				Type:        discordgo.ApplicationCommandOptionSubCommand,
				Name:        "remove",
				Description: "删除过滤规则",
				Options: []*discordgo.ApplicationCommandOption{
					{
						Type:        discordgo.ApplicationCommandOptionChannel,
						Name:        "channel",
						Description: "频道",
						Required:    true,
					},
					{
						Type:        discordgo.ApplicationCommandOptionString,
						Name:        "keyword",
						Description: "关键词（包含+/-前缀，如：+ABC123 或 -Funko）",
						Required:    true,
					},
				},
			},
			{
				Type:        discordgo.ApplicationCommandOptionSubCommand,
				Name:        "list",
				Description: "列出过滤规则",
				Options: []*discordgo.ApplicationCommandOption{
					{
						Type:        discordgo.ApplicationCommandOptionChannel,
						Name:        "channel",
						Description: "频道",
						Required:    true,
					},
				},
			},
			{
				Type:        discordgo.ApplicationCommandOptionSubCommand,
				Name:        "clear",
				Description: "清除所有过滤规则",
				Options: []*discordgo.ApplicationCommandOption{
					{
						Type:        discordgo.ApplicationCommandOptionChannel,
						Name:        "channel",
						Description: "频道",
						Required:    true,
					},
				},
			},
		},
	}
}
