package commands

import (
	"context"
	"fmt"
	"time"

	"github.com/bwmarrin/discordgo"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"
)

// PingCommand Ping 命令实现
type PingCommand struct {
	name        string
	description string
	category    string
	cooldown    time.Duration
	permissions []string
}

// NewPingCommand 创建新的 Ping 命令
func NewPingCommand() *PingCommand {
	return &PingCommand{
		name:        "ping",
		description: "检查 Bot 的延迟和状态",
		category:    "工具",
		cooldown:    3 * time.Second,
		permissions: []string{}, // 无特殊权限要求
	}
}

// GetName 获取命令名称
func (p *PingCommand) GetName() string {
	return p.name
}

// GetDescription 获取命令描述
func (p *PingCommand) GetDescription() string {
	return p.description
}

// GetCategory 获取命令分类
func (p *PingCommand) GetCategory() string {
	return p.category
}

// GetCooldown 获取冷却时间
func (p *PingCommand) GetCooldown() time.Duration {
	return p.cooldown
}

// GetPermissions 获取所需权限
func (p *PingCommand) GetPermissions() []string {
	return p.permissions
}

// Validate 验证命令参数
func (p *PingCommand) Validate(interaction *discordgo.InteractionCreate) error {
	// Ping 命令无需参数验证
	return nil
}

// Execute 执行命令
func (p *PingCommand) Execute(ctx context.Context, client *types.Client, interaction *discordgo.InteractionCreate) error {
	startTime := time.Now()
	
	// 获取用户信息
	var user *discordgo.User
	if interaction.Member != nil {
		user = interaction.Member.User
	} else {
		user = interaction.User
	}
	
	logger.Info("执行 Ping 命令", "user", user.Username, "guild", interaction.GuildID)
	
	// 计算 WebSocket 延迟
	wsLatency := client.Session.HeartbeatLatency()
	
	// 先发送一个初始响应
	err := client.Session.InteractionRespond(interaction.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Content: "🏓 计算延迟中...",
		},
	})
	if err != nil {
		return fmt.Errorf("发送初始响应失败: %w", err)
	}
	
	// 计算 API 延迟
	apiLatency := time.Since(startTime)
	
	// 创建嵌入消息
	embed := &discordgo.MessageEmbed{
		Title: "🏓 Pong!",
		Color: 0x00ff00, // 绿色
		Fields: []*discordgo.MessageEmbedField{
			{
				Name:   "📡 WebSocket 延迟",
				Value:  fmt.Sprintf("`%v`", wsLatency.Round(time.Millisecond)),
				Inline: true,
			},
			{
				Name:   "🌐 API 延迟",
				Value:  fmt.Sprintf("`%v`", apiLatency.Round(time.Millisecond)),
				Inline: true,
			},
			{
				Name:   "⏰ 响应时间",
				Value:  fmt.Sprintf("`%v`", time.Since(startTime).Round(time.Millisecond)),
				Inline: true,
			},
		},
		Footer: &discordgo.MessageEmbedFooter{
			Text: fmt.Sprintf("请求者: %s", user.Username),
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}
	
	// 添加状态信息
	statusEmoji := "🟢"
	statusText := "正常"
	
	if wsLatency > 200*time.Millisecond {
		statusEmoji = "🟡"
		statusText = "较慢"
	}
	if wsLatency > 500*time.Millisecond {
		statusEmoji = "🔴"
		statusText = "延迟较高"
	}
	
	embed.Fields = append(embed.Fields, &discordgo.MessageEmbedField{
		Name:   "📊 连接状态",
		Value:  fmt.Sprintf("%s %s", statusEmoji, statusText),
		Inline: true,
	})
	
	// 更新响应
	_, err = client.Session.InteractionResponseEdit(interaction.Interaction, &discordgo.WebhookEdit{
		Content: nil, // 清空文本内容
		Embeds:  &[]*discordgo.MessageEmbed{embed},
	})
	if err != nil {
		return fmt.Errorf("更新响应失败: %w", err)
	}
	
	logger.Debug("Ping 命令执行完成", 
		"user", user.Username, 
		"ws_latency", wsLatency, 
		"api_latency", apiLatency)
	
	return nil
}

// GetApplicationCommand 获取应用命令定义
func (p *PingCommand) GetApplicationCommand() *discordgo.ApplicationCommand {
	return &discordgo.ApplicationCommand{
		Name:        p.name,
		Description: p.description,
		Type:        discordgo.ChatApplicationCommand,
	}
}
