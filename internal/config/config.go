package config

import (
	"bufio"
	"errors"
	"fmt"
	"os"
	"strings"
	"time"

	"zeka-go/internal/types"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

// Manager 配置管理器
type Manager struct {
	viper  *viper.Viper
	config *types.Config
}

// NewManager 创建新的配置管理器
func NewManager() *Manager {
	v := viper.New()

	// 设置配置文件搜索路径
	v.AddConfigPath("./configs")
	v.AddConfigPath("./")
	v.AddConfigPath("../configs")    // 从 cmd/zeka 目录向上查找
	v.AddConfigPath("../../configs") // 从更深层目录向上查找
	v.AddConfigPath("/app/configs")

	// 设置配置文件名和类型
	v.SetConfigName("config")
	v.SetConfigType("yaml")

	// 设置环境变量前缀
	v.SetEnvPrefix("ZEKA")
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	v.AutomaticEnv()

	// 绑定环境变量
	// Discord 配置
	v.BindEnv("discord.token", "DISCORD_TOKEN")
	v.BindEnv("discord.client_id", "DISCORD_CLIENT_ID")
	v.BindEnv("discord.guild_id", "DISCORD_GUILD_ID")
	v.BindEnv("discord.owner_id", "DISCORD_OWNER_ID")

	// Redis 配置
	v.BindEnv("redis.host", "REDIS_HOST")
	v.BindEnv("redis.port", "REDIS_PORT")
	v.BindEnv("redis.password", "REDIS_PASSWORD")
	v.BindEnv("redis.db", "REDIS_DB")

	// 数据库配置
	v.BindEnv("database.driver", "DB_DRIVER")
	v.BindEnv("database.host", "DB_HOST")
	v.BindEnv("database.port", "DB_PORT")
	v.BindEnv("database.username", "DB_USERNAME")
	v.BindEnv("database.password", "DB_PASSWORD")
	v.BindEnv("database.database", "DB_DATABASE")
	v.BindEnv("database.ssl_mode", "DB_SSL_MODE")
	v.BindEnv("database.max_idle_conns", "DB_MAX_IDLE_CONNS")
	v.BindEnv("database.max_open_conns", "DB_MAX_OPEN_CONNS")
	v.BindEnv("database.conn_max_lifetime", "DB_CONN_MAX_LIFETIME")
	v.BindEnv("database.log_level", "DB_LOG_LEVEL")
	v.BindEnv("database.enabled", "DB_ENABLED")

	// 队列配置
	v.BindEnv("queue.url", "RABBITMQ_URL")

	// API 配置
	v.BindEnv("api.port", "API_PORT")
	v.BindEnv("api.host", "API_HOST")
	v.BindEnv("api.api_key", "API_KEY")
	v.BindEnv("api.admin_key", "ADMIN_KEY")
	v.BindEnv("api.enabled", "API_ENABLED")

	// 日志配置
	v.BindEnv("logger.level", "LOG_LEVEL")
	v.BindEnv("logger.discord.enabled", "LOG_TO_DISCORD")
	v.BindEnv("logger.discord.channel_id", "LOG_CHANNEL_ID")

	return &Manager{
		viper: v,
	}
}

// Load 加载配置
func (m *Manager) Load() (*types.Config, error) {
	// 加载 .env 文件（如果存在）
	m.loadEnvFile()

	// 设置默认值
	m.setDefaults()

	// 读取配置文件
	configFileUsed := false
	if err := m.viper.ReadInConfig(); err != nil {
		// 检查是否是配置文件不存在的错误
		var configFileNotFoundError viper.ConfigFileNotFoundError
		var pathError *os.PathError
		if !errors.As(err, &configFileNotFoundError) && !errors.As(err, &pathError) {
			return nil, fmt.Errorf("读取配置文件失败: %w", err)
		}
		// 配置文件不存在，使用默认值和环境变量
		fmt.Printf("配置文件未找到，使用默认值和环境变量\n")
	} else {
		configFileUsed = true
		fmt.Printf("已加载配置文件: %s\n", m.viper.ConfigFileUsed())
	}

	// 解析配置到结构体
	config := &types.Config{}
	if err := m.viper.Unmarshal(config); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}

	// 加载额外的配置文件
	if err := m.loadAdditionalConfigs(config); err != nil {
		return nil, fmt.Errorf("加载额外配置失败: %w", err)
	}

	// 处理 JSON 配置（如果有）
	if err := m.processJSONConfig(config); err != nil {
		return nil, fmt.Errorf("处理 JSON 配置失败: %w", err)
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	// 输出配置加载状态
	m.logConfigStatus(config, configFileUsed)

	m.config = config
	return config, nil
}

// setDefaults 设置默认配置值
func (m *Manager) setDefaults() {
	// 基础配置
	m.viper.SetDefault("environment", "development")

	// Discord 配置
	m.viper.SetDefault("discord.token", "")
	m.viper.SetDefault("discord.client_id", "")
	m.viper.SetDefault("discord.prefix", "!")
	m.viper.SetDefault("discord.guild_id", "")
	m.viper.SetDefault("discord.owner_id", "")
	m.viper.SetDefault("discord.owners", []string{})

	// Redis 配置
	m.viper.SetDefault("redis.host", "localhost")
	m.viper.SetDefault("redis.port", 6379)
	m.viper.SetDefault("redis.password", "")
	m.viper.SetDefault("redis.db", 0)
	m.viper.SetDefault("redis.prefix", "zeka:")

	// 队列配置
	m.viper.SetDefault("queue.url", "amqp://127.0.0.1:5672") // 强制IPv4连接
	m.viper.SetDefault("queue.prefetch_count", 10)
	m.viper.SetDefault("queue.max_retries", 3)
	m.viper.SetDefault("queue.enable_monitoring", false)
	m.viper.SetDefault("queue.heartbeat_interval", "60s")
	m.viper.SetDefault("queue.reconnect_delay", "5s")
	m.viper.SetDefault("queue.connection_timeout", "10s")
	m.viper.SetDefault("queue.enable_graceful_degradation", true)
	m.viper.SetDefault("queue.required_for_startup", false)

	// 默认交换机配置
	m.viper.SetDefault("queue.default_exchange.name", "zeka.direct")
	m.viper.SetDefault("queue.default_exchange.type", "direct")
	m.viper.SetDefault("queue.default_exchange.options.durable", true)
	m.viper.SetDefault("queue.default_exchange.options.auto_delete", false)

	// 数据库配置
	m.viper.SetDefault("database.driver", "sqlite")
	m.viper.SetDefault("database.host", "localhost")
	m.viper.SetDefault("database.port", 3306)
	m.viper.SetDefault("database.username", "")
	m.viper.SetDefault("database.password", "")
	m.viper.SetDefault("database.database", "zeka.db")
	m.viper.SetDefault("database.ssl_mode", "disable")
	m.viper.SetDefault("database.max_idle_conns", 10)
	m.viper.SetDefault("database.max_open_conns", 100)
	m.viper.SetDefault("database.conn_max_lifetime", "1h")
	m.viper.SetDefault("database.log_level", "warn")
	m.viper.SetDefault("database.enabled", true)

	// 调度器配置
	m.viper.SetDefault("scheduler.workers", 5)
	m.viper.SetDefault("scheduler.queue_size", 1000)
	m.viper.SetDefault("scheduler.job_timeout", "30s")
	m.viper.SetDefault("scheduler.max_retries", 3)
	m.viper.SetDefault("scheduler.cleanup_interval", "1h")
	m.viper.SetDefault("scheduler.enabled", true)

	// API 配置
	m.viper.SetDefault("api.port", 8080)
	m.viper.SetDefault("api.host", "0.0.0.0")
	m.viper.SetDefault("api.api_key", "")
	m.viper.SetDefault("api.admin_key", "")
	m.viper.SetDefault("api.enabled", true)

	// 日志配置
	m.viper.SetDefault("logger.level", "info")
	m.viper.SetDefault("logger.format", "json")
	m.viper.SetDefault("logger.discord.enabled", false)
	m.viper.SetDefault("logger.discord.channel_id", "")
	m.viper.SetDefault("logger.discord.level", "error")
	m.viper.SetDefault("logger.file.enabled", true)
	m.viper.SetDefault("logger.file.path", "./logs")
	m.viper.SetDefault("logger.file.max_size", 100)
	m.viper.SetDefault("logger.file.max_backups", 3)
	m.viper.SetDefault("logger.file.max_age", 28)
	m.viper.SetDefault("logger.file.compress", true)

	// 颜色配置
	m.viper.SetDefault("colors.primary", "#3498db")
	m.viper.SetDefault("colors.success", "#2ecc71")
	m.viper.SetDefault("colors.warning", "#f39c12")
	m.viper.SetDefault("colors.error", "#e74c3c")
	m.viper.SetDefault("colors.info", "#9b59b6")

	// 冷却时间配置
	m.viper.SetDefault("cooldowns.default", "3s")
	m.viper.SetDefault("cooldowns.commands", map[string]string{})

	// 状态消息配置
	m.viper.SetDefault("status_messages", []map[string]string{
		{
			"type":    "Playing",
			"content": "与 Discord 用户互动",
		},
	})

	// 表情反应过滤配置
	m.viper.SetDefault("reaction_filter.enabled", true)
	m.viper.SetDefault("reaction_filter.whitelist_emoji", "✅")
	m.viper.SetDefault("reaction_filter.blacklist_emoji", "❌")
	m.viper.SetDefault("reaction_filter.admin_roles", []string{})
	m.viper.SetDefault("reaction_filter.admin_users", []string{})

}

// loadAdditionalConfigs 加载额外的配置文件
func (m *Manager) loadAdditionalConfigs(config *types.Config) error {
	// 加载权限配置
	if err := m.loadPermissionsConfig(config); err != nil {
		return fmt.Errorf("加载权限配置失败: %w", err)
	}

	return nil
}

// loadPermissionsConfig 加载权限配置文件
func (m *Manager) loadPermissionsConfig(config *types.Config) error {
	// 尝试加载 permissions.yaml 文件
	permissionsViper := viper.New()
	permissionsViper.AddConfigPath("./configs")
	permissionsViper.AddConfigPath("./")
	permissionsViper.AddConfigPath("../configs")    // 从 cmd/zeka 目录向上查找
	permissionsViper.AddConfigPath("../../configs") // 从更深层目录向上查找
	permissionsViper.AddConfigPath("/app/configs")
	permissionsViper.SetConfigName("permissions")
	permissionsViper.SetConfigType("yaml")

	if err := permissionsViper.ReadInConfig(); err != nil {
		// 如果文件不存在，不是错误，使用默认配置
		var configFileNotFoundError viper.ConfigFileNotFoundError
		var pathError *os.PathError
		if errors.As(err, &configFileNotFoundError) || errors.As(err, &pathError) {
			fmt.Printf("权限配置文件未找到，使用默认权限设置\n")
			// 设置默认权限配置
			config.Permissions = types.PermissionConfig{
				EnablePermissionCheck: false,
				CommandPermissions:    make(map[string]types.CommandPermission),
			}
			return nil
		}
		return fmt.Errorf("读取权限配置文件失败: %w", err)
	}

	fmt.Printf("已加载权限配置文件: %s\n", permissionsViper.ConfigFileUsed())

	// 解析权限配置
	var permissionsConfig types.PermissionConfig
	if err := permissionsViper.Unmarshal(&permissionsConfig); err != nil {
		return fmt.Errorf("解析权限配置失败: %w", err)
	}

	// 合并到主配置中
	config.Permissions = permissionsConfig

	return nil
}

// processJSONConfig 处理 JSON 格式的配置
func (m *Manager) processJSONConfig(config *types.Config) error {
	// 处理冷却时间配置
	if err := m.processCooldownConfig(config); err != nil {
		return err
	}

	// 处理时间间隔配置
	if err := m.processTimeIntervals(config); err != nil {
		return err
	}

	return nil
}

// processCooldownConfig 处理冷却时间配置
func (m *Manager) processCooldownConfig(config *types.Config) error {
	// 解析默认冷却时间
	if defaultStr := m.viper.GetString("cooldowns.default"); defaultStr != "" {
		duration, err := time.ParseDuration(defaultStr)
		if err != nil {
			return fmt.Errorf("解析默认冷却时间失败: %w", err)
		}
		config.Cooldowns.Default = duration
	}

	// 解析命令冷却时间
	commandCooldowns := m.viper.GetStringMapString("cooldowns.commands")
	config.Cooldowns.Commands = make(map[string]time.Duration)

	for command, durationStr := range commandCooldowns {
		duration, err := time.ParseDuration(durationStr)
		if err != nil {
			return fmt.Errorf("解析命令 %s 冷却时间失败: %w", command, err)
		}
		config.Cooldowns.Commands[command] = duration
	}

	return nil
}

// processTimeIntervals 处理时间间隔配置
func (m *Manager) processTimeIntervals(config *types.Config) error {
	// 解析队列相关时间间隔
	if heartbeatStr := m.viper.GetString("queue.heartbeat_interval"); heartbeatStr != "" {
		duration, err := time.ParseDuration(heartbeatStr)
		if err != nil {
			return fmt.Errorf("解析心跳间隔失败: %w", err)
		}
		config.Queue.HeartbeatInterval = duration
	}

	if reconnectStr := m.viper.GetString("queue.reconnect_delay"); reconnectStr != "" {
		duration, err := time.ParseDuration(reconnectStr)
		if err != nil {
			return fmt.Errorf("解析重连延迟失败: %w", err)
		}
		config.Queue.ReconnectDelay = duration
	}

	if timeoutStr := m.viper.GetString("queue.connection_timeout"); timeoutStr != "" {
		duration, err := time.ParseDuration(timeoutStr)
		if err != nil {
			return fmt.Errorf("解析连接超时失败: %w", err)
		}
		config.Queue.ConnectionTimeout = duration
	}

	return nil
}

// Reload 重新加载配置
func (m *Manager) Reload() error {
	config, err := m.Load()
	if err != nil {
		return err
	}
	m.config = config
	return nil
}

// Get 获取当前配置
func (m *Manager) Get() *types.Config {
	return m.config
}

// Watch 监听配置文件变化
func (m *Manager) Watch(callback func(*types.Config)) {
	m.viper.WatchConfig()
	m.viper.OnConfigChange(func(e fsnotify.Event) {
		if err := m.Reload(); err != nil {
			// 记录错误，但不中断程序
			return
		}
		if callback != nil {
			callback(m.config)
		}
	})
}

// GetString 获取字符串配置值
func (m *Manager) GetString(key string) string {
	return m.viper.GetString(key)
}

// GetInt 获取整数配置值
func (m *Manager) GetInt(key string) int {
	return m.viper.GetInt(key)
}

// GetBool 获取布尔配置值
func (m *Manager) GetBool(key string) bool {
	return m.viper.GetBool(key)
}

// GetDuration 获取时间间隔配置值
func (m *Manager) GetDuration(key string) time.Duration {
	return m.viper.GetDuration(key)
}

// GetViper 获取底层的 viper 实例（用于测试）
func (m *Manager) GetViper() *viper.Viper {
	return m.viper
}

// loadEnvFile 加载 .env 文件中的环境变量
func (m *Manager) loadEnvFile() {
	envFiles := []string{".env", ".env.local", ".env.development"}

	for _, envFile := range envFiles {
		if _, err := os.Stat(envFile); err == nil {
			fmt.Printf("正在加载环境变量文件: %s\n", envFile)
			if err := m.loadEnvFromFile(envFile); err != nil {
				fmt.Printf("警告: 加载 %s 文件失败: %v\n", envFile, err)
			} else {
				fmt.Printf("成功加载环境变量文件: %s\n", envFile)
			}
		}
	}
}

// loadEnvFromFile 从指定文件加载环境变量
func (m *Manager) loadEnvFromFile(filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// 跳过空行和注释行
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// 解析 KEY=VALUE 格式
		parts := strings.SplitN(line, "=", 2)
		if len(parts) != 2 {
			continue
		}

		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])

		// 移除值两端的引号（如果有）
		if len(value) >= 2 {
			if (strings.HasPrefix(value, "\"") && strings.HasSuffix(value, "\"")) ||
				(strings.HasPrefix(value, "'") && strings.HasSuffix(value, "'")) {
				value = value[1 : len(value)-1]
			}
		}

		// 只有当环境变量不存在时才设置
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}

	return scanner.Err()
}

// logConfigStatus 输出配置加载状态信息
func (m *Manager) logConfigStatus(config *types.Config, configFileUsed bool) {
	fmt.Printf("=== 配置加载状态 ===\n")
	fmt.Printf("环境: %s\n", config.Environment)
	fmt.Printf("配置文件: %v\n", configFileUsed)

	// 检查关键配置项的来源
	if config.Discord.Token != "" {
		fmt.Printf("Discord Token: 已配置\n")
	} else {
		fmt.Printf("Discord Token: 未配置 (需要设置 DISCORD_TOKEN 环境变量)\n")
	}

	if config.Discord.ClientID != "" {
		fmt.Printf("Discord Client ID: 已配置\n")
	} else {
		fmt.Printf("Discord Client ID: 未配置 (需要设置 DISCORD_CLIENT_ID 环境变量)\n")
	}

	fmt.Printf("Redis: %s:%d (启用: %v)\n", config.Redis.Host, config.Redis.Port, config.Redis.IsEnabled())
	fmt.Printf("队列: %s (启用: %v)\n", config.Queue.URL, config.Queue.IsEnabled())
	fmt.Printf("API: %s:%d (启用: %v)\n", config.API.Host, config.API.Port, config.API.Enabled)
	fmt.Printf("==================\n")
}

// GetConfigSource 获取配置项的来源信息
func (m *Manager) GetConfigSource(key string) string {
	// 检查是否来自环境变量
	if m.viper.IsSet(key) {
		// 尝试获取环境变量
		envKey := strings.ToUpper(strings.ReplaceAll("ZEKA_"+key, ".", "_"))
		if os.Getenv(envKey) != "" {
			return "环境变量"
		}

		// 检查是否来自配置文件
		if m.viper.ConfigFileUsed() != "" {
			return "配置文件"
		}
	}

	return "默认值"
}

// ValidateEnvironment 验证环境配置
func (m *Manager) ValidateEnvironment() []string {
	var warnings []string

	// 检查必需的环境变量
	requiredEnvVars := []string{
		"DISCORD_TOKEN",
		"DISCORD_CLIENT_ID",
	}

	for _, envVar := range requiredEnvVars {
		if os.Getenv(envVar) == "" {
			warnings = append(warnings, fmt.Sprintf("环境变量 %s 未设置", envVar))
		}
	}

	return warnings
}

// 全局配置管理器实例
var globalManager *Manager

// Load 加载全局配置
func Load() (*types.Config, error) {
	if globalManager == nil {
		globalManager = NewManager()
	}
	return globalManager.Load()
}

// Get 获取全局配置
func Get() *types.Config {
	if globalManager == nil {
		return nil
	}
	return globalManager.Get()
}

// Reload 重新加载全局配置
func Reload() error {
	if globalManager == nil {
		globalManager = NewManager()
	}
	return globalManager.Reload()
}
