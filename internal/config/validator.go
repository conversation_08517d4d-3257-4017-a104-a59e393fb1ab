package config

import (
	"fmt"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"zeka-go/internal/types"
)

// Validator 配置验证器
type Validator struct{}

// NewValidator 创建新的验证器
func NewValidator() *Validator {
	return &Validator{}
}

// ValidateConfig 验证完整配置
func (v *Validator) ValidateConfig(config *types.Config) error {
	if err := v.ValidateDiscordConfig(config.Discord); err != nil {
		return fmt.Errorf("discord 配置验证失败: %w", err)
	}

	if config.Redis.IsEnabled() {
		if err := v.ValidateRedisConfig(config.Redis); err != nil {
			return fmt.Errorf("redis 配置验证失败: %w", err)
		}
	}

	if config.Queue.IsEnabled() {
		if err := v.ValidateQueueConfig(config.Queue); err != nil {
			return fmt.Errorf("队列配置验证失败: %w", err)
		}
	}

	if err := v.ValidateLoggerConfig(config.Logger); err != nil {
		return fmt.Errorf("日志配置验证失败: %w", err)
	}

	if err := v.ValidateCooldownConfig(config.Cooldowns); err != nil {
		return fmt.Errorf("冷却时间配置验证失败: %w", err)
	}

	if err := v.ValidateColorConfig(config.Colors); err != nil {
		return fmt.Errorf("颜色配置验证失败: %w", err)
	}

	if err := v.ValidateStatusMessages(config.StatusMessages); err != nil {
		return fmt.Errorf("状态消息配置验证失败: %w", err)
	}

	return nil
}

// ValidateDiscordConfig 验证 Discord 配置
func (v *Validator) ValidateDiscordConfig(config types.DiscordConfig) error {
	if config.Token == "" {
		return types.ErrMissingDiscordToken
	}

	if config.ClientID == "" {
		return types.ErrMissingClientID
	}

	// 验证 Token 格式
	if !v.isValidDiscordToken(config.Token) {
		return types.NewValidationError("token", config.Token, "无效的 Discord Token 格式")
	}

	// 验证 Client ID 格式
	if !v.isValidSnowflake(config.ClientID) {
		return types.NewValidationError("client_id", config.ClientID, "无效的 Discord Client ID 格式")
	}

	// 验证 Guild ID 格式（如果提供）
	if config.GuildID != "" && !v.isValidSnowflake(config.GuildID) {
		return types.NewValidationError("guild_id", config.GuildID, "无效的 Discord Guild ID 格式")
	}

	// 验证 Owner ID 格式（如果提供）
	if config.OwnerID != "" && !v.isValidSnowflake(config.OwnerID) {
		return types.NewValidationError("owner_id", config.OwnerID, "无效的 Discord Owner ID 格式")
	}

	// 验证 Owners 列表
	for i, ownerID := range config.Owners {
		if !v.isValidSnowflake(ownerID) {
			return types.NewValidationError(fmt.Sprintf("owners[%d]", i), ownerID, "无效的 Discord Owner ID 格式")
		}
	}

	// 验证前缀
	if len(config.Prefix) > 5 {
		return types.NewValidationError("prefix", config.Prefix, "前缀长度不能超过5个字符")
	}

	return nil
}

// ValidateRedisConfig 验证 Redis 配置
func (v *Validator) ValidateRedisConfig(config types.RedisConfig) error {
	if config.Host == "" {
		return types.NewValidationError("host", config.Host, "Redis 主机地址不能为空")
	}

	if config.Port <= 0 || config.Port > 65535 {
		return types.NewValidationError("port", config.Port, "Redis 端口必须在 1-65535 范围内")
	}

	if config.DB < 0 || config.DB > 15 {
		return types.NewValidationError("db", config.DB, "Redis 数据库索引必须在 0-15 范围内")
	}

	return nil
}

// ValidateQueueConfig 验证队列配置
func (v *Validator) ValidateQueueConfig(config types.QueueConfig) error {
	if config.URL == "" {
		return types.NewValidationError("url", config.URL, "队列 URL 不能为空")
	}

	// 验证 URL 格式
	if _, err := url.Parse(config.URL); err != nil {
		return types.NewValidationError("url", config.URL, "无效的队列 URL 格式")
	}

	if config.PrefetchCount <= 0 {
		return types.NewValidationError("prefetch_count", config.PrefetchCount, "预取数量必须大于 0")
	}

	if config.MaxRetries < 0 {
		return types.NewValidationError("max_retries", config.MaxRetries, "最大重试次数不能为负数")
	}

	// 验证时间间隔
	if config.HeartbeatInterval <= 0 {
		return types.NewValidationError("heartbeat_interval", config.HeartbeatInterval, "心跳间隔必须大于 0")
	}

	if config.ReconnectDelay <= 0 {
		return types.NewValidationError("reconnect_delay", config.ReconnectDelay, "重连延迟必须大于 0")
	}

	if config.ConnectionTimeout <= 0 {
		return types.NewValidationError("connection_timeout", config.ConnectionTimeout, "连接超时必须大于 0")
	}

	// 验证交换机配置
	if config.DefaultExchange.Name == "" {
		return types.NewValidationError("default_exchange.name", config.DefaultExchange.Name, "默认交换机名称不能为空")
	}

	validExchangeTypes := []string{"direct", "fanout", "topic", "headers"}
	if !v.contains(validExchangeTypes, config.DefaultExchange.Type) {
		return types.NewValidationError("default_exchange.type", config.DefaultExchange.Type, "无效的交换机类型")
	}

	// 验证优雅降级配置
	if config.RequiredForStartup && !config.EnableGracefulDegradation {
		return types.NewValidationError("required_for_startup", config.RequiredForStartup,
			"当队列服务为启动必需时，建议启用优雅降级以提高可用性")
	}

	return nil
}

// ValidateLoggerConfig 验证日志配置
func (v *Validator) ValidateLoggerConfig(config types.LoggerConfig) error {
	validLevels := []string{"debug", "info", "warn", "error", "fatal"}
	if !v.contains(validLevels, strings.ToLower(config.Level)) {
		return types.NewValidationError("level", config.Level, "无效的日志级别")
	}

	validFormats := []string{"json", "console"}
	if !v.contains(validFormats, strings.ToLower(config.Format)) {
		return types.NewValidationError("format", config.Format, "无效的日志格式")
	}

	// 验证 Discord 日志配置
	if config.Discord.Enabled {
		if config.Discord.ChannelID == "" {
			return types.NewValidationError("discord.channel_id", config.Discord.ChannelID, "启用 Discord 日志时频道 ID 不能为空")
		}

		if !v.isValidSnowflake(config.Discord.ChannelID) {
			return types.NewValidationError("discord.channel_id", config.Discord.ChannelID, "无效的 Discord 频道 ID 格式")
		}

		if !v.contains(validLevels, strings.ToLower(config.Discord.Level)) {
			return types.NewValidationError("discord.level", config.Discord.Level, "无效的 Discord 日志级别")
		}
	}

	// 验证文件日志配置
	if config.File.Enabled {
		if config.File.MaxSize <= 0 {
			return types.NewValidationError("file.max_size", config.File.MaxSize, "文件最大大小必须大于 0")
		}

		if config.File.MaxBackups < 0 {
			return types.NewValidationError("file.max_backups", config.File.MaxBackups, "最大备份数不能为负数")
		}

		if config.File.MaxAge < 0 {
			return types.NewValidationError("file.max_age", config.File.MaxAge, "最大保存天数不能为负数")
		}
	}

	return nil
}

// ValidateCooldownConfig 验证冷却时间配置
func (v *Validator) ValidateCooldownConfig(config types.CooldownConfig) error {
	if config.Default <= 0 {
		return types.NewValidationError("default", config.Default, "默认冷却时间必须大于 0")
	}

	if config.Default > 24*time.Hour {
		return types.NewValidationError("default", config.Default, "默认冷却时间不能超过 24 小时")
	}

	for command, cooldown := range config.Commands {
		if cooldown <= 0 {
			return types.NewValidationError(fmt.Sprintf("commands.%s", command), cooldown, "命令冷却时间必须大于 0")
		}

		if cooldown > 24*time.Hour {
			return types.NewValidationError(fmt.Sprintf("commands.%s", command), cooldown, "命令冷却时间不能超过 24 小时")
		}
	}

	return nil
}

// ValidateColorConfig 验证颜色配置
func (v *Validator) ValidateColorConfig(config types.ColorConfig) error {
	colors := map[string]string{
		"primary": config.Primary,
		"success": config.Success,
		"warning": config.Warning,
		"error":   config.Error,
		"info":    config.Info,
	}

	for name, color := range colors {
		if !v.isValidHexColor(color) {
			return types.NewValidationError(name, color, "无效的十六进制颜色格式")
		}
	}

	return nil
}

// ValidateStatusMessages 验证状态消息配置
func (v *Validator) ValidateStatusMessages(messages []types.StatusMessage) error {
	if len(messages) == 0 {
		return types.NewValidationError("status_messages", messages, "至少需要一个状态消息")
	}

	validTypes := []string{"Playing", "Listening", "Watching", "Competing"}

	for i, message := range messages {
		if !v.contains(validTypes, message.Type) {
			return types.NewValidationError(fmt.Sprintf("status_messages[%d].type", i), message.Type, "无效的状态消息类型")
		}

		if message.Content == "" {
			return types.NewValidationError(fmt.Sprintf("status_messages[%d].content", i), message.Content, "状态消息内容不能为空")
		}

		if len(message.Content) > 128 {
			return types.NewValidationError(fmt.Sprintf("status_messages[%d].content", i), message.Content, "状态消息内容不能超过 128 个字符")
		}
	}

	return nil
}

// 辅助验证方法

// isValidDiscordToken 验证 Discord Token 格式
func (v *Validator) isValidDiscordToken(token string) bool {
	// Discord Bot Token 通常以 Bot 开头，后跟 base64 编码的字符串
	if after, ok := strings.CutPrefix(token, "Bot "); ok {
		token = after
	}

	// 基本长度检查
	return len(token) >= 50 && len(token) <= 100
}

// isValidSnowflake 验证 Discord Snowflake ID 格式
func (v *Validator) isValidSnowflake(id string) bool {
	// Discord Snowflake 是 64 位整数，通常 17-19 位数字
	if len(id) < 17 || len(id) > 19 {
		return false
	}

	// 检查是否为纯数字
	if _, err := strconv.ParseUint(id, 10, 64); err != nil {
		return false
	}

	return true
}

// isValidHexColor 验证十六进制颜色格式
func (v *Validator) isValidHexColor(color string) bool {
	// 匹配 #RRGGBB 或 #RGB 格式
	hexColorRegex := regexp.MustCompile(`^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$`)
	return hexColorRegex.MatchString(color)
}

// contains 检查切片是否包含指定元素
func (v *Validator) contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// 全局验证器实例
var globalValidator = NewValidator()

// ValidateConfig 验证配置（全局函数）
func ValidateConfig(config *types.Config) error {
	return globalValidator.ValidateConfig(config)
}
