package handlers

import (
	"context"
	"fmt"
	"runtime"
	"strings"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// ErrorMiddleware 错误处理中间件
type ErrorMiddleware struct {
	config           *types.Config
	notificationChan chan *ErrorNotification
	errorStats       *ErrorStats
}

// ErrorNotification 错误通知
type ErrorNotification struct {
	Error     error
	Context   map[string]interface{}
	Timestamp time.Time
	Severity  ErrorSeverity
}

// ErrorSeverity 错误严重程度
type ErrorSeverity string

const (
	SeverityLow      ErrorSeverity = "low"
	SeverityMedium   ErrorSeverity = "medium"
	SeverityHigh     ErrorSeverity = "high"
	SeverityCritical ErrorSeverity = "critical"
)

// ErrorStats 错误统计
type ErrorStats struct {
	TotalErrors  int64
	ErrorsByType map[string]int64
	ErrorsByCode map[types.ErrorCode]int64
	LastError    time.Time
	ErrorRate    float64
}

// NewErrorMiddleware 创建错误处理中间件
func NewErrorMiddleware(config *types.Config) *ErrorMiddleware {
	return &ErrorMiddleware{
		config:           config,
		notificationChan: make(chan *ErrorNotification, 100),
		errorStats: &ErrorStats{
			ErrorsByType: make(map[string]int64),
			ErrorsByCode: make(map[types.ErrorCode]int64),
		},
	}
}

// Process 处理错误
func (m *ErrorMiddleware) Process(ctx context.Context, next func(context.Context) error) error {
	defer func() {
		if r := recover(); r != nil {
			// 处理panic
			err := m.handlePanic(ctx, r)
			m.logError(ctx, err, SeverityCritical)
		}
	}()

	err := next(ctx)
	if err != nil {
		severity := m.determineSeverity(err)
		m.logError(ctx, err, severity)
		m.updateStats(err)

		// 发送错误通知
		m.sendErrorNotification(ctx, err, severity)

		// 根据错误类型决定是否继续传播错误
		if m.shouldPropagateError(err) {
			return err
		}
	}

	return nil
}

// handlePanic 处理panic
func (m *ErrorMiddleware) handlePanic(ctx context.Context, r interface{}) error {
	// 获取堆栈信息
	buf := make([]byte, 4096)
	n := runtime.Stack(buf, false)
	stack := string(buf[:n])

	// 创建panic错误
	panicErr := &types.BotError{
		Code:    "PANIC",
		Message: fmt.Sprintf("系统发生panic: %v", r),
		Cause:   fmt.Errorf("%v", r),
	}

	// 记录详细的panic信息
	logger.Error("系统发生panic",
		"panic", r,
		"stack", stack,
		"context", m.extractContextInfo(ctx))

	return panicErr
}

// determineSeverity 确定错误严重程度
func (m *ErrorMiddleware) determineSeverity(err error) ErrorSeverity {
	switch e := err.(type) {
	case *types.BotError:
		if e.Code == "PANIC" {
			return SeverityCritical
		}
		return SeverityHigh
	case *types.ServiceError:
		if strings.Contains(e.Service, "discord") {
			return SeverityHigh
		}
		return SeverityMedium
	case *types.TaskError:
		if e.Code == types.ErrCodeTaskTimeout {
			return SeverityMedium
		}
		return SeverityLow
	case *types.NetworkError:
		if e.Status >= 500 {
			return SeverityHigh
		}
		return SeverityMedium
	case *types.DiscordError:
		if e.Code == types.ErrCodeDiscordAuth {
			return SeverityCritical
		}
		return SeverityHigh
	case *types.ConfigError:
		return SeverityCritical
	default:
		// 检查是否是已知的错误类型
		switch err {
		case types.ErrBotNotStarted, types.ErrBotAlreadyStarted:
			return SeverityMedium
		case types.ErrCommandCooldown, types.ErrInsufficientPermissions:
			return SeverityLow
		case types.ErrServiceNotAvailable, types.ErrCacheNotAvailable, types.ErrQueueNotAvailable:
			return SeverityHigh
		default:
			return SeverityMedium
		}
	}
}

// logError 记录错误
func (m *ErrorMiddleware) logError(ctx context.Context, err error, severity ErrorSeverity) {
	contextInfo := m.extractContextInfo(ctx)

	logFields := []interface{}{
		"error", err.Error(),
		"severity", severity,
		"type", fmt.Sprintf("%T", err),
		"timestamp", time.Now(),
	}

	// 添加上下文信息
	for key, value := range contextInfo {
		logFields = append(logFields, key, value)
	}

	// 添加错误特定信息
	switch e := err.(type) {
	case *types.TaskError:
		logFields = append(logFields, "task_id", e.TaskID, "task_type", e.TaskType, "error_code", e.Code)
	case *types.ServiceError:
		logFields = append(logFields, "service", e.Service, "operation", e.Operation)
	case *types.DiscordError:
		logFields = append(logFields, "guild_id", e.GuildID, "channel_id", e.ChannelID, "user_id", e.UserID)
	case *types.NetworkError:
		logFields = append(logFields, "url", e.URL, "method", e.Method, "status", e.Status, "duration", e.Duration)
	}

	// 根据严重程度选择日志级别
	switch severity {
	case SeverityLow:
		logger.Debug("错误处理", logFields...)
	case SeverityMedium:
		logger.Warn("错误处理", logFields...)
	case SeverityHigh:
		logger.Error("错误处理", logFields...)
	case SeverityCritical:
		logger.Error("严重错误", logFields...)
	}
}

// extractContextInfo 提取上下文信息
func (m *ErrorMiddleware) extractContextInfo(ctx context.Context) map[string]interface{} {
	info := make(map[string]interface{})

	// 提取常见的上下文信息
	if userID := ctx.Value("user_id"); userID != nil {
		info["user_id"] = userID
	}
	if guildID := ctx.Value("guild_id"); guildID != nil {
		info["guild_id"] = guildID
	}
	if channelID := ctx.Value("channel_id"); channelID != nil {
		info["channel_id"] = channelID
	}
	if commandName := ctx.Value("command_name"); commandName != nil {
		info["command_name"] = commandName
	}
	if interaction := ctx.Value("interaction"); interaction != nil {
		if i, ok := interaction.(*discordgo.InteractionCreate); ok {
			info["interaction_id"] = i.ID
			info["interaction_type"] = i.Type
		}
	}

	return info
}

// updateStats 更新错误统计
func (m *ErrorMiddleware) updateStats(err error) {
	m.errorStats.TotalErrors++
	m.errorStats.LastError = time.Now()

	// 按类型统计
	errorType := fmt.Sprintf("%T", err)
	m.errorStats.ErrorsByType[errorType]++

	// 按错误码统计
	if codeErr, ok := err.(interface{ GetCode() types.ErrorCode }); ok {
		m.errorStats.ErrorsByCode[codeErr.GetCode()]++
	}

	// 计算错误率（简单实现）
	// 这里可以根据需要实现更复杂的错误率计算逻辑
}

// sendErrorNotification 发送错误通知
func (m *ErrorMiddleware) sendErrorNotification(ctx context.Context, err error, severity ErrorSeverity) {
	// 只对高严重程度的错误发送通知
	if severity == SeverityHigh || severity == SeverityCritical {
		notification := &ErrorNotification{
			Error:     err,
			Context:   m.extractContextInfo(ctx),
			Timestamp: time.Now(),
			Severity:  severity,
		}

		// 非阻塞发送
		select {
		case m.notificationChan <- notification:
		default:
			logger.Warn("错误通知队列已满，跳过通知")
		}
	}
}

// shouldPropagateError 判断是否应该继续传播错误
func (m *ErrorMiddleware) shouldPropagateError(err error) bool {
	// 某些错误类型不需要继续传播
	switch err {
	case types.ErrCommandCooldown, types.ErrInsufficientPermissions:
		return false
	}

	// 检查错误类型
	switch err.(type) {
	case *types.TaskError:
		// 任务错误通常不需要传播到上层
		return false
	case *types.DiscordError:
		// Discord API错误需要传播
		return true
	case *types.ServiceError:
		// 服务错误需要传播
		return true
	default:
		// 默认传播错误
		return true
	}
}

// GetStats 获取错误统计信息
func (m *ErrorMiddleware) GetStats() *ErrorStats {
	return m.errorStats
}

// GetNotificationChannel 获取错误通知通道
func (m *ErrorMiddleware) GetNotificationChannel() <-chan *ErrorNotification {
	return m.notificationChan
}
