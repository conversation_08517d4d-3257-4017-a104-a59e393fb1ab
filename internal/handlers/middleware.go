package handlers

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// LoggingMiddleware 日志中间件
type LoggingMiddleware struct{}

// NewLoggingMiddleware 创建日志中间件
func NewLoggingMiddleware() *LoggingMiddleware {
	return &LoggingMiddleware{}
}

// Process 处理请求
func (m *LoggingMiddleware) Process(ctx context.Context, next func(context.Context) error) error {
	start := time.Now()

	// 从上下文获取信息
	userID := ctx.Value("user_id")
	guildID := ctx.Value("guild_id")
	commandName := ctx.Value("command_name")

	logger.Info("命令开始执行",
		"command", commandName,
		"user", userID,
		"guild", guildID,
		"start_time", start)

	// 执行下一个中间件或处理器
	err := next(ctx)

	duration := time.Since(start)

	if err != nil {
		logger.Error("命令执行失败",
			"command", commandName,
			"user", userID,
			"guild", guildID,
			"duration", duration,
			"error", err)
	} else {
		logger.Info("命令执行成功",
			"command", commandName,
			"user", userID,
			"guild", guildID,
			"duration", duration)
	}

	return err
}

// PermissionMiddleware 权限验证中间件
type PermissionMiddleware struct {
	config *types.Config
}

// NewPermissionMiddleware 创建权限验证中间件
func NewPermissionMiddleware(config *types.Config) *PermissionMiddleware {
	return &PermissionMiddleware{
		config: config,
	}
}

// Process 处理权限验证
func (m *PermissionMiddleware) Process(ctx context.Context, next func(context.Context) error) error {
	// 从上下文获取信息
	userID, _ := ctx.Value("user_id").(string)
	guildID, _ := ctx.Value("guild_id").(string)
	command, _ := ctx.Value("command").(types.Command)
	interaction, _ := ctx.Value("interaction").(*discordgo.InteractionCreate)

	if command == nil || interaction == nil {
		return fmt.Errorf("缺少命令或交互信息")
	}

	// 检查是否为 Bot 所有者
	if m.isOwner(userID) {
		logger.Debug("Bot 所有者执行命令，跳过权限检查", "user", userID, "command", command.GetName())
		return next(ctx)
	}

	// 获取命令所需权限
	requiredPermissions := command.GetPermissions()
	if len(requiredPermissions) == 0 {
		// 无特殊权限要求
		return next(ctx)
	}

	// 检查用户权限
	if err := m.checkPermissions(interaction, requiredPermissions, guildID); err != nil {
		logger.Warn("权限验证失败",
			"user", userID,
			"command", command.GetName(),
			"required_permissions", requiredPermissions,
			"error", err)
		return types.ErrInsufficientPermissions
	}

	logger.Debug("权限验证通过",
		"user", userID,
		"command", command.GetName(),
		"permissions", requiredPermissions)

	return next(ctx)
}

// isOwner 检查是否为 Bot 所有者
func (m *PermissionMiddleware) isOwner(userID string) bool {
	// 检查主所有者
	if m.config.Discord.OwnerID == userID {
		return true
	}

	// 检查所有者列表
	for _, ownerID := range m.config.Discord.Owners {
		if ownerID == userID {
			return true
		}
	}

	return false
}

// checkPermissions 检查用户权限
func (m *PermissionMiddleware) checkPermissions(interaction *discordgo.InteractionCreate, requiredPermissions []string, guildID string) error {
	if interaction.Member == nil {
		return fmt.Errorf("无法获取成员信息")
	}

	// 获取成员权限
	memberPermissions := int64(interaction.Member.Permissions)

	// 检查每个所需权限
	for _, permission := range requiredPermissions {
		permissionValue := m.getPermissionValue(permission)
		if permissionValue == 0 {
			logger.Warn("未知权限", "permission", permission)
			continue
		}

		if memberPermissions&permissionValue == 0 {
			return fmt.Errorf("缺少权限: %s", permission)
		}
	}

	return nil
}

// getPermissionValue 获取权限值
func (m *PermissionMiddleware) getPermissionValue(permission string) int64 {
	permissionMap := map[string]int64{
		"ADMINISTRATOR":        discordgo.PermissionAdministrator,
		"MANAGE_GUILD":         discordgo.PermissionManageServer,
		"MANAGE_CHANNELS":      discordgo.PermissionManageChannels,
		"MANAGE_ROLES":         discordgo.PermissionManageRoles,
		"MANAGE_MESSAGES":      discordgo.PermissionManageMessages,
		"KICK_MEMBERS":         discordgo.PermissionKickMembers,
		"BAN_MEMBERS":          discordgo.PermissionBanMembers,
		"SEND_MESSAGES":        discordgo.PermissionSendMessages,
		"READ_MESSAGE_HISTORY": discordgo.PermissionReadMessageHistory,
		"USE_SLASH_COMMANDS":   discordgo.PermissionUseSlashCommands,
		"MODERATE_MEMBERS":     discordgo.PermissionModerateMembers,
	}

	return permissionMap[strings.ToUpper(permission)]
}

// CooldownMiddleware 冷却时间中间件
type CooldownMiddleware struct {
	cooldowns map[string]map[string]time.Time
	config    *types.Config
	mu        sync.RWMutex
}

// NewCooldownMiddleware 创建冷却时间中间件
func NewCooldownMiddleware(config *types.Config) *CooldownMiddleware {
	return &CooldownMiddleware{
		cooldowns: make(map[string]map[string]time.Time),
		config:    config,
	}
}

// Process 处理冷却时间检查
func (m *CooldownMiddleware) Process(ctx context.Context, next func(context.Context) error) error {
	userID, _ := ctx.Value("user_id").(string)
	command, _ := ctx.Value("command").(types.Command)

	if command == nil || userID == "" {
		return next(ctx)
	}

	commandName := command.GetName()

	// 检查冷却时间
	if m.isOnCooldown(commandName, userID) {
		remainingTime := m.getRemainingCooldown(commandName, userID)
		logger.Debug("命令冷却中",
			"command", commandName,
			"user", userID,
			"remaining", remainingTime)
		return types.ErrCommandCooldown
	}

	// 执行命令
	err := next(ctx)

	// 如果命令执行成功，设置冷却时间
	if err == nil {
		m.setCooldown(commandName, userID)
	}

	return err
}

// isOnCooldown 检查是否在冷却时间内
func (m *CooldownMiddleware) isOnCooldown(command, userID string) bool {
	m.mu.RLock()
	defer m.mu.RUnlock()

	userCooldowns, exists := m.cooldowns[userID]
	if !exists {
		return false
	}

	lastUsed, exists := userCooldowns[command]
	if !exists {
		return false
	}

	cooldown := m.getCooldownDuration(command)
	return time.Since(lastUsed) < cooldown
}

// getRemainingCooldown 获取剩余冷却时间
func (m *CooldownMiddleware) getRemainingCooldown(command, userID string) time.Duration {
	m.mu.RLock()
	defer m.mu.RUnlock()

	userCooldowns, exists := m.cooldowns[userID]
	if !exists {
		return 0
	}

	lastUsed, exists := userCooldowns[command]
	if !exists {
		return 0
	}

	cooldown := m.getCooldownDuration(command)
	elapsed := time.Since(lastUsed)

	if elapsed >= cooldown {
		return 0
	}

	return cooldown - elapsed
}

// setCooldown 设置冷却时间
func (m *CooldownMiddleware) setCooldown(command, userID string) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.cooldowns[userID] == nil {
		m.cooldowns[userID] = make(map[string]time.Time)
	}

	m.cooldowns[userID][command] = time.Now()
}

// getCooldownDuration 获取冷却时间长度
func (m *CooldownMiddleware) getCooldownDuration(command string) time.Duration {
	// 检查命令特定的冷却时间
	if cooldown, exists := m.config.Cooldowns.Commands[command]; exists {
		return cooldown
	}

	// 使用默认冷却时间
	return m.config.Cooldowns.Default
}

// RateLimitMiddleware 速率限制中间件
type RateLimitMiddleware struct {
	requests map[string][]time.Time
	limit    int
	window   time.Duration
	mu       sync.RWMutex
}

// NewRateLimitMiddleware 创建速率限制中间件
func NewRateLimitMiddleware(limit int, window time.Duration) *RateLimitMiddleware {
	return &RateLimitMiddleware{
		requests: make(map[string][]time.Time),
		limit:    limit,
		window:   window,
	}
}

// Process 处理速率限制
func (m *RateLimitMiddleware) Process(ctx context.Context, next func(context.Context) error) error {
	userID, _ := ctx.Value("user_id").(string)
	if userID == "" {
		return next(ctx)
	}

	now := time.Now()

	m.mu.Lock()
	defer m.mu.Unlock()

	// 清理过期的请求记录
	m.cleanupExpiredRequests(userID, now)

	// 检查是否超过速率限制
	if len(m.requests[userID]) >= m.limit {
		logger.Warn("用户触发速率限制",
			"user", userID,
			"requests", len(m.requests[userID]),
			"limit", m.limit,
			"window", m.window)
		return fmt.Errorf("速率限制：请求过于频繁")
	}

	// 记录请求
	if m.requests[userID] == nil {
		m.requests[userID] = make([]time.Time, 0)
	}
	m.requests[userID] = append(m.requests[userID], now)

	return next(ctx)
}

// cleanupExpiredRequests 清理过期的请求记录
func (m *RateLimitMiddleware) cleanupExpiredRequests(userID string, now time.Time) {
	requests := m.requests[userID]
	if requests == nil {
		return
	}

	// 过滤掉过期的请求
	validRequests := make([]time.Time, 0)
	for _, requestTime := range requests {
		if now.Sub(requestTime) < m.window {
			validRequests = append(validRequests, requestTime)
		}
	}

	m.requests[userID] = validRequests
}
