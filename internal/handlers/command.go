package handlers

import (
	"context"
	"fmt"
	"sync"

	"zeka-go/internal/commands"
	"zeka-go/internal/services"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// CommandManager 命令管理器
type CommandManager struct {
	commands       map[string]types.Command
	mu             sync.RWMutex
	session        *discordgo.Session
	config         *types.Config
	serviceManager *services.ServiceManager
}

// NewCommandManager 创建新的命令管理器
func NewCommandManager(session *discordgo.Session, config *types.Config) *CommandManager {
	return &CommandManager{
		commands: make(map[string]types.Command),
		session:  session,
		config:   config,
	}
}

// SetServiceManager 设置服务管理器
func (cm *CommandManager) SetServiceManager(serviceManager *services.ServiceManager) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.serviceManager = serviceManager
}

// RegisterCommand 注册命令
func (cm *CommandManager) RegisterCommand(command types.Command) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	name := command.GetName()
	if _, exists := cm.commands[name]; exists {
		return fmt.Errorf("命令 %s 已存在", name)
	}

	cm.commands[name] = command
	logger.Info("注册命令", "name", name, "description", command.GetDescription())
	return nil
}

// UnregisterCommand 注销命令
func (cm *CommandManager) UnregisterCommand(name string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	delete(cm.commands, name)
	logger.Info("注销命令", "name", name)
}

// GetCommand 获取命令
func (cm *CommandManager) GetCommand(name string) (types.Command, bool) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	command, exists := cm.commands[name]
	return command, exists
}

// GetAllCommands 获取所有命令
func (cm *CommandManager) GetAllCommands() []types.Command {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	commands := make([]types.Command, 0, len(cm.commands))
	for _, command := range cm.commands {
		commands = append(commands, command)
	}

	return commands
}

// RegisterDefaultCommands 注册默认命令
func (cm *CommandManager) RegisterDefaultCommands() error {
	logger.Info("开始注册默认命令")

	// 基础命令（无服务依赖）
	basicCommands := []types.Command{
		commands.NewPingCommand(),
		commands.NewHelpCommand(),
	}

	// 注册基础命令
	for _, command := range basicCommands {
		if err := cm.RegisterCommand(command); err != nil {
			return fmt.Errorf("注册基础命令失败: %w", err)
		}
	}

	logger.Info("基础命令注册完成", "count", len(basicCommands))

	// 检查服务管理器状态
	if cm.serviceManager == nil {
		logger.Warn("服务管理器未设置，跳过服务依赖命令注册")
		logger.Info("默认命令注册完成", "count", len(cm.commands))
		return nil
	}

	logger.Info("服务管理器可用，开始注册服务依赖命令")

	// 获取服务实例
	filterService, filterErr := cm.serviceManager.GetService("FilterRuleService")
	forwardService, forwardErr := cm.serviceManager.GetService("ForwardRuleService")
	mappingService, mappingErr := cm.serviceManager.GetService("FieldMappingService")

	// 进行类型断言，确保服务实现了正确的接口
	var filterEngine types.FilterEngine
	var forwardManager types.ForwardRuleManager
	var fieldMapper types.FieldMapper

	// 类型断言 ForwardRuleService
	if forwardErr == nil && forwardService != nil {
		if frs, ok := forwardService.(types.ForwardRuleManager); ok {
			forwardManager = frs
			logger.Info("ForwardRuleService 类型断言成功")
		} else {
			logger.Error("ForwardRuleService 类型断言失败", "service_type", fmt.Sprintf("%T", forwardService))
		}
	} else {
		logger.Error("获取 ForwardRuleService 失败", "error", forwardErr)
	}

	// 类型断言 FieldMappingService
	if mappingErr == nil && mappingService != nil {
		if fms, ok := mappingService.(types.FieldMapper); ok {
			fieldMapper = fms
			logger.Info("FieldMappingService 类型断言成功")
		} else {
			logger.Error("FieldMappingService 类型断言失败", "service_type", fmt.Sprintf("%T", mappingService))
		}
	} else {
		logger.Error("获取 FieldMappingService 失败", "error", mappingErr)
	}

	// 类型断言 FilterRuleService
	if filterErr == nil && filterService != nil {
		if fes, ok := filterService.(types.FilterEngine); ok {
			filterEngine = fes
			logger.Info("FilterRuleService 类型断言成功")
		} else {
			logger.Error("FilterRuleService 类型断言失败", "service_type", fmt.Sprintf("%T", filterService))
		}
	} else {
		logger.Error("获取 FilterRuleService 失败", "error", filterErr)
	}

	// 只有在服务可用时才创建命令
	var serviceCommands []types.Command

	if filterEngine != nil && forwardManager != nil {
		serviceCommands = append(serviceCommands, commands.NewFilterCommand(filterEngine, forwardManager))
		logger.Info("FilterCommand 创建成功")
	} else {
		logger.Warn("FilterCommand 创建跳过", "filterEngine_nil", filterEngine == nil, "forwardManager_nil", forwardManager == nil)
	}

	if forwardManager != nil && fieldMapper != nil {
		serviceCommands = append(serviceCommands, commands.NewForwardCommand(forwardManager, fieldMapper))
		logger.Info("ForwardCommand 创建成功")
	} else {
		logger.Warn("ForwardCommand 创建跳过", "forwardManager_nil", forwardManager == nil, "fieldMapper_nil", fieldMapper == nil)
	}

	// 注册服务命令
	for _, command := range serviceCommands {
		if err := cm.RegisterCommand(command); err != nil {
			logger.Error("注册服务命令失败", "command", command.GetName(), "error", err)
			// 不返回错误，继续注册其他命令
		}
	}

	logger.Info("服务依赖命令注册完成", "service_commands_count", len(serviceCommands))

	logger.Info("默认命令注册完成", "total_commands", len(cm.commands))
	return nil
}

// SyncApplicationCommands 同步应用命令到 Discord
func (cm *CommandManager) SyncApplicationCommands() error {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	logger.Info("开始同步应用命令到 Discord...")

	// 构建应用命令列表
	var appCommands []*discordgo.ApplicationCommand
	for _, command := range cm.commands {
		// 检查命令是否实现了 GetApplicationCommand 方法
		if appCmd, ok := command.(interface {
			GetApplicationCommand() *discordgo.ApplicationCommand
		}); ok {
			appCommands = append(appCommands, appCmd.GetApplicationCommand())
		}
	}

	// 获取应用 ID
	app, err := cm.session.Application("@me")
	if err != nil {
		return fmt.Errorf("获取应用信息失败: %w", err)
	}

	// 同步命令
	var registeredCommands []*discordgo.ApplicationCommand

	if cm.config.Discord.GuildID != "" {
		// 开发模式：注册到特定服务器
		logger.Info("注册命令到开发服务器", "guild_id", cm.config.Discord.GuildID)

		for _, appCommand := range appCommands {
			cmd, err := cm.session.ApplicationCommandCreate(app.ID, cm.config.Discord.GuildID, appCommand)
			if err != nil {
				logger.Error("注册服务器命令失败", "command", appCommand.Name, "error", err)
				continue
			}
			registeredCommands = append(registeredCommands, cmd)
			logger.Debug("服务器命令注册成功", "command", cmd.Name, "id", cmd.ID)
		}
	} else {
		// 生产模式：注册全局命令
		logger.Info("注册全局命令")

		for _, appCommand := range appCommands {
			cmd, err := cm.session.ApplicationCommandCreate(app.ID, "", appCommand)
			if err != nil {
				logger.Error("注册全局命令失败", "command", appCommand.Name, "error", err)
				continue
			}
			registeredCommands = append(registeredCommands, cmd)
			logger.Debug("全局命令注册成功", "command", cmd.Name, "id", cmd.ID)
		}
	}

	logger.Info("应用命令同步完成",
		"total", len(appCommands),
		"registered", len(registeredCommands))

	return nil
}

// CleanupApplicationCommands 清理应用命令
func (cm *CommandManager) CleanupApplicationCommands() error {
	logger.Info("开始清理应用命令...")

	app, err := cm.session.Application("@me")
	if err != nil {
		return fmt.Errorf("获取应用信息失败: %w", err)
	}

	var deletedCount int

	if cm.config.Discord.GuildID != "" {
		// 清理服务器命令
		commands, err := cm.session.ApplicationCommands(app.ID, cm.config.Discord.GuildID)
		if err != nil {
			return fmt.Errorf("获取服务器命令失败: %w", err)
		}

		for _, command := range commands {
			err := cm.session.ApplicationCommandDelete(app.ID, cm.config.Discord.GuildID, command.ID)
			if err != nil {
				logger.Error("删除服务器命令失败", "command", command.Name, "error", err)
				continue
			}
			deletedCount++
			logger.Debug("服务器命令删除成功", "command", command.Name)
		}
	} else {
		// 清理全局命令
		commands, err := cm.session.ApplicationCommands(app.ID, "")
		if err != nil {
			return fmt.Errorf("获取全局命令失败: %w", err)
		}

		for _, command := range commands {
			err := cm.session.ApplicationCommandDelete(app.ID, "", command.ID)
			if err != nil {
				logger.Error("删除全局命令失败", "command", command.Name, "error", err)
				continue
			}
			deletedCount++
			logger.Debug("全局命令删除成功", "command", command.Name)
		}
	}

	logger.Info("应用命令清理完成", "deleted", deletedCount)
	return nil
}

// HandleSlashCommand 处理斜杠命令
func (cm *CommandManager) HandleSlashCommand(ctx context.Context, client *types.Client, interaction *discordgo.InteractionCreate) error {
	commandName := interaction.ApplicationCommandData().Name

	cm.mu.RLock()
	command, exists := cm.commands[commandName]
	cm.mu.RUnlock()

	if !exists {
		return fmt.Errorf("未知命令: %s", commandName)
	}

	// 验证命令
	if err := command.Validate(interaction); err != nil {
		return fmt.Errorf("命令验证失败: %w", err)
	}

	// 执行命令
	return command.Execute(ctx, client, interaction)
}

// GetCommandStats 获取命令统计信息
func (cm *CommandManager) GetCommandStats() map[string]interface{} {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	stats := make(map[string]interface{})

	// 按分类统计
	categories := make(map[string]int)
	for _, command := range cm.commands {
		category := command.GetCategory()
		categories[category]++
	}

	stats["total_commands"] = len(cm.commands)
	stats["categories"] = categories

	// 命令列表
	commandList := make([]map[string]interface{}, 0, len(cm.commands))
	for _, command := range cm.commands {
		commandInfo := map[string]interface{}{
			"name":        command.GetName(),
			"description": command.GetDescription(),
			"category":    command.GetCategory(),
			"cooldown":    command.GetCooldown().String(),
			"permissions": command.GetPermissions(),
		}
		commandList = append(commandList, commandInfo)
	}
	stats["commands"] = commandList

	return stats
}
