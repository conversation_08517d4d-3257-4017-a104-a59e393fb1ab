package handlers

import (
	"context"
	"fmt"
	"sync"

	"github.com/bwmarrin/discordgo"
	"zeka-go/internal/components"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"
)

// ComponentManager 组件管理器
type ComponentManager struct {
	components map[string]types.ComponentHandler
	mu         sync.RWMutex
	session    *discordgo.Session
	config     *types.Config
}

// NewComponentManager 创建新的组件管理器
func NewComponentManager(session *discordgo.Session, config *types.Config) *ComponentManager {
	return &ComponentManager{
		components: make(map[string]types.ComponentHandler),
		session:    session,
		config:     config,
	}
}

// RegisterComponent 注册组件处理器
func (cm *ComponentManager) RegisterComponent(component types.ComponentHandler) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	
	customID := component.GetCustomID()
	if _, exists := cm.components[customID]; exists {
		return fmt.Errorf("组件 %s 已存在", customID)
	}
	
	cm.components[customID] = component
	logger.Info("注册组件处理器",
		"custom_id", customID,
		"type", component.GetType())
	
	return nil
}

// UnregisterComponent 注销组件处理器
func (cm *ComponentManager) UnregisterComponent(customID string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	
	delete(cm.components, customID)
	logger.Info("注销组件处理器", "custom_id", customID)
}

// GetComponent 获取组件处理器
func (cm *ComponentManager) GetComponent(customID string) (types.ComponentHandler, bool) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	component, exists := cm.components[customID]
	return component, exists
}

// GetAllComponents 获取所有组件处理器
func (cm *ComponentManager) GetAllComponents() []types.ComponentHandler {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	components := make([]types.ComponentHandler, 0, len(cm.components))
	for _, component := range cm.components {
		components = append(components, component)
	}
	
	return components
}

// RegisterDefaultComponents 注册默认组件
func (cm *ComponentManager) RegisterDefaultComponents() error {
	defaultComponents := []types.ComponentHandler{
		components.NewConfirmButton(),
		components.NewCancelButton(),
		components.NewDeleteButton(),
		components.NewLanguageSelectMenu(),
	}
	
	for _, component := range defaultComponents {
		if err := cm.RegisterComponent(component); err != nil {
			return fmt.Errorf("注册默认组件失败: %w", err)
		}
	}
	
	logger.Info("默认组件注册完成", "count", len(defaultComponents))
	return nil
}

// HandleComponentInteraction 处理组件交互
func (cm *ComponentManager) HandleComponentInteraction(ctx context.Context, client *types.Client, interaction *discordgo.InteractionCreate) error {
	data := interaction.MessageComponentData()
	customID := data.CustomID
	
	cm.mu.RLock()
	component, exists := cm.components[customID]
	cm.mu.RUnlock()
	
	if !exists {
		// 尝试处理动态组件 ID（如分页按钮）
		return cm.handleDynamicComponent(ctx, client, interaction, customID)
	}
	
	// 验证组件
	if err := component.Validate(interaction); err != nil {
		return fmt.Errorf("组件验证失败: %w", err)
	}
	
	// 执行组件处理
	return component.Handle(ctx, client, interaction)
}

// handleDynamicComponent 处理动态组件
func (cm *ComponentManager) handleDynamicComponent(ctx context.Context, client *types.Client, interaction *discordgo.InteractionCreate, customID string) error {
	// 处理分页按钮
	if customID[:9] == "page_prev" || customID[:9] == "page_next" {
		return cm.handlePaginationButton(ctx, client, interaction, customID)
	}
	
	// 处理其他动态组件...
	
	return fmt.Errorf("未知的组件: %s", customID)
}

// handlePaginationButton 处理分页按钮
func (cm *ComponentManager) handlePaginationButton(ctx context.Context, client *types.Client, interaction *discordgo.InteractionCreate, customID string) error {
	var user *discordgo.User
	if interaction.Member != nil {
		user = interaction.Member.User
	} else {
		user = interaction.User
	}
	
	logger.Info("分页按钮被点击",
		"user", user.Username,
		"custom_id", customID)
	
	// 这里应该实现具体的分页逻辑
	// 暂时返回一个提示消息
	return client.Session.InteractionRespond(interaction.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Content: "分页功能需要在具体使用场景中实现",
			Flags:   discordgo.MessageFlagsEphemeral,
		},
	})
}

// GetComponentStats 获取组件统计信息
func (cm *ComponentManager) GetComponentStats() map[string]interface{} {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	stats := make(map[string]interface{})
	
	// 按类型统计组件数量
	componentTypes := make(map[types.ComponentType]int)
	totalComponents := 0
	
	for _, component := range cm.components {
		componentTypes[component.GetType()]++
		totalComponents++
	}
	
	stats["total_components"] = totalComponents
	stats["component_types"] = componentTypes
	
	// 组件详情
	componentDetails := make([]map[string]interface{}, 0, len(cm.components))
	for customID, component := range cm.components {
		detail := map[string]interface{}{
			"custom_id": customID,
			"type":      component.GetType(),
			"handler":   fmt.Sprintf("%T", component),
		}
		componentDetails = append(componentDetails, detail)
	}
	stats["component_details"] = componentDetails
	
	return stats
}

// CreateConfirmationButtons 创建确认按钮组
func (cm *ComponentManager) CreateConfirmationButtons() []discordgo.MessageComponent {
	return []discordgo.MessageComponent{
		discordgo.ActionsRow{
			Components: []discordgo.MessageComponent{
				discordgo.Button{
					Label:    "确认",
					Style:    discordgo.PrimaryButton,
					CustomID: "confirm_button",
				},
				discordgo.Button{
					Label:    "取消",
					Style:    discordgo.SecondaryButton,
					CustomID: "cancel_button",
				},
			},
		},
	}
}

// CreateDeleteButton 创建删除按钮
func (cm *ComponentManager) CreateDeleteButton() []discordgo.MessageComponent {
	return []discordgo.MessageComponent{
		discordgo.ActionsRow{
			Components: []discordgo.MessageComponent{
				discordgo.Button{
					Label:    "删除",
					Style:    discordgo.DangerButton,
					CustomID: "delete_button",
				},
			},
		},
	}
}

// CreateRoleSelectMenu 创建角色选择菜单
func (cm *ComponentManager) CreateRoleSelectMenu(roles []discordgo.Role) []discordgo.MessageComponent {
	roleSelect := components.NewRoleSelectMenu(roles)
	
	// 临时注册组件
	cm.RegisterComponent(roleSelect)
	
	return []discordgo.MessageComponent{
		discordgo.ActionsRow{
			Components: []discordgo.MessageComponent{
				roleSelect.GetComponent(),
			},
		},
	}
}

// CreateLanguageSelectMenu 创建语言选择菜单
func (cm *ComponentManager) CreateLanguageSelectMenu() []discordgo.MessageComponent {
	languageSelect := components.NewLanguageSelectMenu()
	
	return []discordgo.MessageComponent{
		discordgo.ActionsRow{
			Components: []discordgo.MessageComponent{
				languageSelect.GetComponent(),
			},
		},
	}
}

// CreateCategorySelectMenu 创建分类选择菜单
func (cm *ComponentManager) CreateCategorySelectMenu(categories map[string]string) []discordgo.MessageComponent {
	categorySelect := components.NewCategorySelectMenu(categories)
	
	// 临时注册组件
	cm.RegisterComponent(categorySelect)
	
	return []discordgo.MessageComponent{
		discordgo.ActionsRow{
			Components: []discordgo.MessageComponent{
				categorySelect.GetComponent(),
			},
		},
	}
}

// CreatePaginationButtons 创建分页按钮
func (cm *ComponentManager) CreatePaginationButtons(currentPage, totalPages int) []discordgo.MessageComponent {
	pagination := components.NewPaginationButtons(currentPage, totalPages)
	return pagination.GetComponents()
}

// RemoveAllComponents 移除所有组件
func (cm *ComponentManager) RemoveAllComponents() {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	
	cm.components = make(map[string]types.ComponentHandler)
	logger.Info("已移除所有组件处理器")
}

// GetSupportedComponentTypes 获取支持的组件类型列表
func (cm *ComponentManager) GetSupportedComponentTypes() []types.ComponentType {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	typeSet := make(map[types.ComponentType]bool)
	for _, component := range cm.components {
		typeSet[component.GetType()] = true
	}
	
	var types []types.ComponentType
	for componentType := range typeSet {
		types = append(types, componentType)
	}
	
	return types
}

// IsComponentSupported 检查是否支持指定组件
func (cm *ComponentManager) IsComponentSupported(customID string) bool {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	_, exists := cm.components[customID]
	return exists
}

// GetComponentCount 获取组件数量
func (cm *ComponentManager) GetComponentCount() int {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	return len(cm.components)
}
