package handlers

import (
	"context"
	"fmt"
	"strings"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// PermissionManager 权限管理器
type PermissionManager struct {
	config *types.Config
	cache  types.CacheService
}

// NewPermissionManager 创建权限管理器
func NewPermissionManager(config *types.Config, cache types.CacheService) *PermissionManager {
	return &PermissionManager{
		config: config,
		cache:  cache,
	}
}

// CheckCommandPermission 检查命令权限
func (pm *PermissionManager) CheckCommandPermission(ctx context.Context, session *discordgo.Session, interaction *discordgo.InteractionCreate, command types.Command) error {
	userID := pm.getUserID(interaction)
	guildID := interaction.GuildID
	commandName := command.GetName()

	// Bot 所有者拥有所有权限
	if pm.isOwner(userID) {
		logger.Debug("Bot 所有者执行命令", "user", userID, "command", commandName)
		return nil
	}

	// 检查是否启用权限检查
	if !pm.config.Permissions.EnablePermissionCheck {
		logger.Debug("权限检查已禁用", "command", commandName)
		return nil
	}

	// 优先检查基于角色ID的权限
	if interaction.Member != nil {
		if pm.checkRoleBasedPermissions(interaction.Member, commandName) {
			logger.Debug("角色权限检查通过", "user", userID, "command", commandName)
			return nil
		}
	}

	// 回退到Discord权限位检查
	requiredPermissions := command.GetPermissions()
	if len(requiredPermissions) > 0 {
		if err := pm.checkUserPermissions(ctx, session, userID, guildID, requiredPermissions); err == nil {
			logger.Debug("权限位检查通过", "user", userID, "command", commandName)
			return nil
		}
	}

	// 检查回退权限配置
	if commandPerms, exists := pm.config.Permissions.CommandPermissions[commandName]; exists {
		if len(commandPerms.FallbackPermissions) > 0 {
			if err := pm.checkUserPermissions(ctx, session, userID, guildID, commandPerms.FallbackPermissions); err == nil {
				logger.Debug("回退权限检查通过", "user", userID, "command", commandName)
				return nil
			}
		}
	}

	logger.Warn("用户权限检查失败", "user", userID, "command", commandName)
	return fmt.Errorf("权限不足")
}

// checkUserPermissions 检查用户权限
func (pm *PermissionManager) checkUserPermissions(ctx context.Context, session *discordgo.Session, userID, guildID string, requiredPermissions []string) error {
	// 获取用户在服务器中的权限
	member, err := session.GuildMember(guildID, userID)
	if err != nil {
		return fmt.Errorf("获取成员信息失败: %w", err)
	}

	// 获取服务器信息
	guild, err := session.Guild(guildID)
	if err != nil {
		return fmt.Errorf("获取服务器信息失败: %w", err)
	}

	// 计算用户权限
	userPermissions := pm.calculateUserPermissions(member, guild)

	// 检查每个所需权限
	for _, permission := range requiredPermissions {
		if !pm.hasPermission(userPermissions, permission) {
			logger.Warn("用户缺少权限",
				"user", userID,
				"guild", guildID,
				"required_permission", permission,
				"user_permissions", userPermissions)
			return fmt.Errorf("缺少权限: %s", permission)
		}
	}

	logger.Debug("权限检查通过",
		"user", userID,
		"guild", guildID,
		"permissions", requiredPermissions)

	return nil
}

// calculateUserPermissions 计算用户权限
func (pm *PermissionManager) calculateUserPermissions(member *discordgo.Member, guild *discordgo.Guild) int64 {
	// 如果是服务器所有者，拥有所有权限
	if member.User.ID == guild.OwnerID {
		return discordgo.PermissionAll
	}

	var permissions int64

	// 获取 @everyone 角色权限
	for _, role := range guild.Roles {
		if role.ID == guild.ID {
			permissions |= role.Permissions
			break
		}
	}

	// 添加用户角色权限
	for _, roleID := range member.Roles {
		for _, role := range guild.Roles {
			if role.ID == roleID {
				permissions |= role.Permissions
				break
			}
		}
	}

	// 如果有管理员权限，拥有所有权限
	if permissions&discordgo.PermissionAdministrator != 0 {
		return discordgo.PermissionAll
	}

	return permissions
}

// hasPermission 检查是否拥有指定权限
func (pm *PermissionManager) hasPermission(userPermissions int64, permission string) bool {
	permissionValue := pm.getPermissionValue(permission)
	if permissionValue == 0 {
		logger.Warn("未知权限", "permission", permission)
		return false
	}

	return userPermissions&permissionValue != 0
}

// getPermissionValue 获取权限值
func (pm *PermissionManager) getPermissionValue(permission string) int64 {
	permissionMap := map[string]int64{
		"ADMINISTRATOR":        discordgo.PermissionAdministrator,
		"MANAGE_GUILD":         discordgo.PermissionManageServer,
		"MANAGE_CHANNELS":      discordgo.PermissionManageChannels,
		"MANAGE_ROLES":         discordgo.PermissionManageRoles,
		"MANAGE_MESSAGES":      discordgo.PermissionManageMessages,
		"KICK_MEMBERS":         discordgo.PermissionKickMembers,
		"BAN_MEMBERS":          discordgo.PermissionBanMembers,
		"SEND_MESSAGES":        discordgo.PermissionSendMessages,
		"READ_MESSAGE_HISTORY": discordgo.PermissionReadMessageHistory,
		"USE_SLASH_COMMANDS":   discordgo.PermissionUseSlashCommands,
		"MODERATE_MEMBERS":     discordgo.PermissionModerateMembers,
		"VIEW_CHANNEL":         discordgo.PermissionViewChannel,
		"EMBED_LINKS":          discordgo.PermissionEmbedLinks,
		"ATTACH_FILES":         discordgo.PermissionAttachFiles,
		"ADD_REACTIONS":        discordgo.PermissionAddReactions,
		"USE_EXTERNAL_EMOJIS":  discordgo.PermissionUseExternalEmojis,
		"MENTION_EVERYONE":     discordgo.PermissionMentionEveryone,
		"MANAGE_WEBHOOKS":      discordgo.PermissionManageWebhooks,
		"CONNECT":              discordgo.PermissionVoiceConnect,
		"SPEAK":                discordgo.PermissionVoiceSpeak,
		"MUTE_MEMBERS":         discordgo.PermissionVoiceMuteMembers,
		"DEAFEN_MEMBERS":       discordgo.PermissionVoiceDeafenMembers,
		"MOVE_MEMBERS":         discordgo.PermissionVoiceMoveMembers,
		"USE_VAD":              discordgo.PermissionVoiceUseVAD,
	}

	return permissionMap[strings.ToUpper(permission)]
}

// isOwner 检查是否为 Bot 所有者
func (pm *PermissionManager) isOwner(userID string) bool {
	// 检查主所有者
	if pm.config.Discord.OwnerID == userID {
		return true
	}

	// 检查Discord配置中的所有者列表
	for _, ownerID := range pm.config.Discord.Owners {
		if ownerID == userID {
			return true
		}
	}

	// 检查权限配置中的Bot所有者列表
	for _, ownerID := range pm.config.Permissions.BotOwners {
		if ownerID == userID {
			return true
		}
	}

	return false
}

// checkRoleBasedPermissions 检查基于角色ID的权限
func (pm *PermissionManager) checkRoleBasedPermissions(member *discordgo.Member, commandName string) bool {
	commandPerms, exists := pm.config.Permissions.CommandPermissions[commandName]
	if !exists {
		return false
	}

	userID := member.User.ID

	// 检查用户ID权限
	for _, allowedUser := range commandPerms.AllowedUsers {
		if userID == allowedUser {
			logger.Debug("用户ID权限匹配", "user", userID, "command", commandName)
			return true
		}
	}

	// 检查角色ID权限
	for _, userRole := range member.Roles {
		for _, allowedRole := range commandPerms.AllowedRoles {
			if userRole == allowedRole {
				logger.Debug("角色ID权限匹配", "user", userID, "role", userRole, "command", commandName)
				return true
			}
		}
	}

	return false
}

// getUserID 获取用户ID
func (pm *PermissionManager) getUserID(interaction *discordgo.InteractionCreate) string {
	if interaction.Member != nil && interaction.Member.User != nil {
		return interaction.Member.User.ID
	}
	if interaction.User != nil {
		return interaction.User.ID
	}
	return ""
}

// CheckChannelPermission 检查频道权限
func (pm *PermissionManager) CheckChannelPermission(ctx context.Context, session *discordgo.Session, userID, channelID string, permission string) error {
	// 获取频道信息
	channel, err := session.Channel(channelID)
	if err != nil {
		return fmt.Errorf("获取频道信息失败: %w", err)
	}

	// 如果是 DM 频道，无需权限检查
	if channel.Type == discordgo.ChannelTypeDM || channel.Type == discordgo.ChannelTypeGroupDM {
		return nil
	}

	// 获取用户在频道中的权限
	permissions, err := session.UserChannelPermissions(userID, channelID)
	if err != nil {
		return fmt.Errorf("获取频道权限失败: %w", err)
	}

	// 检查权限
	if !pm.hasPermission(permissions, permission) {
		return fmt.Errorf("在频道 %s 中缺少权限: %s", channelID, permission)
	}

	return nil
}

// GetUserPermissions 获取用户权限列表
func (pm *PermissionManager) GetUserPermissions(ctx context.Context, session *discordgo.Session, userID, guildID string) ([]string, error) {
	// 获取用户在服务器中的权限
	member, err := session.GuildMember(guildID, userID)
	if err != nil {
		return nil, fmt.Errorf("获取成员信息失败: %w", err)
	}

	// 获取服务器信息
	guild, err := session.Guild(guildID)
	if err != nil {
		return nil, fmt.Errorf("获取服务器信息失败: %w", err)
	}

	// 计算用户权限
	userPermissions := pm.calculateUserPermissions(member, guild)

	// 转换为权限名称列表
	var permissions []string
	permissionMap := map[int64]string{
		discordgo.PermissionAdministrator:      "ADMINISTRATOR",
		discordgo.PermissionManageServer:       "MANAGE_GUILD",
		discordgo.PermissionManageChannels:     "MANAGE_CHANNELS",
		discordgo.PermissionManageRoles:        "MANAGE_ROLES",
		discordgo.PermissionManageMessages:     "MANAGE_MESSAGES",
		discordgo.PermissionKickMembers:        "KICK_MEMBERS",
		discordgo.PermissionBanMembers:         "BAN_MEMBERS",
		discordgo.PermissionSendMessages:       "SEND_MESSAGES",
		discordgo.PermissionReadMessageHistory: "READ_MESSAGE_HISTORY",
		discordgo.PermissionUseSlashCommands:   "USE_SLASH_COMMANDS",
		discordgo.PermissionModerateMembers:    "MODERATE_MEMBERS",
	}

	for permissionValue, permissionName := range permissionMap {
		if userPermissions&permissionValue != 0 {
			permissions = append(permissions, permissionName)
		}
	}

	return permissions, nil
}

// PermissionLevel 权限级别
type PermissionLevel int

const (
	PermissionLevelUser PermissionLevel = iota
	PermissionLevelModerator
	PermissionLevelAdmin
	PermissionLevelOwner
)

// GetUserPermissionLevel 获取用户权限级别
func (pm *PermissionManager) GetUserPermissionLevel(ctx context.Context, session *discordgo.Session, userID, guildID string) (PermissionLevel, error) {
	// Bot 所有者
	if pm.isOwner(userID) {
		return PermissionLevelOwner, nil
	}

	// 获取用户权限
	permissions, err := pm.GetUserPermissions(ctx, session, userID, guildID)
	if err != nil {
		return PermissionLevelUser, err
	}

	// 判断权限级别
	for _, permission := range permissions {
		switch permission {
		case "ADMINISTRATOR":
			return PermissionLevelAdmin, nil
		case "MANAGE_GUILD", "MANAGE_CHANNELS", "MANAGE_ROLES":
			return PermissionLevelAdmin, nil
		case "KICK_MEMBERS", "BAN_MEMBERS", "MANAGE_MESSAGES", "MODERATE_MEMBERS":
			return PermissionLevelModerator, nil
		}
	}

	return PermissionLevelUser, nil
}
