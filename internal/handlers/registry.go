package handlers

import (
	"context"
	"fmt"
	"sort"
	"sync"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// HandlerType 处理器类型
type HandlerType string

const (
	HandlerTypeCommand   HandlerType = "command"
	HandlerTypeEvent     HandlerType = "event"
	HandlerTypeComponent HandlerType = "component"
)

// HandlerInfo 处理器信息
type HandlerInfo struct {
	Name        string                 `json:"name"`
	Type        HandlerType            `json:"type"`
	Priority    int                    `json:"priority"`
	Description string                 `json:"description"`
	Enabled     bool                   `json:"enabled"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// HandlerWrapper 处理器包装器
type HandlerWrapper struct {
	Info    *HandlerInfo
	Handler interface{}
}

// HandlerRegistry 处理器注册表
type HandlerRegistry struct {
	// 处理器存储
	commands   map[string]*HandlerWrapper
	events     map[string][]*HandlerWrapper
	components map[string]*HandlerWrapper

	// 中间件
	commandMiddlewares   []types.Middleware
	eventMiddlewares     []types.Middleware
	componentMiddlewares []types.Middleware

	// 状态管理
	mu        sync.RWMutex
	isEnabled bool

	// 统计信息
	stats *RegistryStats
}

// RegistryStats 注册表统计信息
type RegistryStats struct {
	TotalHandlers     int `json:"total_handlers"`
	CommandHandlers   int `json:"command_handlers"`
	EventHandlers     int `json:"event_handlers"`
	ComponentHandlers int `json:"component_handlers"`
	EnabledHandlers   int `json:"enabled_handlers"`
	DisabledHandlers  int `json:"disabled_handlers"`
}

// NewHandlerRegistry 创建处理器注册表
func NewHandlerRegistry() *HandlerRegistry {
	return &HandlerRegistry{
		commands:   make(map[string]*HandlerWrapper),
		events:     make(map[string][]*HandlerWrapper),
		components: make(map[string]*HandlerWrapper),
		isEnabled:  true,
		stats:      &RegistryStats{},
	}
}

// RegisterCommand 注册命令处理器
func (hr *HandlerRegistry) RegisterCommand(name string, handler types.Command, priority int) error {
	hr.mu.Lock()
	defer hr.mu.Unlock()

	if _, exists := hr.commands[name]; exists {
		return fmt.Errorf("命令处理器 %s 已存在", name)
	}

	wrapper := &HandlerWrapper{
		Info: &HandlerInfo{
			Name:        name,
			Type:        HandlerTypeCommand,
			Priority:    priority,
			Description: handler.GetDescription(),
			Enabled:     true,
			Metadata:    make(map[string]interface{}),
		},
		Handler: handler,
	}

	hr.commands[name] = wrapper
	hr.updateStats()

	logger.Info("命令处理器已注册", "name", name, "priority", priority)
	return nil
}

// RegisterEvent 注册事件处理器
func (hr *HandlerRegistry) RegisterEvent(eventType string, handler types.EventHandler, priority int) error {
	hr.mu.Lock()
	defer hr.mu.Unlock()

	wrapper := &HandlerWrapper{
		Info: &HandlerInfo{
			Name:        fmt.Sprintf("%s_%d", eventType, len(hr.events[eventType])),
			Type:        HandlerTypeEvent,
			Priority:    priority,
			Description: fmt.Sprintf("事件处理器: %s", eventType),
			Enabled:     true,
			Metadata:    make(map[string]interface{}),
		},
		Handler: handler,
	}

	hr.events[eventType] = append(hr.events[eventType], wrapper)

	// 按优先级排序
	sort.Slice(hr.events[eventType], func(i, j int) bool {
		return hr.events[eventType][i].Info.Priority > hr.events[eventType][j].Info.Priority
	})

	hr.updateStats()

	logger.Info("事件处理器已注册", "event_type", eventType, "priority", priority)
	return nil
}

// RegisterComponent 注册组件处理器
func (hr *HandlerRegistry) RegisterComponent(customID string, handler types.ComponentHandler, priority int) error {
	hr.mu.Lock()
	defer hr.mu.Unlock()

	if _, exists := hr.components[customID]; exists {
		return fmt.Errorf("组件处理器 %s 已存在", customID)
	}

	wrapper := &HandlerWrapper{
		Info: &HandlerInfo{
			Name:        customID,
			Type:        HandlerTypeComponent,
			Priority:    priority,
			Description: fmt.Sprintf("组件处理器: %s", customID),
			Enabled:     true,
			Metadata:    make(map[string]interface{}),
		},
		Handler: handler,
	}

	hr.components[customID] = wrapper
	hr.updateStats()

	logger.Info("组件处理器已注册", "custom_id", customID, "priority", priority)
	return nil
}

// GetCommand 获取命令处理器
func (hr *HandlerRegistry) GetCommand(name string) (types.Command, bool) {
	hr.mu.RLock()
	defer hr.mu.RUnlock()

	wrapper, exists := hr.commands[name]
	if !exists || !wrapper.Info.Enabled {
		return nil, false
	}

	if command, ok := wrapper.Handler.(types.Command); ok {
		return command, true
	}

	return nil, false
}

// GetEventHandlers 获取事件处理器列表
func (hr *HandlerRegistry) GetEventHandlers(eventType string) []types.EventHandler {
	hr.mu.RLock()
	defer hr.mu.RUnlock()

	wrappers, exists := hr.events[eventType]
	if !exists {
		return nil
	}

	var handlers []types.EventHandler
	for _, wrapper := range wrappers {
		if wrapper.Info.Enabled {
			if handler, ok := wrapper.Handler.(types.EventHandler); ok {
				handlers = append(handlers, handler)
			}
		}
	}

	return handlers
}

// GetComponent 获取组件处理器
func (hr *HandlerRegistry) GetComponent(customID string) (types.ComponentHandler, bool) {
	hr.mu.RLock()
	defer hr.mu.RUnlock()

	wrapper, exists := hr.components[customID]
	if !exists || !wrapper.Info.Enabled {
		return nil, false
	}

	if component, ok := wrapper.Handler.(types.ComponentHandler); ok {
		return component, true
	}

	return nil, false
}

// EnableHandler 启用处理器
func (hr *HandlerRegistry) EnableHandler(handlerType HandlerType, name string) error {
	hr.mu.Lock()
	defer hr.mu.Unlock()

	switch handlerType {
	case HandlerTypeCommand:
		if wrapper, exists := hr.commands[name]; exists {
			wrapper.Info.Enabled = true
			hr.updateStats()
			logger.Info("命令处理器已启用", "name", name)
			return nil
		}
	case HandlerTypeComponent:
		if wrapper, exists := hr.components[name]; exists {
			wrapper.Info.Enabled = true
			hr.updateStats()
			logger.Info("组件处理器已启用", "name", name)
			return nil
		}
	case HandlerTypeEvent:
		// 事件处理器需要通过事件类型和索引来启用
		return fmt.Errorf("事件处理器启用需要使用 EnableEventHandler 方法")
	}

	return fmt.Errorf("处理器 %s 不存在", name)
}

// DisableHandler 禁用处理器
func (hr *HandlerRegistry) DisableHandler(handlerType HandlerType, name string) error {
	hr.mu.Lock()
	defer hr.mu.Unlock()

	switch handlerType {
	case HandlerTypeCommand:
		if wrapper, exists := hr.commands[name]; exists {
			wrapper.Info.Enabled = false
			hr.updateStats()
			logger.Info("命令处理器已禁用", "name", name)
			return nil
		}
	case HandlerTypeComponent:
		if wrapper, exists := hr.components[name]; exists {
			wrapper.Info.Enabled = false
			hr.updateStats()
			logger.Info("组件处理器已禁用", "name", name)
			return nil
		}
	case HandlerTypeEvent:
		return fmt.Errorf("事件处理器禁用需要使用 DisableEventHandler 方法")
	}

	return fmt.Errorf("处理器 %s 不存在", name)
}

// AddCommandMiddleware 添加命令中间件
func (hr *HandlerRegistry) AddCommandMiddleware(middleware types.Middleware) {
	hr.mu.Lock()
	defer hr.mu.Unlock()

	hr.commandMiddlewares = append(hr.commandMiddlewares, middleware)
	logger.Info("命令中间件已添加")
}

// AddEventMiddleware 添加事件中间件
func (hr *HandlerRegistry) AddEventMiddleware(middleware types.Middleware) {
	hr.mu.Lock()
	defer hr.mu.Unlock()

	hr.eventMiddlewares = append(hr.eventMiddlewares, middleware)
	logger.Info("事件中间件已添加")
}

// AddComponentMiddleware 添加组件中间件
func (hr *HandlerRegistry) AddComponentMiddleware(middleware types.Middleware) {
	hr.mu.Lock()
	defer hr.mu.Unlock()

	hr.componentMiddlewares = append(hr.componentMiddlewares, middleware)
	logger.Info("组件中间件已添加")
}

// ProcessCommand 处理命令
func (hr *HandlerRegistry) ProcessCommand(ctx context.Context, client *types.Client, interaction *discordgo.InteractionCreate) error {
	if !hr.isEnabled {
		return fmt.Errorf("处理器注册表已禁用")
	}

	commandName := interaction.ApplicationCommandData().Name
	command, exists := hr.GetCommand(commandName)
	if !exists {
		return fmt.Errorf("未找到命令处理器: %s", commandName)
	}

	// 应用中间件
	return hr.applyMiddlewares(ctx, hr.commandMiddlewares, func(ctx context.Context) error {
		return command.Execute(ctx, client, interaction)
	})
}

// ProcessEvent 处理事件
func (hr *HandlerRegistry) ProcessEvent(ctx context.Context, client *types.Client, eventType string, event interface{}) error {
	if !hr.isEnabled {
		return fmt.Errorf("处理器注册表已禁用")
	}

	handlers := hr.GetEventHandlers(eventType)
	if len(handlers) == 0 {
		return nil // 没有处理器，不是错误
	}

	// 应用中间件并处理事件
	return hr.applyMiddlewares(ctx, hr.eventMiddlewares, func(ctx context.Context) error {
		for _, handler := range handlers {
			if handler.ShouldHandle(event) {
				if err := handler.Handle(ctx, client, event); err != nil {
					logger.Error("事件处理失败", "event_type", eventType, "handler", fmt.Sprintf("%T", handler), "error", err)
					// 继续处理其他处理器
				}
			}
		}
		return nil
	})
}

// ProcessComponent 处理组件
func (hr *HandlerRegistry) ProcessComponent(ctx context.Context, client *types.Client, interaction *discordgo.InteractionCreate) error {
	if !hr.isEnabled {
		return fmt.Errorf("处理器注册表已禁用")
	}

	customID := interaction.MessageComponentData().CustomID
	component, exists := hr.GetComponent(customID)
	if !exists {
		return fmt.Errorf("未找到组件处理器: %s", customID)
	}

	// 应用中间件
	return hr.applyMiddlewares(ctx, hr.componentMiddlewares, func(ctx context.Context) error {
		return component.Handle(ctx, client, interaction)
	})
}

// applyMiddlewares 应用中间件
func (hr *HandlerRegistry) applyMiddlewares(ctx context.Context, middlewares []types.Middleware, handler func(context.Context) error) error {
	if len(middlewares) == 0 {
		return handler(ctx)
	}

	// 构建中间件链
	next := handler
	for i := len(middlewares) - 1; i >= 0; i-- {
		middleware := middlewares[i]
		currentNext := next
		next = func(ctx context.Context) error {
			return middleware.Process(ctx, currentNext)
		}
	}

	return next(ctx)
}

// GetStats 获取统计信息
func (hr *HandlerRegistry) GetStats() *RegistryStats {
	hr.mu.RLock()
	defer hr.mu.RUnlock()

	// 返回副本
	stats := *hr.stats
	return &stats
}

// GetAllHandlers 获取所有处理器信息
func (hr *HandlerRegistry) GetAllHandlers() map[HandlerType][]HandlerInfo {
	hr.mu.RLock()
	defer hr.mu.RUnlock()

	result := make(map[HandlerType][]HandlerInfo)

	// 命令处理器
	for _, wrapper := range hr.commands {
		result[HandlerTypeCommand] = append(result[HandlerTypeCommand], *wrapper.Info)
	}

	// 事件处理器
	for _, wrappers := range hr.events {
		for _, wrapper := range wrappers {
			result[HandlerTypeEvent] = append(result[HandlerTypeEvent], *wrapper.Info)
		}
	}

	// 组件处理器
	for _, wrapper := range hr.components {
		result[HandlerTypeComponent] = append(result[HandlerTypeComponent], *wrapper.Info)
	}

	return result
}

// Enable 启用注册表
func (hr *HandlerRegistry) Enable() {
	hr.mu.Lock()
	defer hr.mu.Unlock()

	hr.isEnabled = true
	logger.Info("处理器注册表已启用")
}

// Disable 禁用注册表
func (hr *HandlerRegistry) Disable() {
	hr.mu.Lock()
	defer hr.mu.Unlock()

	hr.isEnabled = false
	logger.Info("处理器注册表已禁用")
}

// updateStats 更新统计信息
func (hr *HandlerRegistry) updateStats() {
	hr.stats.CommandHandlers = len(hr.commands)
	hr.stats.ComponentHandlers = len(hr.components)

	eventCount := 0
	for _, wrappers := range hr.events {
		eventCount += len(wrappers)
	}
	hr.stats.EventHandlers = eventCount

	hr.stats.TotalHandlers = hr.stats.CommandHandlers + hr.stats.EventHandlers + hr.stats.ComponentHandlers

	// 计算启用/禁用的处理器数量
	enabled := 0
	disabled := 0

	for _, wrapper := range hr.commands {
		if wrapper.Info.Enabled {
			enabled++
		} else {
			disabled++
		}
	}

	for _, wrapper := range hr.components {
		if wrapper.Info.Enabled {
			enabled++
		} else {
			disabled++
		}
	}

	for _, wrappers := range hr.events {
		for _, wrapper := range wrappers {
			if wrapper.Info.Enabled {
				enabled++
			} else {
				disabled++
			}
		}
	}

	hr.stats.EnabledHandlers = enabled
	hr.stats.DisabledHandlers = disabled
}
