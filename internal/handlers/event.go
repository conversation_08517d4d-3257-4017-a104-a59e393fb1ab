package handlers

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"zeka-go/internal/events"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// EventManager 事件管理器
type EventManager struct {
	handlers      map[string][]types.EventHandler
	mu            sync.RWMutex
	session       *discordgo.Session
	config        *types.Config
	filterService types.FilterEngine
}

// NewEventManager 创建新的事件管理器
func NewEventManager(session *discordgo.Session, config *types.Config, filterService types.FilterEngine) *EventManager {
	return &EventManager{
		handlers:      make(map[string][]types.EventHandler),
		session:       session,
		config:        config,
		filterService: filterService,
	}
}

// RegisterHandler 注册事件处理器
func (em *EventManager) RegisterHandler(handler types.EventHandler) error {
	em.mu.Lock()
	defer em.mu.Unlock()

	eventType := handler.GetEventType()

	// 添加处理器到列表
	em.handlers[eventType] = append(em.handlers[eventType], handler)

	// 按优先级排序
	sort.Slice(em.handlers[eventType], func(i, j int) bool {
		return em.handlers[eventType][i].GetPriority() > em.handlers[eventType][j].GetPriority()
	})

	logger.Info("注册事件处理器",
		"event_type", eventType,
		"priority", handler.GetPriority(),
		"total_handlers", len(em.handlers[eventType]))

	return nil
}

// UnregisterHandler 注销事件处理器
func (em *EventManager) UnregisterHandler(eventType string, handler types.EventHandler) {
	em.mu.Lock()
	defer em.mu.Unlock()

	handlers := em.handlers[eventType]
	for i, h := range handlers {
		if h == handler {
			em.handlers[eventType] = append(handlers[:i], handlers[i+1:]...)
			logger.Info("注销事件处理器", "event_type", eventType)
			break
		}
	}
}

// GetHandlers 获取指定事件类型的处理器
func (em *EventManager) GetHandlers(eventType string) []types.EventHandler {
	em.mu.RLock()
	defer em.mu.RUnlock()

	handlers := make([]types.EventHandler, len(em.handlers[eventType]))
	copy(handlers, em.handlers[eventType])
	return handlers
}

// RegisterDefaultHandlers 注册默认事件处理器
func (em *EventManager) RegisterDefaultHandlers() error {
	defaultHandlers := []types.EventHandler{
		events.NewMessageCreateHandler(em.config),
		events.NewMessageUpdateHandler(),
		events.NewMessageDeleteHandler(),
		events.NewMessageReactionAddHandler(em.config, em.filterService),
		events.NewMessageReactionRemoveHandler(em.config, em.filterService),
		// events.NewGuildMemberAddHandler(em.config),
		// events.NewGuildMemberRemoveHandler(em.config),
	}

	for _, handler := range defaultHandlers {
		if err := em.RegisterHandler(handler); err != nil {
			return fmt.Errorf("注册默认事件处理器失败: %w", err)
		}
	}

	logger.Info("默认事件处理器注册完成", "count", len(defaultHandlers))
	return nil
}

// SetupEventListeners 设置事件监听器
func (em *EventManager) SetupEventListeners(client *types.Client) {
	// 消息事件
	em.session.AddHandler(func(s *discordgo.Session, m *discordgo.MessageCreate) {
		em.handleEvent(context.Background(), client, "MESSAGE_CREATE", m)
	})

	em.session.AddHandler(func(s *discordgo.Session, m *discordgo.MessageUpdate) {
		em.handleEvent(context.Background(), client, "MESSAGE_UPDATE", m)
	})

	em.session.AddHandler(func(s *discordgo.Session, m *discordgo.MessageDelete) {
		em.handleEvent(context.Background(), client, "MESSAGE_DELETE", m)
	})

	// 反应事件
	em.session.AddHandler(func(s *discordgo.Session, r *discordgo.MessageReactionAdd) {
		em.handleEvent(context.Background(), client, "MESSAGE_REACTION_ADD", r)
	})

	em.session.AddHandler(func(s *discordgo.Session, r *discordgo.MessageReactionRemove) {
		em.handleEvent(context.Background(), client, "MESSAGE_REACTION_REMOVE", r)
	})

	// 成员事件
	em.session.AddHandler(func(s *discordgo.Session, m *discordgo.GuildMemberAdd) {
		em.handleEvent(context.Background(), client, "GUILD_MEMBER_ADD", m)
	})

	em.session.AddHandler(func(s *discordgo.Session, m *discordgo.GuildMemberRemove) {
		em.handleEvent(context.Background(), client, "GUILD_MEMBER_REMOVE", m)
	})

	// 服务器事件
	em.session.AddHandler(func(s *discordgo.Session, g *discordgo.GuildCreate) {
		em.handleEvent(context.Background(), client, "GUILD_CREATE", g)
	})

	em.session.AddHandler(func(s *discordgo.Session, g *discordgo.GuildDelete) {
		em.handleEvent(context.Background(), client, "GUILD_DELETE", g)
	})

	// 准备就绪事件
	em.session.AddHandler(func(s *discordgo.Session, r *discordgo.Ready) {
		em.handleEvent(context.Background(), client, "READY", r)
	})

	logger.Info("事件监听器设置完成")
}

// handleEvent 处理事件
func (em *EventManager) handleEvent(ctx context.Context, client *types.Client, eventType string, event interface{}) {
	handlers := em.GetHandlers(eventType)
	if len(handlers) == 0 {
		return
	}

	logger.Debug("处理事件", "event_type", eventType, "handlers", len(handlers))

	for _, handler := range handlers {
		// 检查是否应该处理此事件
		if !handler.ShouldHandle(event) {
			continue
		}

		// 异步处理事件以避免阻塞，添加超时控制
		go func(h types.EventHandler) {
			defer func() {
				if r := recover(); r != nil {
					logger.Error("事件处理器发生 panic",
						"event_type", eventType,
						"handler", fmt.Sprintf("%T", h),
						"panic", r)
				}
			}()

			// 创建带超时的上下文，防止goroutine泄漏
			handlerCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
			defer cancel()

			// 使用channel来处理超时
			done := make(chan error, 1)
			go func() {
				done <- h.Handle(handlerCtx, client, event)
			}()

			select {
			case err := <-done:
				if err != nil {
					logger.Error("事件处理失败",
						"event_type", eventType,
						"handler", fmt.Sprintf("%T", h),
						"error", err)
				}
			case <-handlerCtx.Done():
				logger.Warn("事件处理超时",
					"event_type", eventType,
					"handler", fmt.Sprintf("%T", h),
					"timeout", "30s")
			}
		}(handler)
	}
}

// GetEventStats 获取事件统计信息
func (em *EventManager) GetEventStats() map[string]interface{} {
	em.mu.RLock()
	defer em.mu.RUnlock()

	stats := make(map[string]interface{})

	// 按事件类型统计处理器数量
	eventTypes := make(map[string]int)
	totalHandlers := 0

	for eventType, handlers := range em.handlers {
		eventTypes[eventType] = len(handlers)
		totalHandlers += len(handlers)
	}

	stats["total_handlers"] = totalHandlers
	stats["event_types"] = eventTypes

	// 处理器详情
	handlerDetails := make(map[string][]map[string]interface{})
	for eventType, handlers := range em.handlers {
		var details []map[string]interface{}
		for _, handler := range handlers {
			detail := map[string]interface{}{
				"type":     fmt.Sprintf("%T", handler),
				"priority": handler.GetPriority(),
			}
			details = append(details, detail)
		}
		handlerDetails[eventType] = details
	}
	stats["handler_details"] = handlerDetails

	return stats
}

// EmitEvent 手动触发事件（用于测试）
func (em *EventManager) EmitEvent(ctx context.Context, client *types.Client, eventType string, event interface{}) error {
	handlers := em.GetHandlers(eventType)
	if len(handlers) == 0 {
		return fmt.Errorf("没有找到事件类型 %s 的处理器", eventType)
	}

	logger.Debug("手动触发事件", "event_type", eventType, "handlers", len(handlers))

	for _, handler := range handlers {
		if !handler.ShouldHandle(event) {
			continue
		}

		if err := handler.Handle(ctx, client, event); err != nil {
			logger.Error("手动事件处理失败",
				"event_type", eventType,
				"handler", fmt.Sprintf("%T", handler),
				"error", err)
			return err
		}
	}

	return nil
}

// RemoveAllHandlers 移除所有处理器
func (em *EventManager) RemoveAllHandlers() {
	em.mu.Lock()
	defer em.mu.Unlock()

	em.handlers = make(map[string][]types.EventHandler)
	logger.Info("已移除所有事件处理器")
}

// GetSupportedEvents 获取支持的事件类型列表
func (em *EventManager) GetSupportedEvents() []string {
	em.mu.RLock()
	defer em.mu.RUnlock()

	var events []string
	for eventType := range em.handlers {
		events = append(events, eventType)
	}

	sort.Strings(events)
	return events
}

// IsEventSupported 检查是否支持指定事件类型
func (em *EventManager) IsEventSupported(eventType string) bool {
	em.mu.RLock()
	defer em.mu.RUnlock()

	_, exists := em.handlers[eventType]
	return exists
}

// GetHandlerCount 获取指定事件类型的处理器数量
func (em *EventManager) GetHandlerCount(eventType string) int {
	em.mu.RLock()
	defer em.mu.RUnlock()

	return len(em.handlers[eventType])
}

// EnableEventType 启用指定事件类型
func (em *EventManager) EnableEventType(eventType string) {
	// 这里可以添加事件类型启用/禁用的逻辑
	logger.Info("启用事件类型", "event_type", eventType)
}

// DisableEventType 禁用指定事件类型
func (em *EventManager) DisableEventType(eventType string) {
	// 这里可以添加事件类型启用/禁用的逻辑
	logger.Info("禁用事件类型", "event_type", eventType)
}
