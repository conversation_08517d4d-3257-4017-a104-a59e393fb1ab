package components

import (
	"context"
	"fmt"
	"time"

	"github.com/bwmarrin/discordgo"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"
)

// ConfirmButton 确认按钮组件
type ConfirmButton struct {
	customID string
	label    string
	style    discordgo.ButtonStyle
}

// NewConfirmButton 创建确认按钮
func NewConfirmButton() *ConfirmButton {
	return &ConfirmButton{
		customID: "confirm_button",
		label:    "确认",
		style:    discordgo.PrimaryButton,
	}
}

// GetCustomID 获取自定义 ID
func (c *ConfirmButton) GetCustomID() string {
	return c.customID
}

// GetType 获取组件类型
func (c *ConfirmButton) GetType() types.ComponentType {
	return types.ComponentTypeButton
}

// Handle 处理按钮点击
func (c *ConfirmButton) Handle(ctx context.Context, client *types.Client, interaction *discordgo.InteractionCreate) error {
	var user *discordgo.User
	if interaction.Member != nil {
		user = interaction.Member.User
	} else {
		user = interaction.User
	}
	
	logger.Info("确认按钮被点击",
		"user", user.Username,
		"guild", interaction.GuildID,
		"channel", interaction.ChannelID)
	
	// 创建确认响应
	embed := &discordgo.MessageEmbed{
		Title:       "✅ 操作已确认",
		Description: "你的操作已经被确认并执行。",
		Color:       0x00ff00,
		Footer: &discordgo.MessageEmbedFooter{
			Text: fmt.Sprintf("操作者: %s", user.Username),
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}
	
	return client.Session.InteractionRespond(interaction.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseUpdateMessage,
		Data: &discordgo.InteractionResponseData{
			Embeds:     []*discordgo.MessageEmbed{embed},
			Components: []discordgo.MessageComponent{}, // 移除按钮
		},
	})
}

// Validate 验证交互
func (c *ConfirmButton) Validate(interaction *discordgo.InteractionCreate) error {
	// 确认按钮无需特殊验证
	return nil
}

// GetComponent 获取 Discord 组件
func (c *ConfirmButton) GetComponent() discordgo.MessageComponent {
	return discordgo.Button{
		Label:    c.label,
		Style:    c.style,
		CustomID: c.customID,
	}
}

// CancelButton 取消按钮组件
type CancelButton struct {
	customID string
	label    string
	style    discordgo.ButtonStyle
}

// NewCancelButton 创建取消按钮
func NewCancelButton() *CancelButton {
	return &CancelButton{
		customID: "cancel_button",
		label:    "取消",
		style:    discordgo.SecondaryButton,
	}
}

// GetCustomID 获取自定义 ID
func (c *CancelButton) GetCustomID() string {
	return c.customID
}

// GetType 获取组件类型
func (c *CancelButton) GetType() types.ComponentType {
	return types.ComponentTypeButton
}

// Handle 处理按钮点击
func (c *CancelButton) Handle(ctx context.Context, client *types.Client, interaction *discordgo.InteractionCreate) error {
	var user *discordgo.User
	if interaction.Member != nil {
		user = interaction.Member.User
	} else {
		user = interaction.User
	}
	
	logger.Info("取消按钮被点击",
		"user", user.Username,
		"guild", interaction.GuildID,
		"channel", interaction.ChannelID)
	
	// 创建取消响应
	embed := &discordgo.MessageEmbed{
		Title:       "❌ 操作已取消",
		Description: "操作已被取消，没有进行任何更改。",
		Color:       0xff0000,
		Footer: &discordgo.MessageEmbedFooter{
			Text: fmt.Sprintf("操作者: %s", user.Username),
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}
	
	return client.Session.InteractionRespond(interaction.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseUpdateMessage,
		Data: &discordgo.InteractionResponseData{
			Embeds:     []*discordgo.MessageEmbed{embed},
			Components: []discordgo.MessageComponent{}, // 移除按钮
		},
	})
}

// Validate 验证交互
func (c *CancelButton) Validate(interaction *discordgo.InteractionCreate) error {
	// 取消按钮无需特殊验证
	return nil
}

// GetComponent 获取 Discord 组件
func (c *CancelButton) GetComponent() discordgo.MessageComponent {
	return discordgo.Button{
		Label:    c.label,
		Style:    c.style,
		CustomID: c.customID,
	}
}

// DeleteButton 删除按钮组件
type DeleteButton struct {
	customID string
	label    string
	style    discordgo.ButtonStyle
}

// NewDeleteButton 创建删除按钮
func NewDeleteButton() *DeleteButton {
	return &DeleteButton{
		customID: "delete_button",
		label:    "删除",
		style:    discordgo.DangerButton,
	}
}

// GetCustomID 获取自定义 ID
func (d *DeleteButton) GetCustomID() string {
	return d.customID
}

// GetType 获取组件类型
func (d *DeleteButton) GetType() types.ComponentType {
	return types.ComponentTypeButton
}

// Handle 处理按钮点击
func (d *DeleteButton) Handle(ctx context.Context, client *types.Client, interaction *discordgo.InteractionCreate) error {
	var user *discordgo.User
	if interaction.Member != nil {
		user = interaction.Member.User
	} else {
		user = interaction.User
	}
	
	logger.Info("删除按钮被点击",
		"user", user.Username,
		"guild", interaction.GuildID,
		"channel", interaction.ChannelID)
	
	// 删除原消息
	err := client.Session.InteractionRespond(interaction.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseUpdateMessage,
		Data: &discordgo.InteractionResponseData{
			Content:    "🗑️ 消息已删除",
			Embeds:     []*discordgo.MessageEmbed{},
			Components: []discordgo.MessageComponent{},
		},
	})
	
	if err != nil {
		return err
	}
	
	// 延迟删除响应消息
	go func() {
		time.Sleep(3 * time.Second)
		client.Session.InteractionResponseDelete(interaction.Interaction)
	}()
	
	return nil
}

// Validate 验证交互
func (d *DeleteButton) Validate(interaction *discordgo.InteractionCreate) error {
	// 删除按钮无需特殊验证
	return nil
}

// GetComponent 获取 Discord 组件
func (d *DeleteButton) GetComponent() discordgo.MessageComponent {
	return discordgo.Button{
		Label:    d.label,
		Style:    d.style,
		CustomID: d.customID,
	}
}

// PaginationButtons 分页按钮组件
type PaginationButtons struct {
	prevCustomID string
	nextCustomID string
	currentPage  int
	totalPages   int
}

// NewPaginationButtons 创建分页按钮
func NewPaginationButtons(currentPage, totalPages int) *PaginationButtons {
	return &PaginationButtons{
		prevCustomID: fmt.Sprintf("page_prev_%d", currentPage),
		nextCustomID: fmt.Sprintf("page_next_%d", currentPage),
		currentPage:  currentPage,
		totalPages:   totalPages,
	}
}

// GetPrevButton 获取上一页按钮
func (p *PaginationButtons) GetPrevButton() *PrevPageButton {
	return &PrevPageButton{
		customID: p.prevCustomID,
		disabled: p.currentPage <= 1,
	}
}

// GetNextButton 获取下一页按钮
func (p *PaginationButtons) GetNextButton() *NextPageButton {
	return &NextPageButton{
		customID: p.nextCustomID,
		disabled: p.currentPage >= p.totalPages,
	}
}

// GetComponents 获取分页组件
func (p *PaginationButtons) GetComponents() []discordgo.MessageComponent {
	return []discordgo.MessageComponent{
		discordgo.ActionsRow{
			Components: []discordgo.MessageComponent{
				discordgo.Button{
					Label:    "◀️ 上一页",
					Style:    discordgo.SecondaryButton,
					CustomID: p.prevCustomID,
					Disabled: p.currentPage <= 1,
				},
				discordgo.Button{
					Label:    fmt.Sprintf("%d / %d", p.currentPage, p.totalPages),
					Style:    discordgo.SecondaryButton,
					CustomID: "page_info",
					Disabled: true,
				},
				discordgo.Button{
					Label:    "下一页 ▶️",
					Style:    discordgo.SecondaryButton,
					CustomID: p.nextCustomID,
					Disabled: p.currentPage >= p.totalPages,
				},
			},
		},
	}
}

// PrevPageButton 上一页按钮
type PrevPageButton struct {
	customID string
	disabled bool
}

// GetCustomID 获取自定义 ID
func (p *PrevPageButton) GetCustomID() string {
	return p.customID
}

// GetType 获取组件类型
func (p *PrevPageButton) GetType() types.ComponentType {
	return types.ComponentTypeButton
}

// Handle 处理按钮点击
func (p *PrevPageButton) Handle(ctx context.Context, client *types.Client, interaction *discordgo.InteractionCreate) error {
	// 这里应该实现分页逻辑
	// 暂时返回一个简单的响应
	return client.Session.InteractionRespond(interaction.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Content: "上一页功能需要在具体使用场景中实现",
			Flags:   discordgo.MessageFlagsEphemeral,
		},
	})
}

// Validate 验证交互
func (p *PrevPageButton) Validate(interaction *discordgo.InteractionCreate) error {
	return nil
}

// NextPageButton 下一页按钮
type NextPageButton struct {
	customID string
	disabled bool
}

// GetCustomID 获取自定义 ID
func (n *NextPageButton) GetCustomID() string {
	return n.customID
}

// GetType 获取组件类型
func (n *NextPageButton) GetType() types.ComponentType {
	return types.ComponentTypeButton
}

// Handle 处理按钮点击
func (n *NextPageButton) Handle(ctx context.Context, client *types.Client, interaction *discordgo.InteractionCreate) error {
	// 这里应该实现分页逻辑
	// 暂时返回一个简单的响应
	return client.Session.InteractionRespond(interaction.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Content: "下一页功能需要在具体使用场景中实现",
			Flags:   discordgo.MessageFlagsEphemeral,
		},
	})
}

// Validate 验证交互
func (n *NextPageButton) Validate(interaction *discordgo.InteractionCreate) error {
	return nil
}
