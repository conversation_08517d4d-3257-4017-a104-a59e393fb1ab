package components

import (
	"context"
	"fmt"
	"strings"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// RoleSelectMenu 角色选择菜单
type RoleSelectMenu struct {
	customID    string
	placeholder string
	minValues   int
	maxValues   int
	options     []discordgo.SelectMenuOption
}

// NewRoleSelectMenu 创建角色选择菜单
func NewRoleSelectMenu(roles []discordgo.Role) *RoleSelectMenu {
	var options []discordgo.SelectMenuOption

	for _, role := range roles {
		// 跳过 @everyone 角色和管理员角色
		if role.ID == role.ID || role.Permissions&discordgo.PermissionAdministrator != 0 {
			continue
		}

		options = append(options, discordgo.SelectMenuOption{
			Label:       role.Name,
			Value:       role.ID,
			Description: fmt.Sprintf("选择 %s 角色", role.Name),
			Emoji: &discordgo.ComponentEmoji{
				Name: "🎭",
			},
		})
	}

	return &RoleSelectMenu{
		customID:    "role_select",
		placeholder: "选择你想要的角色...",
		minValues:   0,
		maxValues:   len(options),
		options:     options,
	}
}

// GetCustomID 获取自定义 ID
func (r *RoleSelectMenu) GetCustomID() string {
	return r.customID
}

// GetType 获取组件类型
func (r *RoleSelectMenu) GetType() types.ComponentType {
	return types.ComponentTypeSelectMenu
}

// Handle 处理选择菜单交互
func (r *RoleSelectMenu) Handle(ctx context.Context, client *types.Client, interaction *discordgo.InteractionCreate) error {
	var user *discordgo.User
	if interaction.Member != nil {
		user = interaction.Member.User
	} else {
		user = interaction.User
	}

	data := interaction.MessageComponentData()
	selectedRoles := data.Values

	logger.Info("角色选择菜单被使用",
		"user", user.Username,
		"guild", interaction.GuildID,
		"selected_roles", len(selectedRoles))

	// 处理角色分配
	var addedRoles, removedRoles []string

	// 获取用户当前角色
	member := interaction.Member
	if member == nil {
		return fmt.Errorf("无法获取成员信息")
	}

	currentRoles := make(map[string]bool)
	for _, roleID := range member.Roles {
		currentRoles[roleID] = true
	}

	// 确定要添加和移除的角色
	selectedRoleMap := make(map[string]bool)
	for _, roleID := range selectedRoles {
		selectedRoleMap[roleID] = true
		if !currentRoles[roleID] {
			addedRoles = append(addedRoles, roleID)
		}
	}

	// 检查需要移除的角色（在菜单选项中但未被选择的）
	for _, option := range r.options {
		if currentRoles[option.Value] && !selectedRoleMap[option.Value] {
			removedRoles = append(removedRoles, option.Value)
		}
	}

	// 执行角色更改
	for _, roleID := range addedRoles {
		err := client.Session.GuildMemberRoleAdd(interaction.GuildID, user.ID, roleID)
		if err != nil {
			logger.Error("添加角色失败", "role", roleID, "user", user.Username, "error", err)
		}
	}

	for _, roleID := range removedRoles {
		err := client.Session.GuildMemberRoleRemove(interaction.GuildID, user.ID, roleID)
		if err != nil {
			logger.Error("移除角色失败", "role", roleID, "user", user.Username, "error", err)
		}
	}

	// 创建响应消息
	var description strings.Builder
	if len(addedRoles) > 0 {
		description.WriteString(fmt.Sprintf("✅ 添加了 %d 个角色\n", len(addedRoles)))
	}
	if len(removedRoles) > 0 {
		description.WriteString(fmt.Sprintf("❌ 移除了 %d 个角色\n", len(removedRoles)))
	}
	if len(addedRoles) == 0 && len(removedRoles) == 0 {
		description.WriteString("ℹ️ 没有角色变更")
	}

	embed := &discordgo.MessageEmbed{
		Title:       "🎭 角色更新完成",
		Description: description.String(),
		Color:       0x3498db,
		Footer: &discordgo.MessageEmbedFooter{
			Text: fmt.Sprintf("操作者: %s", user.Username),
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	return client.Session.InteractionRespond(interaction.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Embeds: []*discordgo.MessageEmbed{embed},
			Flags:  discordgo.MessageFlagsEphemeral,
		},
	})
}

// Validate 验证交互
func (r *RoleSelectMenu) Validate(interaction *discordgo.InteractionCreate) error {
	data := interaction.MessageComponentData()

	if len(data.Values) < r.minValues {
		return fmt.Errorf("至少需要选择 %d 个选项", r.minValues)
	}

	if len(data.Values) > r.maxValues {
		return fmt.Errorf("最多只能选择 %d 个选项", r.maxValues)
	}

	return nil
}

// GetComponent 获取 Discord 组件
func (r *RoleSelectMenu) GetComponent() discordgo.MessageComponent {
	return discordgo.SelectMenu{
		CustomID:    r.customID,
		Placeholder: r.placeholder,
		MinValues:   &r.minValues,
		MaxValues:   r.maxValues,
		Options:     r.options,
	}
}

// LanguageSelectMenu 语言选择菜单
type LanguageSelectMenu struct {
	customID    string
	placeholder string
	options     []discordgo.SelectMenuOption
}

// NewLanguageSelectMenu 创建语言选择菜单
func NewLanguageSelectMenu() *LanguageSelectMenu {
	options := []discordgo.SelectMenuOption{
		{
			Label:       "中文",
			Value:       "zh-CN",
			Description: "设置语言为中文",
			Emoji: &discordgo.ComponentEmoji{
				Name: "🇨🇳",
			},
		},
		{
			Label:       "English",
			Value:       "en-US",
			Description: "Set language to English",
			Emoji: &discordgo.ComponentEmoji{
				Name: "🇺🇸",
			},
		},
		{
			Label:       "日本語",
			Value:       "ja-JP",
			Description: "言語を日本語に設定",
			Emoji: &discordgo.ComponentEmoji{
				Name: "🇯🇵",
			},
		},
		{
			Label:       "한국어",
			Value:       "ko-KR",
			Description: "언어를 한국어로 설정",
			Emoji: &discordgo.ComponentEmoji{
				Name: "🇰🇷",
			},
		},
	}

	return &LanguageSelectMenu{
		customID:    "language_select",
		placeholder: "选择你的语言 / Choose your language",
		options:     options,
	}
}

// GetCustomID 获取自定义 ID
func (l *LanguageSelectMenu) GetCustomID() string {
	return l.customID
}

// GetType 获取组件类型
func (l *LanguageSelectMenu) GetType() types.ComponentType {
	return types.ComponentTypeSelectMenu
}

// Handle 处理选择菜单交互
func (l *LanguageSelectMenu) Handle(ctx context.Context, client *types.Client, interaction *discordgo.InteractionCreate) error {
	var user *discordgo.User
	if interaction.Member != nil {
		user = interaction.Member.User
	} else {
		user = interaction.User
	}

	data := interaction.MessageComponentData()
	selectedLanguage := data.Values[0]

	logger.Info("语言选择菜单被使用",
		"user", user.Username,
		"selected_language", selectedLanguage)

	// 这里应该保存用户的语言偏好到数据库
	// 暂时只返回确认消息

	var responseText string
	switch selectedLanguage {
	case "zh-CN":
		responseText = "✅ 语言已设置为中文"
	case "en-US":
		responseText = "✅ Language has been set to English"
	case "ja-JP":
		responseText = "✅ 言語が日本語に設定されました"
	case "ko-KR":
		responseText = "✅ 언어가 한국어로 설정되었습니다"
	default:
		responseText = "✅ Language preference saved"
	}

	embed := &discordgo.MessageEmbed{
		Title:       "🌐 语言设置",
		Description: responseText,
		Color:       0x00ff00,
		Footer: &discordgo.MessageEmbedFooter{
			Text: fmt.Sprintf("用户: %s", user.Username),
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	return client.Session.InteractionRespond(interaction.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Embeds: []*discordgo.MessageEmbed{embed},
			Flags:  discordgo.MessageFlagsEphemeral,
		},
	})
}

// Validate 验证交互
func (l *LanguageSelectMenu) Validate(interaction *discordgo.InteractionCreate) error {
	data := interaction.MessageComponentData()

	if len(data.Values) != 1 {
		return fmt.Errorf("必须选择一种语言")
	}

	return nil
}

// GetComponent 获取 Discord 组件
func (l *LanguageSelectMenu) GetComponent() discordgo.MessageComponent {
	return discordgo.SelectMenu{
		CustomID:    l.customID,
		Placeholder: l.placeholder,
		MinValues:   &[]int{1}[0],
		MaxValues:   1,
		Options:     l.options,
	}
}

// CategorySelectMenu 分类选择菜单
type CategorySelectMenu struct {
	customID    string
	placeholder string
	categories  map[string]string
	options     []discordgo.SelectMenuOption
}

// NewCategorySelectMenu 创建分类选择菜单
func NewCategorySelectMenu(categories map[string]string) *CategorySelectMenu {
	var options []discordgo.SelectMenuOption

	for value, label := range categories {
		options = append(options, discordgo.SelectMenuOption{
			Label:       label,
			Value:       value,
			Description: fmt.Sprintf("查看 %s 相关内容", label),
		})
	}

	return &CategorySelectMenu{
		customID:    "category_select",
		placeholder: "选择一个分类...",
		categories:  categories,
		options:     options,
	}
}

// GetCustomID 获取自定义 ID
func (c *CategorySelectMenu) GetCustomID() string {
	return c.customID
}

// GetType 获取组件类型
func (c *CategorySelectMenu) GetType() types.ComponentType {
	return types.ComponentTypeSelectMenu
}

// Handle 处理选择菜单交互
func (c *CategorySelectMenu) Handle(ctx context.Context, client *types.Client, interaction *discordgo.InteractionCreate) error {
	var user *discordgo.User
	if interaction.Member != nil {
		user = interaction.Member.User
	} else {
		user = interaction.User
	}

	data := interaction.MessageComponentData()
	selectedCategory := data.Values[0]

	logger.Info("分类选择菜单被使用",
		"user", user.Username,
		"selected_category", selectedCategory)

	categoryName := c.categories[selectedCategory]

	embed := &discordgo.MessageEmbed{
		Title:       fmt.Sprintf("📂 %s", categoryName),
		Description: fmt.Sprintf("你选择了 **%s** 分类。", categoryName),
		Color:       0x3498db,
		Footer: &discordgo.MessageEmbedFooter{
			Text: fmt.Sprintf("选择者: %s", user.Username),
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	return client.Session.InteractionRespond(interaction.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Embeds: []*discordgo.MessageEmbed{embed},
			Flags:  discordgo.MessageFlagsEphemeral,
		},
	})
}

// Validate 验证交互
func (c *CategorySelectMenu) Validate(interaction *discordgo.InteractionCreate) error {
	data := interaction.MessageComponentData()

	if len(data.Values) != 1 {
		return fmt.Errorf("必须选择一个分类")
	}

	selectedCategory := data.Values[0]
	if _, exists := c.categories[selectedCategory]; !exists {
		return fmt.Errorf("无效的分类选择")
	}

	return nil
}

// GetComponent 获取 Discord 组件
func (c *CategorySelectMenu) GetComponent() discordgo.MessageComponent {
	return discordgo.SelectMenu{
		CustomID:    c.customID,
		Placeholder: c.placeholder,
		MinValues:   &[]int{1}[0],
		MaxValues:   1,
		Options:     c.options,
	}
}
