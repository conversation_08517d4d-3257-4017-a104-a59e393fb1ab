# golangci-lint 配置文件
# 用于 Zeka Discord Bot Go 项目的代码质量检查
# 符合 golangci-lint v1.55+ 格式

version: "2"

run:
  timeout: 5m
  issues-exit-code: 1
  tests: true

linters:
  enable:
    - bodyclose
    - depguard
    - dogsled
    - dupl
    - errcheck
    - funlen
    - gochecknoinits
    - goconst
    - gocritic
    - gocyclo
    - gofmt
    - goimports
    - mnd
    - goprintffuncname
    - gosec
    - gosimple
    - govet
    - ineffassign
    - lll
    - misspell
    - nakedret
    - revive
    - rowserrcheck
    - staticcheck
    - stylecheck
    - typecheck
    - unconvert
    - unparam
    - unused
    - whitespace
  disable:
    - prealloc

linters:
  settings:
    govet:
      check-shadowing: true
      enable:
        - fieldalignment
    revive:
      min-confidence: 0.8
      rules:
        - name: exported
          severity: warning
          disabled: false
        - name: var-naming
          severity: warning
          disabled: false
    gocyclo:
      min-complexity: 15
    dupl:
      threshold: 100
    goconst:
      min-len: 2
      min-occurrences: 2
    misspell:
      locale: US
    lll:
      line-length: 120
    goimports:
      local-prefixes: zeka-go
    gocritic:
      enabled-tags:
        - performance
        - style
        - experimental
      disabled-checks:
        - wrapperFunc
        - dupImport
        - ifElseChain
        - octalLiteral
    funlen:
      lines: 100
      statements: 50

issues:
  max-issues-per-linter: 0
  max-same-issues: 0

linters:
  exclusions:
    rules:
      - path: _test\.go
        linters:
          - mnd
          - funlen
          - gocyclo
      - path: cmd/
        linters:
          - gochecknoinits
      - linters:
          - lll
        source: "^//go:generate "

severity:
  default: error
