#!/bin/bash

# Zeka Bot - 代码质量检查脚本
# 用于安装和运行 golangci-lint

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 golangci-lint 是否已安装
check_golangci_lint() {
    if command -v golangci-lint &> /dev/null; then
        local version=$(golangci-lint --version | head -n1)
        log_info "golangci-lint 已安装: $version"
        return 0
    else
        return 1
    fi
}

# 安装 golangci-lint
install_golangci_lint() {
    log_info "正在安装 golangci-lint..."
    
    # 检查操作系统
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install golangci-lint
        else
            log_warning "未找到 Homebrew，使用 curl 安装"
            curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
    
    log_success "golangci-lint 安装完成"
}

# 运行代码检查
run_lint() {
    log_info "运行代码质量检查..."
    
    # 检查配置文件是否存在
    if [[ ! -f ".golangci.yml" ]]; then
        log_error "未找到 .golangci.yml 配置文件"
        exit 1
    fi
    
    # 运行 golangci-lint
    if golangci-lint run ./...; then
        log_success "代码质量检查通过！"
    else
        log_error "代码质量检查发现问题，请修复后重试"
        exit 1
    fi
}

# 修复可自动修复的问题
fix_issues() {
    log_info "尝试自动修复代码问题..."
    
    golangci-lint run --fix ./...
    
    log_success "自动修复完成"
}

# 显示帮助信息
show_help() {
    cat << EOF
Zeka Bot 代码质量检查脚本

用法: $0 [action]

动作:
  check     运行代码质量检查
  fix       自动修复可修复的问题
  install   安装 golangci-lint
  help      显示帮助

示例:
  $0 check     # 运行代码检查
  $0 fix       # 自动修复问题
  $0 install   # 安装 golangci-lint

配置文件: .golangci.yml

EOF
}

# 主函数
main() {
    if [[ $# -eq 0 ]]; then
        show_help
        exit 1
    fi
    
    local action="$1"
    
    case "$action" in
        check)
            if ! check_golangci_lint; then
                log_warning "golangci-lint 未安装，正在安装..."
                install_golangci_lint
            fi
            run_lint
            ;;
        fix)
            if ! check_golangci_lint; then
                log_warning "golangci-lint 未安装，正在安装..."
                install_golangci_lint
            fi
            fix_issues
            ;;
        install)
            install_golangci_lint
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "无效的动作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
