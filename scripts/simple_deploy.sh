#!/bin/bash

# Zeka Bot 简化部署脚本
# 用法: ./scripts/simple_deploy.sh [action]

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 构建应用
build_app() {
    log_info "构建 Zeka Bot..."
    
    # 清理旧的构建文件
    rm -f zeka
    
    # 构建应用
    go build -o zeka ./cmd/zeka
    
    log_success "构建完成"
}

# 构建 Docker 镜像
build_docker() {
    log_info "构建 Docker 镜像..."
    
    docker build -t zeka-bot:latest .
    
    log_success "Docker 镜像构建完成"
}

# 部署服务
deploy() {
    log_info "部署 Zeka Bot..."
    
    # 检查环境文件
    if [[ ! -f ".env" ]]; then
        if [[ -f ".env.example" ]]; then
            log_warning "未找到 .env 文件，请复制 .env.example 并配置"
            cp .env.example .env
        else
            log_error "未找到环境配置文件"
            exit 1
        fi
    fi
    
    # 使用 Docker Compose 部署
    if [[ -f "docker-compose.prod.yml" ]]; then
        docker-compose -f docker-compose.prod.yml up -d
    else
        log_error "未找到 docker-compose.prod.yml 文件"
        exit 1
    fi
    
    log_success "部署完成"
}

# 停止服务
stop() {
    log_info "停止 Zeka Bot..."
    
    if [[ -f "docker-compose.prod.yml" ]]; then
        docker-compose -f docker-compose.prod.yml down
    else
        log_warning "未找到 docker-compose.prod.yml，尝试停止容器"
        docker stop zeka-bot 2>/dev/null || true
        docker rm zeka-bot 2>/dev/null || true
    fi
    
    log_success "服务已停止"
}

# 查看日志
logs() {
    log_info "查看 Zeka Bot 日志..."
    
    if [[ -f "docker-compose.prod.yml" ]]; then
        docker-compose -f docker-compose.prod.yml logs -f zeka-bot
    else
        docker logs -f zeka-bot 2>/dev/null || log_error "未找到 zeka-bot 容器"
    fi
}

# 清理资源
cleanup() {
    log_info "清理资源..."
    
    # 清理未使用的 Docker 镜像
    docker image prune -f
    
    # 清理构建文件
    rm -f zeka
    
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    cat << EOF
Zeka Bot 简化部署脚本

用法: $0 [action]

动作:
  build     构建应用
  docker    构建 Docker 镜像
  deploy    部署服务
  stop      停止服务
  logs      查看日志
  cleanup   清理资源
  help      显示帮助

示例:
  $0 build     # 构建应用
  $0 docker    # 构建 Docker 镜像
  $0 deploy    # 部署服务
  $0 logs      # 查看日志

EOF
}

# 主函数
main() {
    if [[ $# -eq 0 ]]; then
        show_help
        exit 1
    fi
    
    local action="$1"
    
    case "$action" in
        build)
            build_app
            ;;
        docker)
            build_docker
            ;;
        deploy)
            build_docker
            deploy
            ;;
        stop)
            stop
            ;;
        logs)
            logs
            ;;
        cleanup)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "无效的动作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
