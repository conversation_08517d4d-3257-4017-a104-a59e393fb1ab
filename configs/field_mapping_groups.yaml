# 字段映射组配置文件
# 基于 docs/enhanced_system_design_v3.md 中的设计规范
# 支持从Discord Embed字段到ProductItem字段的映射和转换

field_mapping_groups:
  # Amazon标准映射组
  amazon_standard:
    name: "Amazon标准映射"
    platform: "amazon"
    description: "适用于Amazon产品信息的标准字段映射"
    
    # 只定义Amazon特有的映射规则，其他使用base_mappings
    mappings:
      - source_field: "ASIN"           # Amazon特有字段
        target_field: "ProductID"
        required: true
        description: "Amazon产品唯一标识符"

      - source_field: "PRICE"         # 覆盖base_mappings中的Price映射
        target_field: "Price"
        transform: "parse_price"
        description: "Amazon价格格式"

    # 默认值
    defaults:
      Platform: "amazon"
      Country: "US"
      Availability: "unknown"
      Stock: 0

    # 数据转换规则
    transforms:
      parse_price:
        type: "regex"
        pattern: "\\$?([0-9,]+\\.?[0-9]*)"
        description: "提取价格数字部分"

  # eBay标准映射组
  ebay_standard:
    name: "eBay标准映射"
    platform: "ebay"
    description: "适用于eBay产品信息的标准字段映射"
    
    mappings:
      - source_field: "Item ID"
        target_field: "ProductID"
        required: true
        description: "eBay商品ID"

      - source_field: "Title"
        target_field: "Title"
        fallback_fields: ["Item Title"]
        description: "商品标题"

      - source_field: "Price"
        target_field: "Price"
        transform: "parse_price"
        description: "商品价格"

      - source_field: "Buy It Now"
        target_field: "AtcLink"
        description: "立即购买链接"

      - source_field: "Seller"
        target_field: "Platform"
        transform: "add_ebay_prefix"
        description: "卖家信息"

    defaults:
      Platform: "ebay"
      Country: "US"
      Availability: "unknown"
      Stock: 0

    transforms:
      parse_price:
        type: "regex"
        pattern: "\\$?([0-9,]+\\.?[0-9]*)"
        description: "提取价格数字部分"
      add_ebay_prefix:
        type: "template"
        template: "ebay-{value}"
        description: "添加eBay前缀"

  # AliExpress标准映射组
  aliexpress_popmart_v2:
    name: "Aliexpress Popmart V2"
    author_name: "Aliexpress Popmart V2"
    platform: "aliexpress"
    description: "适用于AliExpress产品信息的标准字段映射"
    
    mappings:
      - source_field: "ATC [Stock]"
        target_field: "ProductID"
        transform: "parse_product_id"
        fallback_fields: ["商品ID", "ID"]
        description: "AliExpress商品ID"
      
      - source_field: "ATC [Stock]"
        target_field: "SkuID"
        transform: "parse_sku_id"
        fallback_fields: ["货号"]
        description: "商品货号"

      - source_field: "Inventory"
        target_field: "Stock"
        fallback_fields: ["剩余库存", "库存"]
        description: "商品标题"

      - source_field: "Price"
        target_field: "Price"
        fallback_fields: ["价格", "售价"]
        description: "商品价格，支持多种货币"

    defaults:
      Availability: "In Stock"
      Platform: "aliexpress"

    transforms:
      parse_product_id:
        type: "regex"
        pattern: "objectId=([0-9]+)"
        description: "从AliExpress链接中提取产品ID"
      parse_sku_id:
        type: "regex"
        pattern: "skuId=([0-9]+)"
        description: "从AliExpress链接中提取SKU ID"

  # 通用映射组
  generic:
    name: "通用映射"
    platform: "generic"
    description: "适用于通用产品信息的基础字段映射"
    
    mappings:
      - source_field: "Price"
        target_field: "Price"
        transform: "parse_price_multi_currency"
        fallback_fields: ["价格", "售价", "Cost"]
        description: "产品价格"

      - source_field: "ID"
        target_field: "ProductID"
        fallback_fields: ["Product ID", "商品ID", "编号"]
        description: "产品ID"

      - source_field: "Image"
        target_field: "ImageURL"
        fallback_fields: ["Picture", "图片", "Thumbnail"]
        description: "产品图片"

    defaults:
      Platform: "unknown"
      Country: "unknown"
      Availability: "unknown"
      Stock: 0

    transforms:
      parse_price_multi_currency:
        type: "regex"
        pattern: "([¥$€£]?[0-9,]+\\.?[0-9]*)"
        description: "提取多种货币格式的价格"

  # 默认映射组（当没有指定映射组时使用）
  default:
    name: "默认映射"
    platform: "default"
    description: "默认的基础字段映射，当没有指定其他映射组时使用"

    # 使用base_mappings，无需重复定义
    mappings: []

    defaults:
      Platform: "unknown"
      Availability: "unknown"
      Stock: 0

# 全局配置
global_settings:
  # 通用基础映射 - 所有映射组都会继承这些规则
  base_mappings:
    # Discord Embed 基础字段映射
    - source_field: "Title"
      target_field: "Title"
      fallback_fields: ["title", "name", "product_name"]
      description: "产品标题"

    - source_field: "URL"
      target_field: "URL"
      fallback_fields: ["url", "link", "embed_url"]
      description: "产品链接"

    # Discord Embed 作者信息
    - source_field: "AuthorName"
      target_field: "AuthorName"
      fallback_fields: ["author", "author_name", "monitor"]
      description: "作者或监控系统名称"

    - source_field: "AuthorURL"
      target_field: "AuthorURL"
      fallback_fields: ["author_url", "author_link"]
      description: "作者链接"

    - source_field: "AuthorIconURL"
      target_field: "AuthorIconURL"
      fallback_fields: ["author_icon_url", "author_icon"]
      description: "作者图标链接"

    # Discord Embed 页脚信息
    - source_field: "FooterText"
      target_field: "FooterText"
      fallback_fields: ["footer", "footer_text"]
      description: "页脚信息"

    - source_field: "FooterIconURL"
      target_field: "FooterIconURL"
      fallback_fields: ["footer_icon_url", "footer_icon"]
      description: "页脚图标链接"

    # Discord Embed 图片信息
    - source_field: "ThumbnailURL"
      target_field: "ThumbnailURL"
      fallback_fields: ["thumbnail", "image", "image_url"]
      description: "产品图片"

    - source_field: "ImageURL"
      target_field: "ImageURL"
      fallback_fields: ["image_url", "picture", "photo"]
      description: "产品大图"

    # Discord Embed 其他信息
    - source_field: "Color"
      target_field: "Color"
      fallback_fields: ["color", "embed_color"]
      description: "Embed颜色"

    - source_field: "Timestamp"
      target_field: "Timestamp"
      fallback_fields: ["timestamp", "time"]
      description: "时间戳"

    - source_field: "Description"
      target_field: "Description"
      fallback_fields: ["description", "desc", "details"]
      description: "产品描述"

  # 默认使用的映射组
  default_mapping_group: "default"

  # 是否启用自动字段检测
  enable_auto_detection: true
  
  # 字段映射缓存设置
  cache_settings:
    enabled: true
    ttl: "1h"  # 缓存生存时间
    max_size: 1000  # 最大缓存条目数
  
  # 日志设置
  logging:
    enabled: true
    level: "info"
    log_mapping_results: false  # 是否记录映射结果（调试用）
  
  # 错误处理
  error_handling:
    ignore_missing_required_fields: false
    use_fallback_on_transform_error: true
    log_transform_errors: true
