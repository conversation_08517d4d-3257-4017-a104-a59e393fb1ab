# 转发规则配置示例
# 严格遵循 docs/enhanced_system_design_v3.md 中的设计规范

# 统一配置格式
forward_rules:
  enabled: true

  # 字段映射组文件引用
  field_mapping_groups_file: "configs/field_mapping_groups.yaml"

  # 转发规则（严格1对1模式）
  rules:
    # 示例1：Amazon产品转发
    - input_channel: "1380203642526634064"   # Amazon监听频道
      output_channel: "1356648190023307304"  # 产品展示频道
      field_mapping_group: "amazon_standard"
      # name 自动生成为: forward_634064_to_307304
      # delay 默认为: 0s
      # enabled 默认为: true

    # 示例2：同一输入，不同输出（1对多转发实现）
    - input_channel: "1380203642526634064"   # 同一输入
      output_channel: "1356648190023307305"  # 通知频道
      field_mapping_group: "amazon_standard"
      # name 自动生成为: forward_634064_to_307305

    # 示例3：eBay产品转发
    - input_channel: "111222333444555666"    # eBay监听频道
      output_channel: "666555444333222111"   # eBay产品频道
      field_mapping_group: "ebay_standard"

    # 示例4：AliExpress产品转发
    - input_channel: "777888999000111222"    # AliExpress监听频道
      output_channel: "222111000999888777"   # AliExpress产品频道
      field_mapping_group: "aliexpress_standard"

    # 示例5：通用产品转发（明确禁用）
    - input_channel: "333444555666777888"
      output_channel: "888777666555444333"
      field_mapping_group: "generic"
      enabled: false  # 明确禁用此规则

# 全局设置
global_settings:
  # 默认字段映射组
  default_field_mapping_group: "generic"
  
  # 默认延迟时间（秒）
  default_delay_seconds: 0
  
  # 全局速率限制
  global_rate_limit:
    max_messages: 100      # 全局每分钟最多100条消息
    time_window: "1m"
  
  # 错误处理
  error_handling:
    max_retries: 3         # 最大重试次数
    retry_delay: "5s"      # 重试延迟
    log_errors: true       # 记录错误
    notify_on_error: false # 错误时不通知
    error_channel_id: ""   # 错误通知频道（可选）
  
  # 监控设置
  monitoring:
    enabled: true
    stats_interval: "5m"           # 统计间隔
    health_check_interval: "30s"   # 健康检查间隔
    metrics_enabled: true          # 启用指标收集

# 1对多转发实现示例
# 要实现 source → target1, target2, target3，只需要创建多个规则：
#
# forward_rules:
#   rules:
#     - input_channel: "source_channel_id"
#       output_channel: "target1_channel_id"
#       field_mapping_group: "amazon_standard"
#
#     - input_channel: "source_channel_id"      # 相同的输入
#       output_channel: "target2_channel_id"    # 不同的输出
#       field_mapping_group: "amazon_standard"
#
#     - input_channel: "source_channel_id"      # 相同的输入
#       output_channel: "target3_channel_id"    # 不同的输出
#       field_mapping_group: "amazon_standard"

# 使用说明
# 1. 复制此文件为 forward_rules.yaml
# 2. 修改频道ID为实际的Discord频道ID
# 3. 根据需要调整字段映射组
# 4. 设置 enabled: false 可以禁用特定规则
# 5. name字段会自动生成，格式为: forward_{source_suffix}_to_{target_suffix}
# 6. 重新加载配置或重启服务生效

# 配置验证
# - input_channel 和 output_channel 是必需的
# - input_channel 和 output_channel 不能相同
# - field_mapping_group 必须在 field_mapping_groups.yaml 中定义
# - enabled 默认为 true，只有明确设置为 false 才禁用
# - 支持向后兼容的 source_channel_id 和 target_channel_id 字段名
