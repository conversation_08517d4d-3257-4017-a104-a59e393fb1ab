# Zeka Go Discord Bot 配置文件

# 环境设置
environment: development

# Discord 配置
discord:
  token: ""  # 通过环境变量 DISCORD_TOKEN 设置
  client_id: ""  # 通过环境变量 DISCORD_CLIENT_ID 设置
  prefix: "!"
  guild_id: ""  # 通过环境变量 DISCORD_GUILD_ID 设置，开发时用于注册斜杠命令的服务器ID
  owner_id: ""  # 通过环境变量 DISCORD_OWNER_ID 设置
  owners: []  # 可以在配置文件中设置，或通过环境变量

# Redis 缓存配置
redis:
  host: "localhost"  # 通过环境变量 REDIS_HOST 覆盖
  port: 6379  # 通过环境变量 REDIS_PORT 覆盖
  password: ""  # 通过环境变量 REDIS_PASSWORD 设置
  db: 0  # 通过环境变量 REDIS_DB 覆盖
  prefix: "zeka:"

# 数据库配置
database:
  driver: "sqlite"  # 通过环境变量 DB_DRIVER 覆盖
  host: "localhost"  # 通过环境变量 DB_HOST 覆盖
  port: 3306  # 通过环境变量 DB_PORT 覆盖
  username: ""  # 通过环境变量 DB_USERNAME 设置
  password: ""  # 通过环境变量 DB_PASSWORD 设置
  database: "zeka.db"  # 通过环境变量 DB_DATABASE 覆盖
  ssl_mode: "disable"  # 通过环境变量 DB_SSL_MODE 覆盖
  max_idle_conns: 10  # 通过环境变量 DB_MAX_IDLE_CONNS 覆盖
  max_open_conns: 100  # 通过环境变量 DB_MAX_OPEN_CONNS 覆盖
  conn_max_lifetime: "1h"  # 通过环境变量 DB_CONN_MAX_LIFETIME 覆盖
  log_level: "warn"  # 通过环境变量 DB_LOG_LEVEL 覆盖
  enabled: true  # 通过环境变量 DB_ENABLED 覆盖

# RabbitMQ 队列配置
queue:
  url: "amqp://127.0.0.1:5672"  # 通过环境变量 RABBITMQ_URL 覆盖，强制IPv4连接
  prefetch_count: 10
  max_retries: 3
  enable_monitoring: false
  heartbeat_interval: "60s"
  reconnect_delay: "5s"
  connection_timeout: "10s"
  # 优雅降级配置
  enable_graceful_degradation: true  # 启用优雅降级模式
  required_for_startup: false  # 队列服务不是启动的必需条件
  default_exchange:
    name: "zeka.direct"
    type: "direct"
    options:
      durable: true
      auto_delete: false
  delay_exchange:
    name: "zeka.delay"
    type: "x-delayed-message"
    options:
      durable: true
      auto_delete: false
      arguments:
        x-delayed-type: "direct"

# API 服务配置
api:
  port: 8080  # 通过环境变量 API_PORT 覆盖
  host: "0.0.0.0"  # 通过环境变量 API_HOST 覆盖
  api_key: ""  # 通过环境变量 API_KEY 设置
  admin_key: ""  # 通过环境变量 ADMIN_KEY 设置
  enabled: true  # 通过环境变量 API_ENABLED 覆盖

# 日志配置
logger:
  level: "info"  # 通过环境变量 LOG_LEVEL 覆盖
  format: "json"
  discord:
    enabled: false  # 通过环境变量 LOG_TO_DISCORD 覆盖
    channel_id: ""  # 通过环境变量 LOG_CHANNEL_ID 设置
    level: "error"
  file:
    enabled: true
    path: "./logs"
    max_size: 4      # MB
    max_backups: 3
    max_age: 28        # days
    compress: true

# 颜色配置
colors:
  primary: "#3498db"
  success: "#2ecc71"
  warning: "#f39c12"
  error: "#e74c3c"
  info: "#9b59b6"

# 冷却时间配置
cooldowns:
  default: "3s"
  commands:
    ping: "1s"
    help: "5s"

# 状态消息配置
status_messages:
  - type: "Playing"
    content: "与 Discord 用户互动"
  - type: "Listening"
    content: "用户的命令"
  - type: "Watching"
    content: "服务器活动"

