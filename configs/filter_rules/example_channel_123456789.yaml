# 过滤规则配置文件示例
# 频道ID: 123456789012345678
# 基于 docs/enhanced_system_design_v3.md 中的设计规范

filter_rules:
  # 示例过滤规则1：价格过滤
  - id: "filter_123456789_price_001"
    name: "价格范围过滤"
    description: "过滤价格在指定范围内的产品"
    enabled: true
    
    # 关联信息
    channel_id: "123456789012345678"
    forward_rule_name: "amazon_products_forward"
    
    # 过滤条件
    filter_conditions:
      # 关键词过滤
      keywords: ["$", "price", "cost"]
      keyword_mode: "whitelist"
      case_sensitive: false
      
      # 正则表达式过滤
      regex_patterns:
        - name: "price_range"
          pattern: "\\$([1-9][0-9]{1,2}|1000)"  # $10-$1000
          field: "content"
          action: "allow"
          description: "允许价格在$10-$1000范围内的产品"
      
      # 内容长度过滤
      min_length: 10
      max_length: 500
      
      # 字段特定过滤
      field_filters:
        - field_name: "price"
          operator: "regex"
          value: "\\$[1-9][0-9]+"
          action: "allow"
      
      # 逻辑操作符
      logical_operator: "AND"
    
    # 过滤动作
    filter_action:
      type: "allow"
      description: "允许通过的消息"
    
    # 元数据
    metadata:
      created_at: "2025-01-31T16:00:00Z"
      created_by: "admin"
      version: 1
      tags: ["price", "range", "amazon"]
      priority: 10

  # 示例过滤规则2：关键词黑名单
  - id: "filter_123456789_blacklist_001"
    name: "垃圾信息过滤"
    description: "过滤包含垃圾信息关键词的消息"
    enabled: true
    
    channel_id: "123456789012345678"
    
    filter_conditions:
      keywords: ["spam", "scam", "fake", "垃圾", "诈骗", "虚假"]
      keyword_mode: "blacklist"
      case_sensitive: false
      logical_operator: "OR"
    
    filter_action:
      type: "deny"
      description: "拒绝垃圾信息"
    
    metadata:
      created_at: "2025-01-31T16:00:00Z"
      created_by: "admin"
      version: 1
      tags: ["spam", "blacklist"]
      priority: 100  # 高优先级

  # 示例过滤规则3：时间限制过滤
  - id: "filter_123456789_time_001"
    name: "工作时间过滤"
    description: "只在工作时间允许消息通过"
    enabled: false  # 默认禁用
    
    channel_id: "123456789012345678"
    
    filter_conditions:
      time_filters:
        enabled: true
        start_time: "09:00"
        end_time: "18:00"
        weekdays: ["monday", "tuesday", "wednesday", "thursday", "friday"]
        timezone: "UTC"
      logical_operator: "AND"
    
    filter_action:
      type: "allow"
      description: "工作时间内允许"
    
    metadata:
      created_at: "2025-01-31T16:00:00Z"
      created_by: "admin"
      version: 1
      tags: ["time", "business_hours"]
      priority: 5

  # 示例过滤规则4：用户权限过滤
  - id: "filter_123456789_user_001"
    name: "用户权限过滤"
    description: "基于用户角色的过滤规则"
    enabled: true
    
    channel_id: "123456789012345678"
    
    filter_conditions:
      user_filters:
        enabled: true
        allowed_users: ["111111111111111111", "222222222222222222"]
        blocked_users: ["333333333333333333"]
        required_roles: ["444444444444444444"]  # 管理员角色
        filter_bots: true
      logical_operator: "AND"
    
    filter_action:
      type: "allow"
      description: "允许有权限的用户"
    
    metadata:
      created_at: "2025-01-31T16:00:00Z"
      created_by: "admin"
      version: 1
      tags: ["user", "permission", "role"]
      priority: 20

  # 示例过滤规则5：产品特定过滤
  - id: "filter_123456789_product_001"
    name: "特定产品过滤"
    description: "针对特定产品ID的过滤规则"
    enabled: true
    
    channel_id: "123456789012345678"
    product_id: "B08ABC123"  # 特定产品ID
    
    filter_conditions:
      keywords: ["B08ABC123", "特定产品"]
      keyword_mode: "whitelist"
      case_sensitive: false
      
      field_filters:
        - field_name: "product_id"
          operator: "equals"
          value: "B08ABC123"
          action: "allow"
      
      logical_operator: "OR"
    
    filter_action:
      type: "allow"
      description: "允许特定产品"
    
    metadata:
      created_at: "2025-01-31T16:00:00Z"
      created_by: "user_reaction"  # 通过表情反应创建
      version: 1
      tags: ["product", "specific", "reaction"]
      priority: 50
      expires_at: "2025-02-28T16:00:00Z"  # 设置过期时间

# 全局设置
global_settings:
  # 默认设置
  default_action: "allow"
  default_logical_operator: "AND"
  case_sensitive_default: false
  
  # 性能设置
  max_rules_per_channel: 100
  cache_enabled: true
  cache_ttl: "10m"
  
  # 监控设置
  enable_metrics: true
  stats_interval: "5m"
  log_filter_results: false  # 生产环境建议关闭
  
  # 表情反应过滤设置
  reaction_filter:
    enabled: true
    allowed_emojis: ["✅", "❌", "⭐", "🔥", "👍", "👎"]
    required_permissions: ["MANAGE_MESSAGES"]
    silent_skip_no_permission: true
    auto_create_rules: true
    rule_expiration_time: "30d"

# 规则模板（用于快速创建）
rule_templates:
  # 价格过滤模板
  price_filter_template:
    name: "价格过滤模板"
    description: "基于价格范围的过滤模板"
    filter_conditions:
      regex_patterns:
        - name: "price_check"
          pattern: "\\$([0-9,]+\\.?[0-9]*)"
          field: "content"
          action: "allow"
      logical_operator: "AND"
    filter_action:
      type: "allow"
    metadata:
      tags: ["price", "template"]
      priority: 10
  
  # 关键词黑名单模板
  blacklist_template:
    name: "关键词黑名单模板"
    description: "关键词黑名单过滤模板"
    filter_conditions:
      keywords: []  # 需要填入具体关键词
      keyword_mode: "blacklist"
      case_sensitive: false
      logical_operator: "OR"
    filter_action:
      type: "deny"
    metadata:
      tags: ["blacklist", "template"]
      priority: 100
  
  # 用户权限模板
  permission_template:
    name: "用户权限模板"
    description: "基于用户权限的过滤模板"
    filter_conditions:
      user_filters:
        enabled: true
        filter_bots: true
      logical_operator: "AND"
    filter_action:
      type: "allow"
    metadata:
      tags: ["permission", "template"]
      priority: 20

# 使用说明
# 1. 复制模板到filter_rules部分
# 2. 修改id、name、channel_id等基础信息
# 3. 根据需要调整过滤条件和动作
# 4. 设置enabled: true启用规则
# 5. 重新加载配置或重启服务
