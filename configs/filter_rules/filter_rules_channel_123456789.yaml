# 频道过滤规则配置文件（严格遵循设计文档）
# 文件命名格式：filter_rules_channel_{channel_id}.yaml

# 频道信息
channel_info:
  channel_id: "123456789"
  channel_name: "deals"
  guild_id: "987654321"
  created_at: "2024-01-31T14:30:22Z"
  updated_at: "2024-01-31T15:45:33Z"

# 过滤规则列表（极简结构）
filter_rules:
  # 示例1：白名单规则（+前缀）
  - channel: "123456789"
    keyword: "ABC123"
    mode: "whitelist"
    created_at: "2024-01-31T14:30:22Z"
    updated_at: "2024-01-31T15:45:33Z"

  # 示例2：黑名单规则（-前缀）
  - channel: "123456789"
    keyword: "Funko"
    mode: "blacklist"
    created_at: "2024-01-31T14:30:22Z"
    updated_at: "2024-01-31T15:45:33Z"

  # 示例3：另一个白名单规则
  - channel: "123456789"
    keyword: "B08XYZ789"
    mode: "whitelist"
    created_at: "2024-01-31T16:00:00Z"
    updated_at: "2024-01-31T16:00:00Z"

# 使用说明：
# 1. channel: 频道ID（必需）
# 2. keyword: 关键词，在ProductID、Title、URL中搜索（可选）
# 3. mode: whitelist（白名单）或 blacklist（黑名单）
# 4. 唯一标识：Channel + Keyword + Mode 组合
# 5. 添加的规则自动启用，不需要的规则直接删除即可
# 6. 命令格式：
#    /filter add channel:#target keyword:+ABC123  # 白名单
#    /filter add channel:#target keyword:-Funko   # 黑名单
